from django.shortcuts import render

from changelog.models import ChangeLog
from changelog.serializers import ChangeLogDetailSerializer
from rest_framework.views import APIView
from core.authorization import CareCenterAuthentication, HasPermission
from core.resp import make_response

# 更新日志列表
class ChangeLogListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasPermission]
    
    
    def get(self, request):
        queryset = ChangeLog.objects.all().order_by('-release_date')
        serializer = ChangeLogDetailSerializer(queryset, many=True)
        return make_response(code=0, msg="Success", data=serializer.data)