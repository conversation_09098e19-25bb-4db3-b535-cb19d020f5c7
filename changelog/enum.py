from django.db import models


# 变更类型枚举
class ChangeTypeEnum(models.TextChoices):
    # 新功能
    FEATURE = "FEATURE", "新功能"
    # 功能改进
    IMPROVEMENT = "IMPROVEMENT", "功能改进"
    # 错误修复
    BUGFIX = "BUGFIX", "错误修复"
    # 安全更新
    SECURITY = "SECURITY", "安全更新"
    # 性能优化
    PERFORMANCE = "PERFORMANCE", "性能优化"
    # 界面优化
    UI = "UI", "界面优化"
    # 配置变更
    CONFIG = "CONFIG", "配置变更"
    # 其他
    OTHER = "OTHER", "其他"

