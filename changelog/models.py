from django.db import models
from django.utils import timezone

from changelog.enum import ChangeTypeEnum
from core.model import BaseModel


# 更新日志模型
class ChangeLog(BaseModel):
    # 版本号
    version = models.CharField(verbose_name="版本号", max_length=20, unique=True)
    # 更新标题
    title = models.CharField(verbose_name="更新标题", max_length=200)
    # 更新摘要
    summary = models.TextField(verbose_name="更新摘要")
    # 变更类型
    change_type = models.CharField(verbose_name="变更类型", max_length=15, choices=ChangeTypeEnum.choices, default=ChangeTypeEnum.FEATURE)
    # 详细说明
    details = models.TextField(verbose_name="详细说明", blank=True, null=True)
    # 发布时间，自动添加
    release_date = models.DateTimeField(verbose_name="发布时间", auto_now_add=True)
    
    class Meta:
        verbose_name = "更新日志"
        verbose_name_plural = "更新日志"
        
    def __str__(self):
        return f"v{self.version} - {self.title}"
    
