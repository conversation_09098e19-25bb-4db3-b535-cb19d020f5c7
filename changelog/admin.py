from django.contrib import admin
from django.utils.html import format_html

from changelog.models import ChangeLog
from changelog.enum import ChangeTypeEnum


@admin.register(ChangeLog)
class ChangeLogAdmin(admin.ModelAdmin):
    # 列表页显示字段
    list_display = [
        'version_display',
        'title',
        'change_type_display',
        'summary_preview',
        'release_date_display',
        'created_at_display'
    ]

    # 列表页过滤器
    list_filter = [
        'change_type',
        'release_date',
        'created_at',
    ]

    # 搜索字段
    search_fields = [
        'version',
        'title',
        'summary',
        'details'
    ]

    # 排序
    ordering = ['-release_date', '-created_at']

    # 详情页字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('version', 'title', 'change_type')
        }),
        ('内容信息', {
            'fields': ('summary', 'details'),
            'classes': ('wide',)
        }),
        ('时间信息', {
            'fields': ('release_date',),
            'classes': ('collapse',)
        }),
    )

    # 只读字段
    readonly_fields = ['release_date', 'created_at', 'updated_at']

    # 每页显示数量
    list_per_page = 20

    # 启用日期层次导航
    date_hierarchy = 'release_date'

    # 自定义显示方法
    def version_display(self, obj):
        """版本号显示"""
        return format_html(
            '<span style="background: #007cba; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-weight: bold; font-size: 12px;">{}</span>',
            obj.version
        )
    version_display.short_description = '版本号'
    version_display.admin_order_field = 'version'

    def change_type_display(self, obj):
        """变更类型显示"""
        type_colors = {
            ChangeTypeEnum.FEATURE: '#28a745',      # 绿色 - 新功能
            ChangeTypeEnum.IMPROVEMENT: '#17a2b8',  # 蓝色 - 功能改进
            ChangeTypeEnum.BUGFIX: '#dc3545',       # 红色 - 错误修复
            ChangeTypeEnum.SECURITY: '#fd7e14',     # 橙色 - 安全更新
            ChangeTypeEnum.PERFORMANCE: '#6f42c1',  # 紫色 - 性能优化
            ChangeTypeEnum.UI: '#e83e8c',           # 粉色 - 界面优化
            ChangeTypeEnum.CONFIG: '#6c757d',       # 灰色 - 配置变更
            ChangeTypeEnum.OTHER: '#20c997',        # 青色 - 其他
        }
        color = type_colors.get(obj.change_type, '#6c757d')
        return format_html(
            '<span style="background: {}; color: white; padding: 2px 6px; '
            'border-radius: 8px; font-size: 11px;">{}</span>',
            color,
            obj.get_change_type_display()
        )
    change_type_display.short_description = '变更类型'
    change_type_display.admin_order_field = 'change_type'

    def summary_preview(self, obj):
        """摘要预览"""
        if len(obj.summary) > 50:
            return format_html(
                '<span title="{}">{}</span>',
                obj.summary,
                obj.summary[:50] + '...'
            )
        return obj.summary
    summary_preview.short_description = '更新摘要'
    summary_preview.admin_order_field = 'summary'

    def release_date_display(self, obj):
        """发布时间显示"""
        return format_html(
            '<span style="color: #666;">{}</span>',
            obj.release_date.strftime('%Y-%m-%d %H:%M')
        )
    release_date_display.short_description = '发布时间'
    release_date_display.admin_order_field = 'release_date'

    def created_at_display(self, obj):
        """创建时间显示"""
        return format_html(
            '<span style="color: #999; font-size: 12px;">{}</span>',
            obj.created_at.strftime('%Y-%m-%d %H:%M')
        )
    created_at_display.short_description = '创建时间'
    created_at_display.admin_order_field = 'created_at'

    # 自定义操作
    actions = ['mark_as_feature', 'mark_as_bugfix']

    def mark_as_feature(self, request, queryset):
        """标记为新功能"""
        updated = queryset.update(change_type=ChangeTypeEnum.FEATURE)
        self.message_user(request, f'成功将 {updated} 条记录标记为新功能')
    mark_as_feature.short_description = '标记为新功能'

    def mark_as_bugfix(self, request, queryset):
        """标记为错误修复"""
        updated = queryset.update(change_type=ChangeTypeEnum.BUGFIX)
        self.message_user(request, f'成功将 {updated} 条记录标记为错误修复')
    mark_as_bugfix.short_description = '标记为错误修复'