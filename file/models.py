import hashlib
import os
from datetime import datetime

from django.db import models

from core.generate_hashid import generate_resource_uuid
from maternity_center.models import MaternityCenter
from user.models import Staff


def generate_secure_filename(original_filename):
    name_without_ext = os.path.splitext(original_filename)[0]
    ext = os.path.splitext(original_filename)[1]
    date_str = datetime.now().strftime("%Y%m%d")
    hash_input = f"{original_filename}_{datetime.now().isoformat()}_{os.urandom(8).hex()}"
    file_hash = hashlib.md5(hash_input.encode()).hexdigest()[:8]
    secure_filename = f"{name_without_ext}_{date_str}_{file_hash}{ext}"
    return secure_filename

def contract_file_upload_path(instance, filename):
    # 生成合同文件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"maternity/contracts/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def discharge_records_file_upload_path(instance, filename):
    # 生成出院记录文件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"maternity/discharge_records/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def medical_referral_file_upload_path(instance, filename):
    # 生成医疗转诊文件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"medical/referrals/out/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def medical_referral_return_file_upload_path(instance, filename):
    # 生成医疗转诊返回文件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"medical/referrals/return/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def infection_management_file_upload_path(instance, filename):
    # 生成院感管理制度文件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"org/infection_management/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def disinfect_file_upload_path(instance, filename):
    # 消毒规范文档
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"org/disinfect/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def staff_contract_file_upload_path(instance, filename):
    # 员工合同文件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"staff/contracts/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def staff_qualification_certificate_upload_path(instance, filename):
    # 员工资质证书文件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"staff/qualification_certificates/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def staff_training_record_attachment_upload_path(instance, filename):
    # 员工培训记录附件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"staff/training_records/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def staff_assessment_record_attachment_upload_path(instance, filename):
    # 员工考核记录附件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"staff/assessment_records/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

def staff_health_check_record_attachment_upload_path(instance, filename):
    # 员工健康检查记录附件路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"staff/health_check_records/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

# 活动封面上传路径
def activity_cover_file_upload_path(instance, filename):
    # 活动封面上传路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"activity/covers/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"


# 设备图片上传路径
def equipment_image_upload_path(instance, filename):
    # 设备图片上传路径
    secure_name = generate_secure_filename(filename)
    date_path = datetime.now().strftime('%Y-%m')
    return f"equipment/images/{instance.maternity_center.file_identifier}/{date_path}/{secure_name}"

# 合同文件模型
class MaternityContractFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 合同文件
    file = models.FileField(upload_to=contract_file_upload_path, verbose_name="合同文件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)

    class Meta:
        verbose_name = "合同文件"
        verbose_name_plural = "合同文件"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None


# 出院记录文件模型
class MaternityDischargeRecordsFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 出院记录文件
    file = models.FileField(upload_to=discharge_records_file_upload_path, verbose_name="出院记录文件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "出院记录文件"
        verbose_name_plural = "出院记录文件"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        

# 院内转诊相关资料文件模型
class MedicalReferralFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 院内转诊相关资料文件
    file = models.FileField(upload_to=medical_referral_file_upload_path, verbose_name="院内转诊相关资料文件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "院内转诊相关资料文件"
        verbose_name_plural = "院内转诊相关资料文件"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
        
# 院内转诊返回相关资料文件模型
class MedicalReferralReturnFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 院内转诊返回相关资料文件
    file = models.FileField(upload_to=medical_referral_return_file_upload_path, verbose_name="院内转诊返回相关资料文件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "院内转诊返回相关资料文件"
        verbose_name_plural = "院内转诊返回相关资料文件"

    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
    @classmethod
    def check_rids(cls, rids,maternity_center):
        existing_files = cls.objects.filter(
            rid__in=rids,
            maternity_center=maternity_center
        )
        existing_rids = set(existing_files.values_list('rid', flat=True))
        input_rids = set(rids)
        missing_rids = input_rids - existing_rids
        if missing_rids:
            return False
        return True

# 院感管理制度文档
class InfectionManagementFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 院感管理制度文档
    file = models.FileField(upload_to=infection_management_file_upload_path, verbose_name="院感管理制度文档", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "院感管理制度文档"
        verbose_name_plural = "院感管理制度文档"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None

# 消毒规范文档
class DisinfectFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 消毒规范文档
    file = models.FileField(upload_to=disinfect_file_upload_path, verbose_name="消毒规范文档库", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "消毒规范文档"
        verbose_name_plural = "消毒规范文档"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
    

# 员工合同文件模型
class StaffContractFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 员工合同文件
    file = models.FileField(upload_to=staff_contract_file_upload_path, verbose_name="员工合同文件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工合同文件"
        verbose_name_plural = "员工合同文件"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        

# 员工资质证书模型
class StaffQualificationCertificateFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 证书文件
    file = models.FileField(upload_to=staff_qualification_certificate_upload_path, verbose_name="员工资质证书", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工资质证书"
        verbose_name_plural = "员工资质证书"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None

# 员工培训记录附件模型
class StaffTrainingRecordAttachment(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 附件
    file = models.FileField(upload_to=staff_training_record_attachment_upload_path, verbose_name="附件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True,related_name='creator_staff_training_record_attachments')
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工培训记录附件"
        verbose_name_plural = "员工培训记录附件"
        
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
    @classmethod
    def check_rids(cls, rids,maternity_center):
        print(rids)
        existing_files = cls.objects.filter(
            rid__in=rids,
            maternity_center=maternity_center
        )
        existing_rids = set(existing_files.values_list('rid', flat=True))
        input_rids = set(rids)
        missing_rids = input_rids - existing_rids
        if missing_rids:
            return False
        return True
        


# 员工考核记录附件模型
class StaffAssessmentRecordAttachment(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 附件
    file = models.FileField(upload_to=staff_assessment_record_attachment_upload_path, verbose_name="附件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True,related_name='creator_staff_assessment_record_attachments')
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工培训记录附件"
        verbose_name_plural = "员工培训记录附件"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
    @classmethod
    def check_rids(cls, rids,maternity_center):
        print(rids)
        existing_files = cls.objects.filter(
            rid__in=rids,
            maternity_center=maternity_center
        )
        existing_rids = set(existing_files.values_list('rid', flat=True))
        input_rids = set(rids)
        missing_rids = input_rids - existing_rids
        if missing_rids:
            return False
        return True
        
        
# 员工健康检查记录附件模型
class StaffHealthCheckRecordAttachment(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 附件
    file = models.FileField(upload_to=staff_health_check_record_attachment_upload_path, verbose_name="附件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True,related_name='creator_staff_health_check_record_attachments')
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工培训记录附件"
        verbose_name_plural = "员工培训记录附件"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
    @classmethod
    def check_rids(cls, rids,maternity_center):
        print(rids)
        existing_files = cls.objects.filter(
            rid__in=rids,
            maternity_center=maternity_center
        )
        existing_rids = set(existing_files.values_list('rid', flat=True))
        input_rids = set(rids)
        missing_rids = input_rids - existing_rids
        if missing_rids:
            return False
        return True
                


# 活动封面上传模型
class ActivityCoverFile(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 活动封面文件
    file = models.FileField(upload_to=activity_cover_file_upload_path, verbose_name="活动封面文件", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "活动封面上传"
        verbose_name_plural = "活动封面上传"
        
    @classmethod
    def get_activity_cover_file_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
    
# 设备图片上传模型
class EquipmentImage(models.Model):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 设备图片
    file = models.FileField(upload_to=equipment_image_upload_path, verbose_name="设备图片", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "设备图片上传"
        verbose_name_plural = "设备图片上传"
        
    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None