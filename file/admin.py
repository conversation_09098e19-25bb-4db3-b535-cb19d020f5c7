from django.contrib import admin

from .models import (
    ActivityCoverFile,
    MaternityContractFile,
    MaternityDischargeRecordsFile,
    MedicalReferralFile,
    InfectionManagementFile,
    DisinfectFile,
    StaffContractFile
)

    
    


# 活动封面上传管理
@admin.register(ActivityCoverFile)
class ActivityCoverFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'file', 'maternity_center', 'created_at')
    list_filter = ('maternity_center', 'created_at')
    search_fields = ('maternity_center__name',) 
    ordering = ('-created_at',)

    # def get_queryset(self, request):
    #     return super().get_queryset(request).select_related('maternity_center')


# 合同文件管理
@admin.register(MaternityContractFile)
class MaternityContractFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'file', 'maternity_center', 'created_at')
    list_filter = ('maternity_center', 'created_at')
    search_fields = ('maternity_center__name',)
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity_center')


# 出院记录文件管理
@admin.register(MaternityDischargeRecordsFile)
class MaternityDischargeRecordsFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'file', 'maternity_center', 'created_at')
    list_filter = ('maternity_center', 'created_at')
    search_fields = ('maternity_center__name',)
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity_center')


# 院内转诊相关资料文件管理
@admin.register(MedicalReferralFile)
class MedicalReferralFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'file', 'maternity_center', 'created_at')
    list_filter = ('maternity_center', 'created_at')
    search_fields = ('maternity_center__name',)
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity_center')


# 院感管理制度文档管理
@admin.register(InfectionManagementFile)
class InfectionManagementFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'file', 'maternity_center', 'created_at')
    list_filter = ('maternity_center', 'created_at')
    search_fields = ('maternity_center__name',)
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity_center')


# 消毒规范文档管理
@admin.register(DisinfectFile)
class DisinfectFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'file', 'maternity_center', 'created_at')
    list_filter = ('maternity_center', 'created_at')
    search_fields = ('maternity_center__name',)
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity_center')



