from django.urls import path

from .views import ContractFileUploadView, DischargeRecordsFileUploadView, EquipmentImageUploadView, \
    MedicalReferralFileUploadView, \
    InfectionManagementFileUploadView, DisinfectFileUploadView, StaffContractFileUploadView, \
    StaffQualificationCertificateUploadView, \
    StaffTrainingRecordAttachmentUploadView, StaffAssessmentRecordAttachmentUploadView, \
    StaffHealthCheckRecordAttachmentUploadView, MedicalReferralReturnFileUploadView, ActivityCoverFileUploadView

urlpatterns = [
    # 产妇入住合同上传
    path('maternity-contract/upload/', ContractFileUploadView.as_view(), name='contract-file-upload'),
    # 产妇出院记录上传
    path('maternity-discharge-records/upload/', DischargeRecordsFileUploadView.as_view(), name='discharge-records-file-upload'),
    # 院内转诊相关资料上传
    path('medical-referral/file/upload/', MedicalReferralFileUploadView.as_view(), name='medical-referral-file-upload'),
    # 转诊返回出院小结上传
    path('medical-referral-return/file/upload/', MedicalReferralReturnFileUploadView.as_view(), name='medical-referral-return-file-upload'),
    # 院感管理制度上传
    path('infection-management/file/upload/', InfectionManagementFileUploadView.as_view(), name='infection-management-file-upload'),
    #  消毒规范上传
    path('disinfect/file/upload/',DisinfectFileUploadView.as_view(),name = 'disinfect-file-upload'),
    # 员工合同上传
    path('staff-contract/upload/', StaffContractFileUploadView.as_view(), name='staff-contract-file-upload'),
    # 员工资质证书上传
    path('sqc/upload/', StaffQualificationCertificateUploadView.as_view(), name='staff-qualification-certificate-upload'),
    # 员工培训记录附件上传
    path('stra/upload/', StaffTrainingRecordAttachmentUploadView.as_view(), name='staff-training-record-attachment-upload'),
    # 员工考核记录附件上传
    path('sar/upload/', StaffAssessmentRecordAttachmentUploadView.as_view(), name='staff-assessment-record-attachment-upload'),
    # 员工健康检查记录附件上传
    path('shr/upload/', StaffHealthCheckRecordAttachmentUploadView.as_view(), name='staff-health-check-record-attachment-upload'),
    # 活动封面上传
    path('activity/cover/upload/', ActivityCoverFileUploadView.as_view(), name='activity-cover-file-upload'),
    # 设备图片上传
    path('equipment/image/upload/', EquipmentImageUploadView.as_view(), name='equipment-image-upload'),
]