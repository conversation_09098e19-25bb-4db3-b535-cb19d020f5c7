from django.conf import settings
from rest_framework import serializers

from .models import StaffContractFile


class StaffContractFileSerializer(serializers.ModelSerializer):
    # 文件完整路径
    url = serializers.SerializerMethodField()
    staff_number = serializers.CharField(source='staff.staff_number', read_only=True)
    staff_name = serializers.CharField(source='staff.name', read_only=True)
    
    class Meta:
        model = StaffContractFile
        fields = [
            'rid',
            'url',
            'staff_number',
            'staff_name',
            'contract_number',
            'contract_validity_period',
        ]
    
    def get_url(self, obj):
        if obj.contract_file:
            return obj.contract_file.url
        return None
    


