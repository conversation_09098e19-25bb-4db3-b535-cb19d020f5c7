from datetime import datetime

from django.conf import settings
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.logs import AuditLogCreator, create_file_upload_audit_log
from core.resp import make_response
from file.models import ActivityCoverFile, EquipmentImage, MaternityContractFile, MaternityDischargeRecordsFile, \
    InfectionManagementFile, MedicalReferralFile, MedicalReferralReturnFile, StaffAssessmentRecordAttachment, \
    StaffContractFile, StaffHealthCheckRecordAttachment, \
    DisinfectFile, StaffQualificationCertificateFile, StaffTrainingRecordAttachment
from file.serializers import StaffContractFileSerializer
from permissions.enum import PermissionEnum
from user.models import Staff


class FileUploadView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.FILE_UPLOAD
    


class ContractFileUploadView(FileUploadView):
    
    def post(self, request):
        file = request.FILES.get('contract_file')
        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        contract_file = MaternityContractFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        resData = {
            "rid": contract_file.rid,
            "url": contract_file.file.url if contract_file.file else ''
        }
        
        AuditLogCreator.create_file_upload_audit_log(request,"入住管理","上传了入住合同文件",{'model': 'MC', 'rid': contract_file.rid, 'field': 'file'})
        
        return make_response(code=0, msg="合同文件上传成功", data=resData)

class DischargeRecordsFileUploadView(FileUploadView):
    
    def post(self, request):
        
        file = request.FILES.get('discharge_records_file')
        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        discharge_records_file = MaternityDischargeRecordsFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        
        resData = {
            "rid": discharge_records_file.rid,
            "url": discharge_records_file.file.url if discharge_records_file.file else ''
        }
        
        AuditLogCreator.create_file_upload_audit_log(request,"入住管理","上传了出院记录文件",{'model': 'MDR', 'rid': discharge_records_file.rid, 'field': 'file'})

        return make_response(code=0, msg="出院记录文件上传成功", data=resData)
    

class MedicalReferralFileUploadView(FileUploadView):
    
    def post(self, request):
        file = request.FILES.get('medical_referral_file')
        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        medical_referral_file = MedicalReferralFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        
        resData = {
            "rid": medical_referral_file.rid,
            "url": medical_referral_file.file.url if medical_referral_file.file else ''
        }
        
        AuditLogCreator.create_file_upload_audit_log(request,"院内转诊","上传了院内转诊相关资料",{'model': 'MCR', 'rid': medical_referral_file.rid, 'field': 'file'})
        
        return make_response(code=0, msg="院内转诊相关资料上传成功", data=resData)


class MedicalReferralReturnFileUploadView(FileUploadView):
    
    def post(self, request):
        
        file = request.FILES.get('medical_referral_return_file')
        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        medical_referral_return_file = MedicalReferralReturnFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        
        resData = {
            "rid": medical_referral_return_file.rid,
            "url": medical_referral_return_file.file.url if medical_referral_return_file.file else ''
        }
        
        AuditLogCreator.create_file_upload_audit_log(request,"院内转诊","上传了院内转诊返回相关资料",{'model': 'MCB', 'rid': medical_referral_return_file.rid, 'field': 'file'})
        
        return make_response(code=0, msg="院内转诊返回相关资料上传成功", data=resData)


class InfectionManagementFileUploadView(FileUploadView):

    def post(self, request):
        
        file = request.FILES.get('infection_management_file')
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        infection_management_file = InfectionManagementFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        resData = {
            "rid": infection_management_file.rid,
            "url": infection_management_file.file.url if infection_management_file.file else ''
        }
        
        AuditLogCreator.create_file_upload_audit_log(request,"院感管理制度","上传了院感管理制度",{'model': 'IFM', 'rid': infection_management_file.rid, 'field': 'file'})
        
        return make_response(code=0, msg="院感管理制度上传成功", data=resData)

#  DisinfectFile文件上传
class DisinfectFileUploadView(FileUploadView):
    def post(self, request):
        
        file = request.FILES.get('disinfect_file')
        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        disinfect_file = DisinfectFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        
        resData = {
            "rid": disinfect_file.rid,
            "url": disinfect_file.file.url if disinfect_file.file else ''
        }
        
        AuditLogCreator.create_file_upload_audit_log(request,"消毒规范","上传了消毒规范",{'model': 'DIF', 'rid': disinfect_file.rid, 'field': 'file'})
        
        return make_response(code=0, msg="消毒规范上传成功", data=resData)

# 员工合同上传
class StaffContractFileUploadView(FileUploadView):
    
    def post(self, request):
        
        file = request.FILES.get('staff_contract_file')
            
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        
        scf = StaffContractFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        
        resData = {
            "rid": scf.rid,
            "url": scf.file.url
        }
        
        AuditLogCreator.create_file_upload_audit_log(request,"员工管理","上传了员工合同文件",{'model': 'SCF', 'rid': scf.rid, 'field': 'file'})
        
        return make_response(code=0, msg="员工合同上传成功", data=resData)

# 员工资质证书上传
class StaffQualificationCertificateUploadView(FileUploadView):
    
    def post(self, request):
        file = request.FILES.get('staff_qualification_certificate_file')

        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")

        
        sqc = StaffQualificationCertificateFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )

        resData = {
            "rid": sqc.rid,
            "url": sqc.file.url
        }
        
        AuditLogCreator.create_file_upload_audit_log(request,"员工管理","上传了员工资质证书",{'model': 'SQC', 'rid': sqc.rid, 'field': 'file'})
        
        return make_response(code=0, msg="员工资质证书上传成功", data=resData)
    
    
# 员工培训记录附件上传
class StaffTrainingRecordAttachmentUploadView(FileUploadView):
    
    def post(self, request):
        
        file = request.FILES.get('staff_training_record_attachment')
        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
    

        staff_training_record_attachment = StaffTrainingRecordAttachment.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        if staff_training_record_attachment.file:
            
            resData = {
                "rid": staff_training_record_attachment.rid,
                "url": staff_training_record_attachment.file.url if staff_training_record_attachment.file else ''
            }
            
            AuditLogCreator.create_file_upload_audit_log(request,"员工管理","上传了员工培训记录附件",{'model': 'STR', 'rid': staff_training_record_attachment.rid, 'field': 'file'})
            
            return make_response(code=0, msg="员工培训记录附件上传成功", data=resData)
        return make_response(code=-1, msg="员工培训记录附件上传失败")
    

# 员工考核记录附件上传
class StaffAssessmentRecordAttachmentUploadView(FileUploadView):
    
    def post(self, request):
        
        file = request.FILES.get('staff_assessment_record_attachment')
            
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        staff_assessment_record_attachment = StaffAssessmentRecordAttachment.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        if staff_assessment_record_attachment.file:
            resData = {
                "rid": staff_assessment_record_attachment.rid,
                "url": staff_assessment_record_attachment.file.url if staff_assessment_record_attachment.file else ''
            }
            
            AuditLogCreator.create_file_upload_audit_log(request,"员工管理","上传了员工考核记录附件",{'model': 'SAR', 'rid': staff_assessment_record_attachment.rid, 'field': 'file'})
            
            return make_response(code=0, msg="员工考核记录附件上传成功", data=resData)
        return make_response(code=-1, msg="员工考核记录附件上传失败")
    

# 员工健康检查记录附件上传
class StaffHealthCheckRecordAttachmentUploadView(FileUploadView):
    
    def post(self, request):
        
        file = request.FILES.get('staff_health_check_record_attachment')
        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        staff_health_check_record_attachment = StaffHealthCheckRecordAttachment.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        if staff_health_check_record_attachment.file:
            resData = {
                "rid": staff_health_check_record_attachment.rid,
                "url": staff_health_check_record_attachment.file.url if staff_health_check_record_attachment.file else ''
            }

            AuditLogCreator.create_file_upload_audit_log(request,"员工管理","上传了员工健康检查记录附件",{'model': 'SHR', 'rid': staff_health_check_record_attachment.rid, 'field': 'file'})
            
            return make_response(code=0, msg="员工健康检查记录附件上传成功", data=resData)
        return make_response(code=-1, msg="员工健康检查记录附件上传失败")


# 活动封面上传
class ActivityCoverFileUploadView(FileUploadView):
    
    def post(self, request):
        
        file = request.FILES.get('activity_cover_file')
        
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        activity_cover_file = ActivityCoverFile.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user
        )
        
        if activity_cover_file.file:
            resData = {
                "rid": activity_cover_file.rid,
                "url": activity_cover_file.file.url if activity_cover_file.file else ''
            }
            
            AuditLogCreator.create_file_upload_audit_log(request,"活动管理","上传了活动封面",{'model': 'ACF', 'rid': activity_cover_file.rid, 'field': 'file'})
            
            return make_response(code=0, msg="活动封面上传成功", data=resData)
        return make_response(code=-1, msg="活动封面上传失败")
    
    
# 设备图片上传
class EquipmentImageUploadView(FileUploadView):
    
    def post(self, request):
        
        file = request.FILES.get('equipment_image')
        if not file:
            return make_response(code=-1, msg="未提供上传文件")
        
        equipment_image = EquipmentImage.objects.create(
            file=file,
            maternity_center=request.user.maternity_center,
            creator=request.user)
        
        if equipment_image.file:
            resData = {
                "rid": equipment_image.rid,
                "url": equipment_image.file.url if equipment_image.file else ''
            }

            AuditLogCreator.create_file_upload_audit_log(request,"设备管理","上传了设备图片",{'model': 'EI', 'rid': equipment_image.rid, 'field': 'file'})
            
            return make_response(code=0, msg="设备图片上传成功", data=resData)
        return make_response(code=-1, msg="活动封面上传失败")