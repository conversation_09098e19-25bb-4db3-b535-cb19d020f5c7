from django.contrib import admin
from django.utils.html import format_html

from .models import *


# Register your models here.

@admin.register(MaternityCenter)
class MaternityCenterAdmin(admin.ModelAdmin):
    list_display = ('cid', 'name', 'address', 'contact', 'capacity', 'status_display', 'created_at')
    search_fields = ('name', 'cid', 'address', 'contact')
    list_filter = ('status', 'created_at')
    readonly_fields = ('file_identifier', 'cid', 'created_at', 'updated_at')
    list_per_page = 20
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'address', 'contact', 'capacity')
        }),
        ('状态信息', {
            'fields': ('status',)
        }),
        ('系统信息', {
            'fields': ('cid', 'file_identifier', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def status_display(self, obj):
        status_icons = {
            1: '<span style="color: #28a745;">🟢</span> ',
            2: '<span style="color: #dc3545;">🔴</span> ', 
            3: '<span style="color: #6c757d;">⚪</span> ', 
        }
        return format_html(status_icons.get(obj.status, obj.get_status_display()))
    status_display.short_description = '状态'
    status_display.admin_order_field = 'status'
    

# 月子中心轮播图
@admin.register(MaternityCenterCarouselFile)
class MaternityCenterCarouselFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'maternity_center', 'file_thumbnail', 'created_at')
    search_fields = ('maternity_center__name',)
    list_filter = ('maternity_center', 'created_at')
    
    def file_thumbnail(self, obj):
        if obj.file:
            return format_html('<img src="{}" style="width: 50px; height: 50px; object-fit: cover;" />', obj.file.url)
        return "无图片"
    file_thumbnail.short_description = '轮播图'
    
    
# 月子中心发展历程
@admin.register(MaternityCenterDevelopmentHistory)
class MaternityCenterDevelopmentHistoryAdmin(admin.ModelAdmin):
    list_display = ('rid', 'maternity_center_name', 'year', 'short_description', 'created_at')
    search_fields = ('maternity_center__name', 'rid', 'description')
    list_filter = ('maternity_center', 'year', 'created_at')
    list_per_page = 20
    ordering = ('maternity_center', '-year')

    fieldsets = (
        ('基本信息', {
            'fields': ('maternity_center', 'year', 'description')
        }),
        ('系统信息', {
            'fields': ('rid', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('rid', 'created_at', 'updated_at')

    def maternity_center_name(self, obj):
        return obj.maternity_center.name if obj.maternity_center else '-'
    maternity_center_name.short_description = '月子中心'
    maternity_center_name.admin_order_field = 'maternity_center__name'

    def short_description(self, obj):
        if len(obj.description) > 50:
            return obj.description[:50] + '...'
        return obj.description
    short_description.short_description = '发展描述'
    short_description.admin_order_field = 'description'

    
# 月子中心品牌介绍
@admin.register(MaternityCenterBrandIntroduction)
class MaternityCenterBrandIntroductionAdmin(admin.ModelAdmin):
    list_display = ('id', 'maternity_center', 'image_thumbnail', 'short_brand_introduction', 'short_brand_advantage', 'short_brand_vision', 'short_honor_qualification', 'created_at')
    search_fields = ('maternity_center__name',)
    list_filter = ('maternity_center', 'created_at')

    def image_thumbnail(self, obj):
        if obj.introduction_image:
            return format_html('<img src="{}" style="width: 50px; height: 50px; object-fit: cover;" />', obj.introduction_image.url)
        return "无图片"
    image_thumbnail.short_description = '介绍图'

    def short_brand_introduction(self, obj):
        if len(obj.brand_introduction) > 50:
            return obj.brand_introduction[:50] + '...'
        return obj.brand_introduction
    short_brand_introduction.short_description = '品牌简介'

    def short_brand_advantage(self, obj):
        if len(obj.brand_advantage) > 50:
            return obj.brand_advantage[:50] + '...'
        return obj.brand_advantage
    short_brand_advantage.short_description = '品牌优势'

    def short_brand_vision(self, obj):
        if len(obj.brand_vision) > 50:
            return obj.brand_vision[:50] + '...'
        return obj.brand_vision
    short_brand_vision.short_description = '品牌愿景'

    def short_honor_qualification(self, obj):
        if len(obj.honor_qualification) > 50:
            return obj.honor_qualification[:50] + '...'
        return obj.honor_qualification
    short_honor_qualification.short_description = '荣誉资质'
    
@admin.register(MaternityCenterContactUs)
class MaternityCenterContactUsAdmin(admin.ModelAdmin):
    list_display = ('id', 'maternity_center', 'contact_image_thumbnail', 'contact_phone', 'contact_email', 'address', 'business_hours', 'created_at')
    search_fields = ('maternity_center__name',)
    list_filter = ('maternity_center', 'created_at')

    def contact_image_thumbnail(self, obj):
        if obj.contact_image:
            return format_html('<img src="{}" style="width: 50px; height: 50px; object-fit: cover;" />', obj.contact_image.url)
        return "无图片"
    contact_image_thumbnail.short_description = '联系图片'