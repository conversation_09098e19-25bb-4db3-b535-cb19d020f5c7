import hashlib
import os
import uuid
from datetime import datetime

from django.db import models

from core.generate_hashid import generate_maternity_center_cid, generate_resource_uuid
from core.model import BaseModel
from maternity_center.enum import MaternityCenterStatusEnum


def generate_secure_filename(original_filename):
    ext = os.path.splitext(original_filename)[1]
    hash_input = f"{original_filename}_{datetime.now().isoformat()}_{os.urandom(8).hex()}"
    file_hash = hashlib.md5(hash_input.encode()).hexdigest()[:17]
    secure_filename = f"{file_hash}{ext}"
    return secure_filename

def carousel_file_upload_path(instance, filename):
    secure_name = generate_secure_filename(filename)
    return f"center/carousel/{instance.maternity_center.file_identifier}/{secure_name}"

def introduction_image_upload_path(instance, filename):
    secure_name = generate_secure_filename(filename)
    return f"center/intro/{instance.maternity_center.file_identifier}/{secure_name}"


def contact_us_upload_path(instance, filename):
    secure_name = generate_secure_filename(filename)
    return f"center/contact/{instance.maternity_center.file_identifier}/{secure_name}"


# 月子中心模型
class MaternityCenter(BaseModel):
    # 名称
    name = models.CharField(max_length=200, verbose_name="名称", help_text="月子中心名称")
    # 地址
    address = models.CharField(max_length=500, verbose_name="地址", help_text="月子中心详细地址")
    # 联系人
    contact = models.CharField(max_length=100, verbose_name="联系人", help_text="负责人联系方式")
    # 容量
    capacity = models.IntegerField(verbose_name="容量", help_text="可容纳的产妇数量")
    # 文件路径标识符
    file_identifier = models.CharField(max_length=32,blank=True,verbose_name="文件路径标识符",help_text="用于文件存储路径的唯一标识符")
    # 状态
    status = models.IntegerField(
        choices=MaternityCenterStatusEnum.choices, 
        default=MaternityCenterStatusEnum.OPEN, 
        verbose_name="状态",
        help_text="月子中心当前状态"
    )
    # 编号
    cid = models.CharField(max_length=100,verbose_name="编号",help_text="月子中心编号",default=generate_maternity_center_cid)
    
    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "月子中心"
        verbose_name_plural = "月子中心"
        ordering = ['-created_at']
            
    def save(self, *args, **kwargs):
        if not self.file_identifier:
            self.file_identifier = self.generate_file_identifier()
        super().save(*args, **kwargs)
    
    def generate_file_identifier(self):
        return str(uuid.uuid4()).replace('-', '')[:17]
    
    @classmethod
    def get_maternity_center_by_cid(cls, cid):
        try:
            return cls.objects.get(cid=cid)
        except cls.DoesNotExist:
            return None
    

        

# 月子中心轮播图模型
class MaternityCenterCarouselFile(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True,related_name='carousel_files')
    # 轮播图
    file = models.FileField(upload_to=carousel_file_upload_path, verbose_name="轮播图", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # rid
    rid = models.CharField(max_length=100,verbose_name="rid",help_text="轮播图rid",default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "月子中心轮播图"
        verbose_name_plural = "月子中心轮播图"

    @classmethod
    def get_carouse_list(cls, maternity_center):
        query_set = cls.objects.filter(maternity_center=maternity_center).order_by('-created_at')
        
        return query_set,len(query_set)


# 月子中心发展历程
class MaternityCenterDevelopmentHistory(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True,related_name='development_histories')
    # 年份
    year = models.PositiveIntegerField(verbose_name="年份", help_text="月子中心发展历程年份")
    # 发展文本
    description = models.TextField(verbose_name="发展文本", help_text="月子中心发展文本",default="")
    # rid
    rid = models.CharField(max_length=100,verbose_name="rid",help_text="发展历程rid",default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "月子中心发展历程"
        verbose_name_plural = "月子中心发展历程"
        ordering = ['year']
        
    @classmethod
    def get_development_history_list_by_maternity_center(cls,maternity_center):
        return cls.objects.filter(maternity_center=maternity_center).order_by('year')
    
    @classmethod
    def get_development_history_by_rid(cls,rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
   


# 月子中心品牌介绍
class MaternityCenterBrandIntroduction(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True,related_name='brand_introductions')
    # 介绍图
    introduction_image = models.FileField(upload_to=introduction_image_upload_path, verbose_name="介绍图", blank=True, null=True)
    # 品牌简介
    brand_introduction = models.TextField(verbose_name="品牌简介", help_text="月子中心品牌简介",default="")
    # 品牌优势
    brand_advantage = models.TextField(verbose_name="品牌优势", help_text="月子中心品牌优势",default="")
    # 品牌愿景
    brand_vision = models.TextField(verbose_name="品牌愿景", help_text="月子中心品牌愿景",default="")
    # 荣誉资质
    honor_qualification = models.TextField(verbose_name="荣誉资质", help_text="月子中心荣誉资质",default="")
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # rid
    rid = models.CharField(max_length=100,verbose_name="rid",help_text="品牌介绍rid",default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "月子中心品牌介绍"
        verbose_name_plural = "月子中心品牌介绍"
        
    @classmethod
    def get_brand_introduction_by_maternity_center(cls,maternity_center):
        try:
            return cls.objects.get(maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
# 月子中心联系我们
class MaternityCenterContactUs(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name='contact_us')
    # 联系图
    contact_image = models.FileField(upload_to=contact_us_upload_path, verbose_name="联系图", )
    # 联系电话
    contact_phone = models.CharField(max_length=100, verbose_name="联系电话", help_text="月子中心联系电话")
    # 联系邮箱
    contact_email = models.EmailField(max_length=100, verbose_name="联系邮箱", help_text="月子中心联系邮箱")
    # 地址
    address = models.CharField(max_length=100, verbose_name="地址", help_text="月子中心地址")
    # 营业时间
    business_hours = models.CharField(max_length=100, verbose_name="营业时间", help_text="月子中心营业时间")
    # rid
    rid = models.CharField(max_length=100,verbose_name="rid",help_text="联系我们rid",default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "月子中心联系我们"
        verbose_name_plural = "月子中心联系我们"
        
    @classmethod
    def get_contact_us_by_maternity_center(cls, maternity_center):
        try:
            return cls.objects.get(maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
