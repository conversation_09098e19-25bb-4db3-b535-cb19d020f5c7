from django.conf import settings
from rest_framework.views import APIView

from core.authorization import Care<PERSON><PERSON><PERSON>uthentication, StaffWithSpecificPermissionOnly
from core.logs import AuditLogCreator
from core.resp import make_response
from permissions.enum import PermissionEnum
from .models import Maternity<PERSON><PERSON>, MaternityCenterBrandIntroduction, MaternityCenterCarouselFile, \
    MaternityCenterContactUs, MaternityCenterDevelopmentHistory
from .serializers import MaternityCenterBasicInfoSerializer, MaternityCenterBasicInfoUpdateSerializer, MaternityCenterBrandIntroductionCreateSerializer, MaternityCenterBrandIntroductionSerializer, MaternityCenterBrandIntroductionUpdateSerializer, MaternityCenterCarouselListSerializer, MaternityCenterCarouselFileSerializer, MaternityCenterContactUsCreateSerializer, MaternityCenterContactUsSerializer, MaternityCenterContactUsUpdateSerializer, MaternityCenterDevelopmentHistoryCreateSerializer, MaternityCenterDevelopmentHistorySerializer, MaternityCenterDevelopmentHistoryUpdateSerializer



# 获取月子中心基本信息
class MaternityCenterBasicInfoView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_VIEW

    def get(self, request):
        serializer = MaternityCenterBasicInfoSerializer(request.user.maternity_center)
        return make_response(msg='获取月子中心基本信息成功',data=serializer.data)

# 更新月子中心基本信息
class MaternityCenterBasicInfoUpdateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT

    def put(self, request):
        serializer = MaternityCenterBasicInfoUpdateSerializer(request.user.maternity_center, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return make_response(msg='更新月子中心基本信息成功',data=MaternityCenterBasicInfoSerializer(request.user.maternity_center).data)
        return make_response(msg='更新月子中心基本信息失败',data=serializer.errors)

# 获取月子中心轮播图列表
class MaternityCenterCarouselListView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_VIEW

    def get(self, request):
        csfs,count = MaternityCenterCarouselFile.get_carouse_list(request.user.maternity_center)
        return make_response(msg='获取月子中心轮播图列表成功',data={'count':count,'list':MaternityCenterCarouselListSerializer(csfs, many=True).data})


# 新增轮播图
class MaternityCenterCarouselUploadView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT

    def post(self, request):
        
        file = request.FILES.get('carousel_file')
        if not file:
            return make_response(code=-1,msg='请提供轮播图文件')
        
        csf = MaternityCenterCarouselFile.objects.create(maternity_center=request.user.maternity_center, file=file)
        
        if not csf:
            return make_response(code=-1,msg='上传轮播图失败')
        
        AuditLogCreator.create_file_upload_audit_log(request,"微信小程序","上传了轮播图",{'model': 'MCF', 'rid': csf.rid, 'field': 'file'})

        return make_response(msg='上传轮播图成功',data=MaternityCenterCarouselListSerializer(csf).data)
    
# 删除轮播图
class MaternityCenterCarouselDeleteView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT

    def delete(self, request,rid):  
        try:
            csf = MaternityCenterCarouselFile.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            csf.delete()
            return make_response(msg='删除轮播图成功')
        except MaternityCenterCarouselFile.DoesNotExist:
            return make_response(code=-1,msg='轮播图不存在')


# 获取月子中心联系我们详情
class MaternityCenterContactUsDetailView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_VIEW

    def get(self, request):
        try:    
            mci = MaternityCenterContactUs.objects.get(maternity_center=request.user.maternity_center)
            return make_response(msg='获取月子中心联系我们详情成功',data=MaternityCenterContactUsSerializer(mci).data)
        except MaternityCenterContactUs.DoesNotExist:
            return make_response(code=0,msg='请先创建数据',data=None)

# 创建月子中心联系我们
class MaternityCenterContactUsCreateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT
    
    def post(self, request):
        
        mci = MaternityCenterContactUs.get_contact_us_by_maternity_center(request.user.maternity_center)
        
        if mci:
            return make_response(code=-1,msg='数据已存在，不允许重复创建，请使用更新功能')
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = MaternityCenterContactUsCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
                        
            return make_response(msg='创建成功',data=MaternityCenterContactUsSerializer(serializer.instance).data)
        return make_response(msg='创建失败',data=serializer.errors)
    
    
# 更新月子中心联系我们
class MaternityCenterContactUsUpdateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT
    
    
    def put(self, request):
        
        mci = MaternityCenterContactUs.get_contact_us_by_maternity_center(request.user.maternity_center)
        
        if not mci:
            return make_response(code=-1,msg='数据不存在，请先创建')
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = MaternityCenterContactUsUpdateSerializer(mci,data=data,partial=True)
        if serializer.is_valid():
            serializer.save()
            return make_response(msg='更新成功',data=MaternityCenterContactUsSerializer(serializer.instance).data)
        return make_response(msg='更新失败',data=serializer.errors)
    
    
    
    
# 获取月子中心品牌介绍列表
class MaternityCenterBrandIntroductionView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_VIEW

    def get(self, request):

        mci = MaternityCenterBrandIntroduction.get_brand_introduction_by_maternity_center(maternity_center=request.user.maternity_center)
        
        if not mci:
            return make_response(code=0,msg='请先创建数据',data=None)
        
        return make_response(msg='获取月子中心品牌介绍成功',data=MaternityCenterBrandIntroductionSerializer(mci).data)

    
    
# 创建月子中心品牌介绍
class MaternityCenterBrandIntroductionCreateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT

    def post(self, request):
        
        mci = MaternityCenterBrandIntroduction.get_brand_introduction_by_maternity_center(maternity_center=request.user.maternity_center)
        
        if mci:
            return make_response(code=-1,msg='数据已存在，不允许重复创建，请使用更新功能')
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = MaternityCenterBrandIntroductionCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(msg='创建成功',data=MaternityCenterBrandIntroductionSerializer(serializer.instance).data)
        
        return make_response(msg='创建失败',data=serializer.errors)
    

# 更新月子中心品牌介绍
class MaternityCenterBrandIntroductionUpdateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT

    def post(self, request):
        
        mci = MaternityCenterBrandIntroduction.get_brand_introduction_by_maternity_center(maternity_center=request.user.maternity_center)
        
        if not mci:
            return make_response(code=-1,msg='数据不存在，请先创建')
        
        data = request.data.copy()
                
        serializer = MaternityCenterBrandIntroductionUpdateSerializer(mci,data=data,partial=True)
        if serializer.is_valid():
            serializer.save()
            return make_response(msg='更新成功',data=MaternityCenterBrandIntroductionSerializer(serializer.instance).data)
        
        return make_response(msg='更新失败',data=serializer.errors)

# 获取发展历程列表
class MaternityCenterDevelopmentHistoryListView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_VIEW
    
    def get(self, request):
        
        mci = MaternityCenterDevelopmentHistory.get_development_history_list_by_maternity_center(maternity_center=request.user.maternity_center)
        
        return make_response(msg='获取发展历程列表成功',data=MaternityCenterDevelopmentHistorySerializer(mci, many=True).data)


# 创建发展历程
class MaternityCenterDevelopmentHistoryCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT

    def post(self, request):
        
        # 获取品牌介绍
        mci = MaternityCenterBrandIntroduction.get_brand_introduction_by_maternity_center(maternity_center=request.user.maternity_center)
        
        if not mci:
            return make_response(code=-1,msg='请先创建品牌介绍')
          
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = MaternityCenterDevelopmentHistoryCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(msg='创建成功',data=MaternityCenterDevelopmentHistorySerializer(serializer.instance).data)
        return make_response(msg='创建失败',data=serializer.errors)
    

# 更新发展历程
class MaternityCenterDevelopmentHistoryUpdateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT

    def put(self, request, rid):

        mci = MaternityCenterDevelopmentHistory.get_development_history_by_rid(rid,request.user.maternity_center)
        
        if not mci:
            return make_response(code=-1,msg='数据不存在')
        
        serializer = MaternityCenterDevelopmentHistoryUpdateSerializer(mci,data=request.data,partial=True)
        if serializer.is_valid():
            serializer.save()
            return make_response(msg='更新成功',data=MaternityCenterDevelopmentHistorySerializer(serializer.instance).data)
        
        return make_response(msg='更新失败',data=serializer.errors)

    
    

# 删除发展历程
class MaternityCenterDevelopmentHistoryDeleteView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WECHAT_APP_EDIT

    def delete(self, request, rid):
        mci = MaternityCenterDevelopmentHistory.get_development_history_by_rid(rid,request.user.maternity_center)
        
        if not mci:
            return make_response(code=-1,msg='数据不存在')
        
        mci.delete()
        
        return make_response(msg='删除成功')