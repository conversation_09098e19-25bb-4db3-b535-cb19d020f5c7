from django.conf import settings
from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from .models import MaternityCenter, MaternityCenterBrandIntroduction, MaternityCenterCarouselFile, \
    MaternityCenterDevelopmentHistory, MaternityCenterContactUs

# 月子中心基本信息序列化器
class MaternityCenterBasicInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaternityCenter
        fields = ['name','address','contact','capacity','cid']
        
# 月子中心基本信息更新序列化器
class MaternityCenterBasicInfoUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaternityCenter
        fields = ['name','address','contact','capacity']

# 月子中心轮播图序列化器
class MaternityCenterCarouselListSerializer(serializers.ModelSerializer):
    
    # 链接
    url = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = MaternityCenterCarouselFile
        fields = ['url','rid','created_at']

    def get_url(self, obj):
        if obj.file:
            return obj.file.url
        return None


# 月子中心联系我们创建序列化器
class MaternityCenterContactUsCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityCenterContactUs
        fields = ['maternity_center', 'contact_image', 'contact_phone', 'contact_email', 'address', 'business_hours']

# 月子中心联系我们更新序列化器
class MaternityCenterContactUsUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityCenterContactUs
        fields = ['contact_image', 'contact_phone', 'contact_email', 'address', 'business_hours']
        

# 月子中心发展历程创建序列化器
class MaternityCenterDevelopmentHistoryCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityCenterDevelopmentHistory
        fields = ['maternity_center', 'year', 'description']

# 月子中心发展历程更新序列化器
class MaternityCenterDevelopmentHistoryUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityCenterDevelopmentHistory
        fields = ['year', 'description']


# 月子中心品牌介绍创建序列化器
class MaternityCenterBrandIntroductionCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityCenterBrandIntroduction
        fields = ['maternity_center', 'introduction_image', 'brand_introduction', 'brand_advantage', 'brand_vision','honor_qualification']


# 月子中心品牌介绍更新序列化器
class MaternityCenterBrandIntroductionUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityCenterBrandIntroduction
        fields = ['introduction_image', 'brand_introduction', 'brand_advantage', 'brand_vision','honor_qualification']

class MaternityCenterSerializer(serializers.ModelSerializer):
    """
    月子中心序列化器
    用于序列化和反序列化月子中心数据
    """
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = MaternityCenter
        exclude = ['id']

class MaternityCenterListSerializer(serializers.ModelSerializer):
    
    
    class Meta:
        model = MaternityCenter
        fields = ['cid','name','address']
        

# 月子中心轮播图序列化器
class MaternityCenterCarouselFileSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = MaternityCenterCarouselFile
        exclude = ('maternity_center','id')
        

# 月子中心发展历程
class MaternityCenterDevelopmentHistorySerializer(serializers.ModelSerializer):
    
    
    class Meta:
        model = MaternityCenterDevelopmentHistory
        fields = ['year', 'description','rid']
        
# 月子中心品牌介绍序列化器
class MaternityCenterBrandIntroductionSerializer(serializers.ModelSerializer):
    
    development_histories = serializers.SerializerMethodField()
    introduction_image = serializers.SerializerMethodField()
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = MaternityCenterBrandIntroduction
        exclude = ('maternity_center','id')
        read_only_fields = ('created_at', 'updated_at')
        
    def get_development_histories(self, obj):
        if obj.maternity_center:
            return MaternityCenterDevelopmentHistorySerializer(
                obj.maternity_center.development_histories.all(), 
                many=True
            ).data
        return []
    
    def get_introduction_image(self, obj):
        if obj.introduction_image:
            return obj.introduction_image.url
        return None
    
# 月子中心联系我们序列化器
class MaternityCenterContactUsSerializer(serializers.ModelSerializer):
    
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    contact_image = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityCenterContactUs
        exclude = ('maternity_center','id')
        read_only_fields = ('created_at', 'updated_at')

        
    def get_contact_image(self, obj):
        if obj.contact_image:
            return obj.contact_image.url
        return None
    
    
    
    
    
        