from django.db import models

class FeedbackTypeEnum(models.TextChoices):
    # 意见建议
    SUGGESTION = 'SUGGESTION', '意见建议'
    # 服务反馈
    SERVICE = 'SERVICE', '服务反馈'
    # 环境反馈
    ENVIRONMENT = 'ENVIRONMENT', '环境反馈'
    # 设施反馈
    FACILITY = 'FACILITY', '设施反馈'
    # 其他反馈
    OTHER = 'OTHER', '其他反馈'


class FeedbackStatusEnum(models.TextChoices):
    # 待处理
    PENDING = 'PENDING', '待处理'
    # 处理中
    PROCESSING = 'PROCESSING', '处理中'
    # 已处理
    RESOLVED = 'RESOLVED', '已处理'