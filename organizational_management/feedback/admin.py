from django.contrib import admin

from .models import MaternityFeedback, FeedbackProcessRecord


# Register your models here.
@admin.register(MaternityFeedback)
class MaternityFeedbackAdmin(admin.ModelAdmin):
    list_display = ['fid', 'maternity_center', 'maternity_admission', 'feedback_type', 'status', 'created_at']
    search_fields = ['fid', 'maternity_center', 'maternity_admission', 'feedback_type', 'status', 'created_at']
    list_filter = ['maternity_center', 'maternity_admission', 'feedback_type', 'status', 'created_at']

@admin.register(FeedbackProcessRecord)
class MaternityFeedbackProcessRecordAdmin(admin.ModelAdmin):
    list_display = ['fid', 'feedback', 'creator', 'created_at']
    search_fields = ['fid', 'feedback', 'creator', 'created_at']
    list_filter = ['feedback', 'creator', 'created_at']