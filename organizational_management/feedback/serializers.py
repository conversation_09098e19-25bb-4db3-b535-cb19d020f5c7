from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from .models import MaternityFeedback, FeedbackProcessRecord


class FeedbackRecordSerializer(serializers.ModelSerializer):
    creator_name = serializers.CharField(source='creator.name', read_only=True)

    class Meta:
        model = FeedbackProcessRecord
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'creator_name')

# 客户反馈列表序列化器
class FeedbackListSerializer(serializers.ModelSerializer):
    
    # 产妇信息
    maternity_info = serializers.SerializerMethodField()
    
    # 反馈时间
    feedback_time = ShanghaiFriendlyDateTimeField()
    
    # 反馈类型
    feedback_type_display = serializers.SerializerMethodField()
    
    # 处理状态
    status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityFeedback
        fields = [
            'fid',
            'maternity_info',
            'feedback_time',
            'feedback_type',
            'feedback_type_display',
            'status',
            'status_display',
            'content',
        ]

    def get_maternity_info(self, obj):
        return f'{obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else '-'} （{obj.maternity_admission.room.room_number if obj.maternity_admission.room else '-'}）'

    def get_feedback_type_display(self, obj):
        return obj.get_feedback_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()



# 客户反馈处理记录序列化器
class FeedbackProcessRecordSerializer(serializers.ModelSerializer):
    
    # 处理人
    creator_name = serializers.SerializerMethodField()
    # 处理时间
    process_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = FeedbackProcessRecord
        fields = [
            'fid',
            'creator_name',
            'process_time',
            'content',
        ]
        read_only_fields = ('id', 'process_time', 'creator_name')
        
    def get_creator_name(self, obj):
        return obj.creator.name

    

# 客户反馈详情序列化器
class FeedbackDetailSerializer(serializers.ModelSerializer):
    
    # 产妇信息
    maternity_info = serializers.SerializerMethodField()
    
    # 反馈时间
    feedback_time = ShanghaiFriendlyDateTimeField()
    
    # 反馈类型
    feedback_type_display = serializers.SerializerMethodField()
    
    # 处理状态
    status_display = serializers.SerializerMethodField()
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    
    # 反馈处理记录
    process_records = FeedbackProcessRecordSerializer(many=True, read_only=True)
    
    
    class Meta:
        model = MaternityFeedback
        fields = [
            'fid',
            'maternity_info',
            'feedback_time',
            'feedback_type',
            'feedback_type_display',
            'status',
            'status_display',
            'content',
            'contact_info',
            'process_records',  
            'created_at',
            'updated_at',
        ]

    def get_maternity_info(self, obj):
        return f'{obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else '-'} （{obj.maternity_admission.room.room_number if obj.maternity_admission.room else '-'}）'

    def get_feedback_type_display(self, obj):
        return obj.get_feedback_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    
    
