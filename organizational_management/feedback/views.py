from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly, MaternityOrStaffWithPermission
from core.resp import make_response
from core.view import PaginationListBaseView
from permissions.enum import PermissionEnum
from .enum import FeedbackType<PERSON>num, FeedbackStatusEnum
from .models import MaternityFeedback, FeedbackProcessRecord
from .serializers import FeedbackDetailSerializer, FeedbackListSerializer, FeedbackProcessRecordSerializer


# Create your views here.

# 客户反馈列表
class FeedbackListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CUSTOMER_FEEDBACK_SURVEY_VIEW
    serializer_class = FeedbackListSerializer
    response_msg = "获取客户反馈列表成功"
    error_response_msg = "获取客户反馈列表失败"
    search_fields = ['maternity_admission__maternity__name', 'maternity_admission__room__room_number']
    
    
    def get_queryset(self):

        fd_time = self.request.query_params.get('fd_time', None)
        fd_type = self.request.query_params.get('fd_type', None)
        status = self.request.query_params.get('status', None)
        
        base_queryset = MaternityFeedback.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
        
        # 反馈时间
        if fd_time:
            base_queryset = base_queryset.filter(feedback_time__gte=fd_time)
        
        # 反馈类型
        if fd_type:
            if fd_type in FeedbackTypeEnum.values:
                base_queryset = base_queryset.filter(feedback_type=fd_type)
            else:
                self.error_response_msg = "反馈类型不正确"
                return None
        
        # 处理状态
        if status:
            if status in FeedbackStatusEnum.values:
                base_queryset = base_queryset.filter(status=status)
            else:
                self.error_response_msg = "处理状态不正确"
                return None
        
        return base_queryset



# 客户反馈详情
class FeedbackDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.CUSTOMER_FEEDBACK_SURVEY_VIEW

    def get(self, request, fid):
        
        instance = MaternityFeedback.get_feedback_by_fid(fid, request.user.maternity_center)
        
        instance.update_pending_status()
        
        if not instance:
            return make_response(code=-1, msg="反馈不存在")
        
        serializer = FeedbackDetailSerializer(instance)
        return make_response(code=0, msg="获取反馈详情成功", data=serializer.data)



# 客户反馈处理
class FeedbackProcessView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.CUSTOMER_FEEDBACK_SURVEY_EDIT
    
    def put(self, request, fid):

        instance = MaternityFeedback.get_feedback_by_fid(fid, request.user.maternity_center)
        
        if not instance:
            return make_response(code=-1, msg="反馈不存在")
        
        if instance.status not in [FeedbackStatusEnum.PENDING, FeedbackStatusEnum.PROCESSING]:
            return make_response(code=-1, msg=f"当前反馈状态无法处理，当前状态为：{instance.get_status_display()}")
        
        content = request.data.get('content', None)
        
        if not content:
            return make_response(code=-1, msg="处理内容不能为空")
        
        fp_ins = FeedbackProcessRecord.create_process_record(instance, request.user, content)
        
        if not fp_ins:
            return make_response(code=-1, msg=f"{instance.fid} 处理失败，请稍后重试")
        
        return make_response(code=0, msg=f"{instance.fid} 处理成功", data=FeedbackDetailSerializer(instance).data)




# 客户反馈处理记录列表
class FeedbackProcessRecordListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.CUSTOMER_FEEDBACK_SURVEY_VIEW
    
    def get(self, request, fid):
        
        instance = MaternityFeedback.get_feedback_by_fid(fid, request.user.maternity_center)
        
        if not instance:
            return make_response(code=-1, msg="反馈不存在")
        
        serializer = FeedbackProcessRecordSerializer(instance.process_records.all(), many=True)

    