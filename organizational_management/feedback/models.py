from django.db import models, transaction

from core.generate_hashid import generate_feedback_code, generate_feedback_process_code
from core.model import BaseModel
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from maternity_center.models import MaternityCenter
from organizational_management.feedback.enum import FeedbackTypeEnum, FeedbackStatusEnum
from user.models import Staff


class MaternityFeedback(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name='月子中心')
    # 入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name='入院记录')
    # 反馈内容
    content = models.TextField(verbose_name='反馈内容')
    # 联系方式（选填）
    contact_info = models.CharField(max_length=100, verbose_name='联系方式',  blank=True,default="")
    # 反馈时间
    feedback_time = models.DateTimeField(verbose_name='反馈时间',auto_now_add=True)
    # 反馈类型
    feedback_type = models.TextField(choices=FeedbackTypeEnum.choices, verbose_name='反馈类型')
    # 处理状态
    status = models.TextField(choices=FeedbackStatusEnum.choices, default=FeedbackStatusEnum.PENDING, verbose_name='处理状态')
    # 编号
    fid = models.CharField(max_length=100, verbose_name='反馈编号',  default=generate_feedback_code, unique=True, editable=False)

    class Meta:
        verbose_name = '客户反馈'
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return self.fid
    
    def update_pending_status(self):
        if self.status == FeedbackStatusEnum.PENDING:
            self.status = FeedbackStatusEnum.PROCESSING
            self.save()
    
    @classmethod
    def get_feedback_by_fid(cls, fid, maternity_center,maternity=None):
        try:    
            if maternity:
                return cls.objects.select_related('maternity_admission__maternity').get(fid=fid, maternity_center=maternity_center,maternity_admission__maternity=maternity)
            else:
                return cls.objects.select_related('maternity_admission__maternity').get(fid=fid, maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
    @classmethod
    def get_feedback_queryset_by_aid(cls, aid):
        return cls.objects.filter(maternity_admission__aid=aid).order_by('-feedback_time')
    
    
    

class FeedbackProcessRecord(BaseModel):
    # 反馈
    feedback = models.ForeignKey(MaternityFeedback, on_delete=models.CASCADE, verbose_name='反馈', related_name='process_records')
    # 处理人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name='处理人')
    # 处理时间
    process_time = models.DateTimeField(auto_now_add=True, verbose_name='处理时间')
    # 处理内容
    content = models.TextField(verbose_name='处理内容')
    # 反馈编号
    fid = models.CharField(max_length=100, verbose_name='反馈编号',  default=generate_feedback_process_code, unique=True, editable=False)    

    class Meta:
        verbose_name = '反馈处理记录'
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return self.fid
    
    @classmethod
    def create_process_record(cls, feedback, creator, content):
        
        with transaction.atomic():
        
            fp_ins = cls.objects.create(feedback=feedback, creator=creator, content=content)
            
            feedback.status = FeedbackStatusEnum.RESOLVED
            feedback.save()
    
        return fp_ins



