from datetime import datetime, timedelta
from datetime import datetime, timedelta
from django.db import transaction
from django.utils import timezone
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.resp import make_response
from core.view import PaginationListBaseView
from file.models import StaffAssessmentRecordAttachment, StaffContractFile, StaffHealthCheckRecordAttachment, StaffTrainingRecordAttachment
from file.serializers import StaffContractFileSerializer
from organizational_management.staff_schedule.utils import comprehensive_schedule_validation, comprehensive_single_schedule_validation, validate_schedule_date, validate_schedule_date_range, validate_schedule_rid
from permissions.enum import PermissionEnum
from permissions.models import StaffRole
from user.models import Department, Staff
from user.serializers import DepartmentListSerializer, StaffCreateSerializer, StaffListSerializer, StaffSerializer, \
    StaffUpdateSerializer
from .enum import ScheduleStatusEnum, ScheduleDetailStatusEnum, HandoverStatusEnum, ShiftTypeEnum
from .models import Schedule, Staff<PERSON>sessmentRecord, StaffContract, StaffHealthCheckRecord, StaffQualificationCertificate, \
    StaffTrainingRecord
from .serializers import (
    ScheduleSerializer,
    StaffAssessmentRecordCreateSerializer,
    StaffAssessmentRecordDetailSerializer,
    StaffAssessmentRecordUpdateSerializer,
    StaffContractCreateSerializer,
    StaffContractDetailSerializer,
    StaffHealthCheckRecordCreateSerializer,
    StaffHealthCheckRecordDetailSerializer,
    StaffHealthCheckRecordUpdateSerializer,
    StaffQualificationCertificateCreateSerializer,
    StaffQualificationCertificateDetailSerializer,
    StaffTrainingRecordCreateSerializer,
    StaffTrainingRecordUpdateSerializer,
    StaffTrainingRecordDetailSerializer
)


# 校验员工排班
class ScheduleValidateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.SCHEDULE_VIEW
    
    def post(self, request):
        
        data = request.data.copy()
        data['maternity_center'] = request.user.maternity_center.id
        
        is_valid_date, error_msg, start_date, end_date = validate_schedule_date_range(data['start_date'], data['end_date'])
        
        result = comprehensive_schedule_validation(data, is_valid_date, start_date, end_date)
        is_valid = result['is_valid']
        response_msg = "排班校验完成" 
        if not is_valid:
            response_msg = error_msg if error_msg else "排班校验失败"
        return make_response(code=0 if is_valid else -1, msg=response_msg, data=result)


# 创建排班
class ScheduleCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.SCHEDULE_EDIT
    
    def post(self, request):

        data = request.data.copy()
        data['maternity_center'] = request.user.maternity_center.id

        start_date = data.get('start_date')
        end_date = data.get('end_date')
        schedule_configs = data.get('schedules')  # 注意这里应该是schedules
        remark = data.get('remark', '')

        is_valid_date, error_msg, start_date, end_date = validate_schedule_date_range(start_date, end_date)
        staff_dict = {}
        result = comprehensive_schedule_validation(data, is_valid_date, start_date, end_date, staff_dict)
        if not result['is_valid']:
            return make_response(code=-1, msg=error_msg if error_msg else "排班校验失败", data=result)

        try:
            with transaction.atomic():
                created_schedules = self._create_schedule_records(
                    staff_dict=staff_dict,
                    maternity_center_id=data['maternity_center'],
                    start_date=start_date,
                    end_date=end_date,
                    schedule_configs=schedule_configs,
                    remark=remark
                )

                sample_schedules = Schedule.objects.filter(
                    maternity_center_id=data['maternity_center'],
                    schedule_date__range=[start_date, end_date],
                    staff__sid__in=[config['staff_sid'] for config in schedule_configs]
                ).select_related('staff').order_by('schedule_date', 'staff__sid', 'shift_type')

                return make_response(
                    code=0,
                    msg="排班创建成功",
                    data={
                        "created_schedules_count": len(created_schedules),
                        "date_range": f"{start_date} 至 {end_date}",
                        "staff_count": len(set(config['staff_sid'] for config in schedule_configs)),
                        "schedules": ScheduleSerializer(sample_schedules, many=True).data,
                    }
                )

        except Exception as e:
            return make_response(code=-1, msg=f"排班创建失败: {str(e)}")

    def _create_schedule_records(self, staff_dict, maternity_center_id, start_date, end_date, schedule_configs, remark):

        # 生成日期范围
        date_range = []
        current_date = start_date
        while current_date <= end_date:
            date_range.append(current_date)
            current_date += timedelta(days=1)

        # 批量创建排班记录
        schedule_records = []
        for config in schedule_configs:
            staff_sid = config['staff_sid']
            position = config.get('position', '')
            shift_type = config['shift_type']
            for date in date_range:
                schedule_record = Schedule(
                    maternity_center_id=maternity_center_id,
                    staff_id=staff_dict[staff_sid][0],
                    position=position,
                    schedule_date=date,
                    shift_type=shift_type,
                    remark=remark
                )
                schedule_records.append(schedule_record)
        # 批量插入数据库
        created_schedules = Schedule.objects.bulk_create(schedule_records)

        # 返回创建的记录数量
        return created_schedules


# 查看排班日/周/月视图
class ScheduleQueryView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.SCHEDULE_VIEW

    def get(self, request):
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)
        department_rid = request.query_params.get('department_rid', None)
        staff_sid = request.query_params.get('staff_sid', None)
        shift_type = request.query_params.get('shift_type', None)
        start_date_obj = None
        end_date_obj = None
        # 校验日期格式
        try:
            if start_date:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            if end_date:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except Exception as e:
            return make_response(code=-1, msg="日期格式不正确")
        if start_date_obj and end_date_obj and start_date_obj > end_date_obj:
            return make_response(code=-1, msg="开始日期不能大于结束日期")
        # 校验班次类型
        if shift_type:
            try:
                shift_type = ShiftTypeEnum(shift_type)
            except Exception as e:
                return make_response(code=-1, msg="班次类型错误")
        # 获取排班数据
        schedules = Schedule.objects.filter(
            maternity_center=request.user.maternity_center,
        ).select_related('staff','staff__department').order_by('schedule_date')
        if start_date_obj:
            schedules = schedules.filter(schedule_date__gte=start_date_obj)
        if end_date_obj:
            schedules = schedules.filter(schedule_date__lte=end_date_obj)
        if staff_sid:
            schedules = schedules.filter(staff__sid=staff_sid)
        elif department_rid:
            schedules = schedules.filter(staff__department__rid=department_rid)
        if shift_type:
            schedules = schedules.filter(shift_type=shift_type)
        schedules = schedules[:500]
        # 序列化数据        
        serializer = ScheduleSerializer(schedules, many=True)
        return make_response(code=0, msg="获取排班成功", data=serializer.data)


# 查看排班列表
class ScheduleListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.SCHEDULE_VIEW
    serializer_class = ScheduleSerializer
    response_msg = "获取排班列表成功"
    error_response_msg = "获取排班列表失败"
    
    def get_queryset(self):
        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        department_rid = self.request.query_params.get('department_rid', None)
        staff_sid = self.request.query_params.get('staff_sid', None)
        shift_type = self.request.query_params.get('shift_type', None)
        start_date_obj = None
        end_date_obj = None
        # 校验日期
        try:
            if start_date:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            if end_date:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except Exception as e:
            self.error_response_msg = "日期格式不正确"
            return None
        if start_date_obj and end_date_obj and start_date_obj > end_date_obj:
            self.error_response_msg = "开始日期不能大于结束日期"
            return None
        # 校验班次类型
        if shift_type:
            try:
                shift_type = ShiftTypeEnum(shift_type)
            except Exception as e:
                self.error_response_msg = "班次类型错误"
                return None
        # 获取排班数据
        schedules = Schedule.objects.filter(
            maternity_center=self.request.user.maternity_center,
        ).select_related('staff','staff__department').order_by('schedule_date')
        if start_date_obj:
            schedules = schedules.filter(schedule_date__gte=start_date_obj)
        if end_date_obj:
            schedules = schedules.filter(schedule_date__lte=end_date_obj)
        if staff_sid:
            schedules = schedules.filter(staff__sid=staff_sid)
        elif department_rid:
            schedules = schedules.filter(staff__department__rid=department_rid)
        if shift_type:
            schedules = schedules.filter(shift_type=shift_type)
        return schedules


# 排班详情
class ScheduleDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.SCHEDULE_VIEW
    
    def get(self, request, rid):
        schedule = Schedule.objects.filter(
            maternity_center=request.user.maternity_center,
            rid=rid
        ).select_related('staff','staff__department').first()
        if not schedule:
            return make_response(code=-1, msg="排班不存在")
        return make_response(code=0, msg="获取排班详情成功", data=ScheduleSerializer(schedule).data)


# 排班更新
class ScheduleUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.SCHEDULE_EDIT
    # post仅校验
    def post(self, request, rid):
        data = request.data.copy()
        data['maternity_center'] = request.user.maternity_center.id
        data['rid'] = rid
        
        error_msg, schedule_date = validate_schedule_date(data['schedule_date'])
        result = comprehensive_single_schedule_validation(data, error_msg=="", schedule_date)
        is_valid = result['is_valid']
        response_msg = "排班更新校验成功"
        if not is_valid:
            response_msg = error_msg if error_msg else "排班更新校验失败"
        return make_response(code=0 if is_valid else -1, msg=response_msg, data=result)

    # put更新
    def put(self, request, rid):
        data = request.data.copy()
        data['maternity_center'] = request.user.maternity_center.id
        data['rid'] = rid
        
        error_msg, schedule_date = validate_schedule_date(data['schedule_date'])
        original_schedule_list = []
        staff_list = []
        result = comprehensive_single_schedule_validation(data, error_msg=="", schedule_date, original_schedule_list, staff_list)

        if not result['is_valid']:
            return make_response(code=-1, msg=error_msg if error_msg else "排班更新校验失败", data=result)
        
        original_schedule = original_schedule_list[0]
        try:
            original_schedule.schedule_date = schedule_date
            original_schedule.staff = staff_list[0]
            original_schedule.position = data.get('position', '')
            original_schedule.shift_type = data['shift_type']
            original_schedule.remark = data.get('remark', '')
            if 'status' in data:
                original_schedule.status = data['status']
            original_schedule.save()
        except Exception as e:
            return make_response(code=-1, msg=f"排班更新失败: {str(e)}")
        
        return make_response(
            code=0,
            msg="排班更新成功",
            data={
                "updated_schedules_count": 1,
                "schedules": [ScheduleSerializer(original_schedule).data],
            }
        )


# 排班删除
class ScheduleDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.SCHEDULE_EDIT
    
    def delete(self, request, rid):
        errors = []
        original_schedule = validate_schedule_rid(errors, rid, request.user.maternity_center.id)
        if not original_schedule:
            return make_response(code=-1, msg="排班不存在", data={
                "is_valid": False,
                "validation_type": "security_violation",
                "is_legitimate_request": False,
                "errors": errors
            })
        original_schedule.delete()
        return make_response(code=0, msg="排班删除成功", data={
            "deleted_schedules_count": 1,
            "schedules": [ScheduleSerializer(original_schedule).data],
        })


# 员工列表
class StaffListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    serializer_class = StaffListSerializer
    response_msg = "获取员工列表成功"
    error_response_msg = "获取员工列表失败"
    search_fields = ['name','staff_number']
    
    def get_queryset(self):
        
        department = self.request.query_params.get('department',None)
        if department:
            return Staff.objects.filter(maternity_center=self.request.user.maternity_center,department__rid=department)
        
        return Staff.objects.filter(maternity_center=self.request.user.maternity_center)
    
# 员工详情
class StaffDetailView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    
    def get(self, request, sid):
        staff = Staff.get_staff_by_sid(sid=sid,maternity_center=request.user.maternity_center)
        
        if not staff:
            return make_response(code=-1, msg="员工不存在")
                
        return make_response(code=0, msg="获取员工详情成功", data=StaffSerializer(staff).data)
    

# 员工创建视图
class StaffCreateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT

    def post(self, request):
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        phone = data['phone']
        staff_number = data['staff_number']
        identity_number = data.get('identity_number',"")
        department = data.get('department',None)
        role = data.get('role',None)
        password  = data.get('password',"")
        password_confirm = data.get('password_confirm',"")
        
        if not password or not password_confirm or password != password_confirm:
            return make_response(code=-1, msg="两次输入的密码不一致或密码为空")
        
        department = Department.get_department_by_rid(rid=department,maternity_center=request.user.maternity_center)
        if not department:
            return make_response(code=-1, msg="部门数据错误")
        
        data['department'] = department.id
        
        staff_role = StaffRole.get_role_by_rid(rid=role,maternity_center=request.user.maternity_center)
        if not staff_role:
            return make_response(code=-1, msg="角色数据错误")
        
        data['role'] = staff_role.id
        
        if phone and Staff.objects.filter(phone=phone).exists():
            return make_response(code=-1, msg="该电话号码已存在")
        
        if staff_number and Staff.objects.filter(staff_number=staff_number).exists():
            return make_response(code=-1, msg="该工号已存在")
        
        if identity_number and Staff.objects.filter(identity_number=identity_number).exists():
            return make_response(code=-1, msg="该身份证号已存在")

        serializer = StaffCreateSerializer(data=data, context={'request': request})

        if serializer.is_valid():
            try:
                staff = serializer.save()
                return make_response(
                    code=0,
                    msg="员工创建成功",
                    data=StaffSerializer(staff).data
                )
            except Exception as e:
                return make_response(code=-1, msg=f"创建员工失败: {str(e)}")
        return make_response(code=-1, msg="创建员工失败",data=serializer.errors)


# 员工更新视图
class StaffUpdateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT

    def put(self, request, sid):
        try:
            staff = Staff.objects.get(sid=sid, maternity_center=request.user.maternity_center)
        except Staff.DoesNotExist:
            return make_response(code=-1, msg="员工不存在")

        data = request.data.copy()
        phone = data.get('phone')
        staff_number = data.get('staff_number')
        department = data.get('department', None)
        role = data.get('role', None)

        # 验证部门是否存在且属于当前月子中心
        department = Department.get_department_by_rid(rid=department,maternity_center=request.user.maternity_center)
        if not department:
            return make_response(code=-1, msg="部门数据错误")
        
        data['department'] = department.id

        # 验证角色是否存在且属于当前月子中心
        staff_role = StaffRole.get_role_by_rid(rid=role,maternity_center=request.user.maternity_center)
        if not staff_role:
            return make_response(code=-1, msg="角色数据错误")
        
        data['role'] = staff_role.id

        # 检查电话号码是否被其他员工使用
        if phone:
            existing_staff = Staff.objects.filter(phone=phone).exclude(id=staff.id).first()
            if existing_staff:
                return make_response(code=-1, msg=f"该电话号码已被员工{existing_staff.name}使用")

        # 检查工号是否被其他员工使用
        if staff_number:
            existing_staff = Staff.objects.filter(staff_number=staff_number).exclude(id=staff.id).first()
            if existing_staff:
                return make_response(code=-1, msg=f"该工号已被员工{existing_staff.name}使用")

        # 如果请求数据中包含密码相关字段，直接拒绝
        if 'password' in data or 'password_confirm' in data:
            return make_response(code=-1, msg="不允许通过此接口更新密码")

        serializer = StaffUpdateSerializer(staff, data=data, partial=True, context={'request': request})

        if serializer.is_valid():
            try:
                updated_staff = serializer.save()
                return make_response(
                    code=0,
                    msg="员工信息更新成功",
                    data=StaffSerializer(updated_staff).data
                )
            except Exception as e:
                return make_response(code=-1, msg=f"更新员工失败: {str(e)}")
        return make_response(code=-1, msg="更新员工失败", data=serializer.errors)


# 员工离职视图
class StaffResignationView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT

    def put(self, request, sid):
        try:
            staff = Staff.objects.get(sid=sid, maternity_center=request.user.maternity_center)
        except Staff.DoesNotExist:
            return make_response(code=-1, msg="员工不存在")

        # 检查员工是否已经离职
        if not staff.is_active:
            return make_response(code=-1, msg="该员工已经离职")

        # # 检查员工是否有预定或在住的入院记录
        # from customer_service.core_records.models.maternity_admission import MaternityAdmission
        # from core.enum import CheckInStatusEnum

        # # 查找该员工作为主任护理的预定或在住记录
        # active_admissions = MaternityAdmission.objects.filter(
        #     chief_nurse=staff,
        #     check_in_status__in=[CheckInStatusEnum.RESERVED, CheckInStatusEnum.CHECKED_IN],
        #     maternity_center=request.user.maternity_center
        # )

        # if active_admissions.exists():
        #     # 获取具体的记录信息用于错误提示
        #     admission_info = []
        #     for admission in active_admissions[:3]:
        #         maternity_name = admission.maternity.name if admission.maternity else "未知产妇"
        #         status_label = CheckInStatusEnum(admission.check_in_status).label
        #         room_info = f"房间{admission.room.room_number}" if admission.room else "未分配房间"
        #         admission_info.append(f"{maternity_name}({status_label}, {room_info})")

        #     total_count = active_admissions.count()
        #     if total_count > 3:
        #         admission_info.append(f"等{total_count}条记录")

            # return make_response(
            #     code=-1,
            #     msg=f"该员工目前负责{total_count}条预定或在住的入院记录，无法办理离职。涉及记录：{', '.join(admission_info)}"
            # )

        # # 检查员工是否有未完成的排班安排
        # from .models import ScheduleDetail
        # from .enum import ScheduleDetailStatusEnum

        # future_schedules = ScheduleDetail.objects.filter(
        #     staff=staff,
        #     schedule__schedule_date__gte=timezone.now().date(),
        #     status__in=[ScheduleDetailStatusEnum.PENDING, ScheduleDetailStatusEnum.CONFIRMED],
        #     schedule__maternity_center=request.user.maternity_center
        # )

        # if future_schedules.exists():
        #     schedule_count = future_schedules.count()
        #     return make_response(
        #         code=-1,
        #         msg=f"该员工还有{schedule_count}个未完成的排班安排，请先处理排班后再办理离职"
        #     )

        staff.is_active = False
        staff.save()

        return make_response(
            code=0,
            msg="员工标记离职成功",
            data=StaffSerializer(staff).data
        )



# 部门列表
class DepartmentListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    serializer_class = DepartmentListSerializer
    response_msg = "获取部门列表成功"
    error_response_msg = "获取部门列表失败"
    search_fields = ['name']
    
    def get_queryset(self):
        return Department.objects.filter(maternity_center=self.request.user.maternity_center)
    
    
# 角色列表
class RoleListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    serializer_class = DepartmentListSerializer
    response_msg = "获取部门列表成功"
    error_response_msg = "获取部门列表失败"
    search_fields = ['name']
    
    def get_queryset(self):
        return StaffRole.objects.filter(maternity_center=self.request.user.maternity_center)
    

# 获取指定员工合同列表
class StaffContractFileList(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    
    def get(self, request, sid):
        staff_contracts = StaffContract.objects.select_related('file','staff','creator').filter(staff__sid=sid,maternity_center=request.user.maternity_center).order_by('-created_at')
        serializer = StaffContractDetailSerializer(staff_contracts, many=True)
        return make_response(code=0, msg="获取员工合同列表成功", data=serializer.data)

# 创建员工合同
class StaffContractFileCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def post(self, request, sid):
        
        data = request.data.copy()
        
        staff = Staff.get_staff_by_sid(sid=sid,maternity_center=request.user.maternity_center)
        
        if not staff:
            return make_response(code=-1, msg="员工不存在")
        
        
        try:
            contract_date = datetime.strptime(data['contract_validity_period'], "%Y-%m-%d").date()
            if contract_date <= datetime.now().date():
                return make_response(code=-1, msg="合同有效期必须大于当前日期")
        except ValueError:
                return make_response(code=-1, msg="合同有效期格式错误")
        
        data['staff'] = staff.id
        data['creator'] = request.user.id
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = StaffContractCreateSerializer(data=data,context={'request': request})
        
        if serializer.is_valid():
            staff_contract = serializer.save()
            return make_response(code=0, msg="员工合同创建成功", data=StaffContractDetailSerializer(staff_contract).data)
        return make_response(code=-1, msg='员工合同创建失败',data=serializer.errors)
        

    
# 删除指定员工合同
class StaffContractFileDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def delete(self, request, rid):
        try:
            staff_contract_file = StaffContract.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            staff_contract_file.delete()
            return make_response(code=0, msg="删除员工合同成功")
        except StaffContract.DoesNotExist:
            return make_response(code=-1, msg="员工合同不存在")
        except Exception as e:
            return make_response(code=-1, msg=f"删除员工合同失败: {str(e)}")
        

# 获取指定员工资质证书列表  
class StaffQualificationCertificateList(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    
    def get(self, request, sid):
        staff_qualification_certificates = StaffQualificationCertificate.objects.select_related('file','staff','creator').filter(staff__sid=sid,maternity_center=request.user.maternity_center)
        print(staff_qualification_certificates.count())
        serializer = StaffQualificationCertificateDetailSerializer(staff_qualification_certificates, many=True)
        return make_response(code=0, msg="获取员工资质证书列表成功", data=serializer.data)




# 创建员工资质证书
class StaffQualificationCertificateCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT

    def post(self, request, sid):
        data = request.data.copy()
        
        staff = Staff.get_staff_by_sid(sid=sid,maternity_center=request.user.maternity_center)
        
        if not staff:
            return make_response(code=-1, msg="员工不存在")
        
        try:
            validity_date = datetime.strptime(data['validity_period'], "%Y-%m-%d").date()
            if validity_date <= datetime.now().date():
                return make_response(code=-1, msg="证书有效期必须大于当前日期")
        except ValueError:
            return make_response(code=-1, msg="证书有效期格式错误")
        
        data['staff'] = staff.id
        data['creator'] = request.user.id
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = StaffQualificationCertificateCreateSerializer(data=data,context={'request': request})
        
        if serializer.is_valid():
            staff_contract = serializer.save()
            return make_response(code=0, msg="员工合同创建成功", data=StaffQualificationCertificateDetailSerializer(staff_contract).data)
        return make_response(code=-1, msg='员工合同创建失败',data=serializer.errors)

# 删除指定员工资质证书
class StaffQualificationCertificateDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def delete(self, request, rid):
        try:
            staff_qualification_certificate = StaffQualificationCertificate.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            staff_qualification_certificate.delete()
            return make_response(code=0, msg="删除员工资质证书成功")
        except StaffQualificationCertificate.DoesNotExist:
            return make_response(code=-1, msg="员工资质证书不存在")
        except Exception as e:
            return make_response(code=-1, msg=f"删除员工资质证书失败: {str(e)}")
        
        
  
# 员工培训记录列表
class StaffTrainingRecordListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    
    def get(self, request, sid):
        staff_training_records = StaffTrainingRecord.objects.filter(staff__sid=sid,maternity_center=request.user.maternity_center)
        serializer = StaffTrainingRecordDetailSerializer(staff_training_records, many=True)
        return make_response(code=0, msg="获取员工培训记录列表成功", data=serializer.data)  
    
# 新增员工培训记录
class StaffTrainingRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def post(self, request, sid):
        
        data = request.data.copy()
        
        staff = Staff.get_staff_by_sid(sid=sid,maternity_center=request.user.maternity_center)
        
        if not staff:
            return make_response(code=-1, msg="员工不存在")
        
        data['staff'] = staff.id
        
        attachment = data.get("attachment")
        if attachment:
            if not StaffTrainingRecordAttachment.check_rids(attachment,request.user.maternity_center):
                return make_response(code=-1,msg="文件不存在")
        
        serializer = StaffTrainingRecordCreateSerializer(data=data)
        if serializer.is_valid():
            staff_training_record = serializer.save(
                maternity_center=request.user.maternity_center,
            )
            return make_response(code=0, msg="新增员工培训记录成功", data=StaffTrainingRecordDetailSerializer(staff_training_record).data)
        return make_response(code=-1, msg=serializer.errors)
    
    
# 更新员工培训记录
class StaffTrainingRecordUpdateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT

    def put(self, request, rid):
        try:
            training_record = StaffTrainingRecord.objects.get(
                rid=rid,
                maternity_center=request.user.maternity_center
            )
        except StaffTrainingRecord.DoesNotExist:
            return make_response(code=-1, msg="员工培训记录不存在")

        data = request.data.copy()
        attachment = data.get("attachment")
        if attachment:
            if not StaffTrainingRecordAttachment.check_rids(attachment,request.user.maternity_center):
                return make_response(code=-1,msg="文件不存在")

        serializer = StaffTrainingRecordUpdateSerializer(training_record, data=request.data)
        if serializer.is_valid():
            updated_record = serializer.save()
            return make_response(
                code=0,
                msg="更新员工培训记录成功",
                data=StaffTrainingRecordDetailSerializer(updated_record).data
            )
        return make_response(code=-1, msg=serializer.errors)
    
    
# 删除员工培训记录
class StaffTrainingRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def delete(self, request, rid):
        try:
            staff_training_record = StaffTrainingRecord.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            staff_training_record.delete()
            return make_response(code=0, msg="删除员工培训记录成功")
        except StaffTrainingRecord.DoesNotExist:
            return make_response(code=-1, msg="员工培训记录不存在")
        except Exception as e:
            return make_response(code=-1, msg=f"删除员工培训记录失败: {str(e)}")
        

# 员工考核记录列表
class StaffAssessmentRecordListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    
    def get(self, request, sid):
        staff_assessment_records = StaffAssessmentRecord.objects.filter(staff__sid=sid,maternity_center=request.user.maternity_center)
        serializer = StaffAssessmentRecordDetailSerializer(staff_assessment_records, many=True)
        return make_response(code=0, msg="获取员工考核记录列表成功", data=serializer.data)

# 新增员工考核记录
class StaffAssessmentRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def post(self, request, sid):
        
        data = request.data.copy()
        
        staff = Staff.get_staff_by_sid(sid=sid,maternity_center=request.user.maternity_center)
        
        attachment = data.get("attachment")
        if attachment:
            if not StaffAssessmentRecordAttachment.check_rids(attachment,request.user.maternity_center):
                return make_response(code=-1,msg="文件不存在")
        
        if not staff:
            return make_response(code=-1, msg="员工不存在")
        
        data['staff'] = staff.id
        
        serializer = StaffAssessmentRecordCreateSerializer(data=data)
        if serializer.is_valid():
            staff_assessment_record = serializer.save(
                maternity_center=request.user.maternity_center,
            )
            return make_response(code=0, msg="新增员工考核记录成功", data=StaffAssessmentRecordDetailSerializer(staff_assessment_record).data)
        return make_response(code=-1, msg=serializer.errors)
    

# 更新员工考核记录
class StaffAssessmentRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def put(self, request, rid):
        
        try:
            assessment_record = StaffAssessmentRecord.objects.get(
                rid=rid,
                maternity_center=request.user.maternity_center
            )
        except StaffAssessmentRecord.DoesNotExist:
            return make_response(code=-1, msg="员工考核记录不存在")

        data = request.data.copy()

        attachment = data.get("attachment")
        if attachment:
            if not StaffAssessmentRecordAttachment.check_rids(attachment,request.user.maternity_center):
                return make_response(code=-1,msg="文件不存在")

        serializer = StaffAssessmentRecordUpdateSerializer(assessment_record, data=data)
        if serializer.is_valid():
            updated_record = serializer.save()
            return make_response(
                code=0,
                msg="更新员工培训记录成功",
                data=StaffAssessmentRecordDetailSerializer(updated_record).data
            )
        return make_response(code=-1, msg=serializer.errors)
    

# 删除员工考核记录
class StaffAssessmentRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def delete(self, request, rid):
        try:    
            staff_assessment_record = StaffAssessmentRecord.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            staff_assessment_record.delete()
            return make_response(code=0, msg="删除员工考核记录成功")
        except StaffAssessmentRecord.DoesNotExist:
            return make_response(code=-1, msg="员工考核记录不存在")
        except Exception as e:
            return make_response(code=-1, msg=f"删除员工考核记录失败: {str(e)}")
        
        

# 员工健康检查记录列表
class StaffHealthCheckRecordListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_VIEW
    
    def get(self, request, sid):
        staff_health_check_records = StaffHealthCheckRecord.objects.filter(staff__sid=sid,maternity_center=request.user.maternity_center)
        serializer = StaffHealthCheckRecordDetailSerializer(staff_health_check_records, many=True)
        return make_response(code=0, msg="获取员工健康检查记录列表成功", data=serializer.data)

# 新增员工健康检查记录
class StaffHealthCheckRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def post(self, request, sid):
        
        data = request.data.copy()
        
        staff = Staff.get_staff_by_sid(sid=sid,maternity_center=request.user.maternity_center)
        
        if not staff:
            return make_response(code=-1, msg="员工不存在")
        
        data['staff'] = staff.id
        
        attachment = data.get("attachment")
        if attachment:
            if not StaffHealthCheckRecordAttachment.check_rids(attachment,request.user.maternity_center):
                return make_response(code=-1,msg="文件不存在")
        
        serializer = StaffHealthCheckRecordCreateSerializer(data=data)
        if serializer.is_valid():
            staff_health_check_record = serializer.save(
                maternity_center=request.user.maternity_center,
            )
            return make_response(code=0, msg="新增员工健康检查记录成功", data=StaffHealthCheckRecordDetailSerializer(staff_health_check_record).data)
        return make_response(code=-1, msg=serializer.errors)
    

# 更新员工健康检查记录
class StaffHealthCheckRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def put(self, request, rid):
        try:
            staff_health_check_record = StaffHealthCheckRecord.objects.get(
                rid=rid,
                maternity_center=request.user.maternity_center
            )
        except StaffHealthCheckRecord.DoesNotExist:
            return make_response(code=-1, msg="员工健康检查记录不存在")

        data = request.data.copy()
        
        attachment = data.get("attachment")
        if attachment:
            if not StaffHealthCheckRecordAttachment.check_rids(attachment,request.user.maternity_center):
                return make_response(code=-1,msg="文件不存在")

        serializer = StaffHealthCheckRecordUpdateSerializer(staff_health_check_record, data=data)
        if serializer.is_valid():
            updated_record = serializer.save()
            return make_response(
                code=0,
                msg="更新员工健康检查记录成功",
                data=StaffHealthCheckRecordDetailSerializer(updated_record).data
            )
        return make_response(code=-1, msg=serializer.errors)
    

# 删除员工健康检查记录
class StaffHealthCheckRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.STAFF_EDIT
    
    def delete(self, request, rid):
        try:    
            staff_health_check_record = StaffHealthCheckRecord.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            staff_health_check_record.delete()
            return make_response(code=0, msg="删除员工健康检查记录成功")
        except StaffHealthCheckRecord.DoesNotExist:
            return make_response(code=-1, msg="员工健康检查记录不存在")
        except Exception as e:
            return make_response(code=-1, msg=f"删除员工健康检查记录失败: {str(e)}")