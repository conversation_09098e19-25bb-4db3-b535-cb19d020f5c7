from datetime import datetime, timedelta
from django.db.models import Q
from collections import defaultdict

from organizational_management.staff_schedule.models import Schedule
from organizational_management.staff_schedule.enum import OrgScheduleStatusEnum, ShiftTypeEnum


def validate_staff_availability(details, staff_sids, maternity_center_id):
    """
    校验员工是否可用（存在于指定月子中心且在职）
    区分安全违规和业务错误

    Args:
        details: 保存报错信息
        staff_sids: 员工SID集合
        maternity_center_id: 月子中心ID
        
    Returns:
        tuple: (是否合法请求, 员工列表)
    """
    from user.models import Staff

    # 先查询所有员工，检查是否存在非本月子中心的员工
    all_staff_list = Staff.objects.filter(
        sid__in=staff_sids,
        maternity_center_id=maternity_center_id
    ).values('id', 'sid', 'name', 'is_active')

    found_staff_sids = {staff['sid'] for staff in all_staff_list}
    missing_staff_sids = staff_sids - found_staff_sids
    
    # 请求不存在的员工，非法请求，属于安全违规 # TODO 这个还返回员工不存在吗？
    if missing_staff_sids:
        for sid in missing_staff_sids:
            details[sid]['errors'].append({
                'type': 'staff',
                'msg': f"员工 {sid} 不存在"
            })
        return (False, False, None)
    # 检查员工是否离职
    active_staff_dict = {}
    is_valid_request = True
    for staff in all_staff_list:
        if not staff['is_active']:
            details[staff['sid']]['errors'].append({
                'type': 'staff',
                'msg': f"员工 {staff['name']}(ID:{staff['sid']}) 已离职"
            })
            is_valid_request = False
        else:
            active_staff_dict[staff['sid']] = (staff['id'], staff['name'])
    # TODO 校验员工资质
    return (True, is_valid_request, active_staff_dict)


def validate_single_staff_availability(errors, staff_sid, maternity_center_id):
    """
    校验单个员工是否可用（存在于指定月子中心且在职）
    """
    from user.models import Staff

    staff = Staff.objects.filter(
        sid=staff_sid,
        maternity_center_id=maternity_center_id
    ).first()
    if not staff:
        # 非法请求，属于安全违规
        errors.append({
            'type': 'staff',
            'msg': f"员工 {staff_sid} 不存在"
        })
        return (False, False, None)
    elif not staff.is_active:
        errors.append({
            'type': 'staff',
            'msg': f"员工 {staff_sid} 已离职"
        })
        return (True, False, None)
    # TODO 校验员工资质
    return (True, True, staff)


def validate_shift_type(shift_type):
    """
    校验班次类型
    """
    SHIFT_TYPE_MAPPING = {
        'MORNING': 'MORNING', # 早班
        'AFTERNOON': 'AFTERNOON', # 中班
        'NIGHT': 'NIGHT', # 晚班
        # 兼容旧的命名
        'DAY_SHIFT': 'MORNING', # 早班
        'NIGHT_SHIFT': 'NIGHT', # 夜班
        'EVENING_SHIFT': 'AFTERNOON', # 晚班
        'MORNING_SHIFT': 'MORNING', # 上午班
        'AFTERNOON_SHIFT': 'AFTERNOON', # 下午班
    }
    shift_type = shift_type.upper()
    if shift_type not in SHIFT_TYPE_MAPPING:
        return None
    shift_type = SHIFT_TYPE_MAPPING[shift_type]
    try:
        ShiftTypeEnum(shift_type)
    except ValueError:
        return None
    return shift_type

def validate_status(status):
    """
    校验排班状态
    """
    status = status.upper()
    try:
        OrgScheduleStatusEnum(status)
    except ValueError:
        return None
    return status


def validate_single_schedule_data(errors, data, schedule_date, maternity_center_id, staff):
    """
    校验单个排班数据更新
    Args:
        data: 请求数据
        schedule_date: 排班日期，已校验
        maternity_center_id: 月子中心ID
        staff: 员工对象，已校验
    Returns:
        bool: 是否有效
    """
    shift_type = validate_shift_type(data['shift_type'])
    if shift_type is None:
        errors.append({
            'type': 'shift',
            'msg': f"班次类型 {data['shift_type']} 不存在，有效值为: {', '.join([choice.value for choice in ShiftTypeEnum])}"
        })
        return False
    is_valid_request = True
    # 校验排班日期是否冲突/有多个班次
    existing_schedules = Schedule.objects.filter(
        maternity_center_id=maternity_center_id,
        staff=staff,
        schedule_date=schedule_date,
    ).values('rid', 'shift_type', 'schedule_date')
    for existing_schedule in existing_schedules:
        if existing_schedule['rid'] == data['rid']:
            continue
        if existing_schedule['shift_type'] == shift_type:
            # 排班冲突
            errors.append({
                'type': 'conflict',
                'msg': [existing_schedule['schedule_date']]
            })
        else:
            # 一天之中有多个班次
            errors.append({
                'type': 'multiple',
                'msg': [existing_schedule['schedule_date']]
            })
        is_valid_request = False
    return is_valid_request


def validate_schedule_data_integrity(details, schedule_configs, start_date, end_date, maternity_center_id, staff_dict):
    """
    校验提交的排班数据完整性
    检查是否存在同一员工在同一天的同一班次被重复安排

    Args:
        details: 保存报错信息
        schedule_configs: 排班配置列表
        start_date: 开始日期obj, 已校验
        end_date: 结束日期obj, 已校验
        maternity_center_id: 月子中心ID
        staff_dict: 员工姓名字典: key是员工sid, value是员工id和姓名, 已经过校验
    """
    from datetime import timedelta

    is_valid_request = True
    # 校验班次类型，保存有效配置
    shift_types = []
    valid_schedule_configs = []
    for config in schedule_configs:
        shift_type = config['shift_type']
        shift_type = validate_shift_type(shift_type)
        if shift_type is None:
            details[config['staff_sid']]['errors'].append({
                'type': 'shift',
                'msg': f"班次类型 {shift_type} 不存在，有效值为: {', '.join([choice.value for choice in ShiftTypeEnum])}"
            })
            is_valid_request = False
            continue
        shift_types.append(shift_type)
        if config['staff_sid'] in staff_dict:
            valid_schedule_configs.append(config)
    # 去重
    shift_types = list(set(shift_types))

    # 生成日期范围
    date_range = []
    current_date = start_date
    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)

    schedule_counter = {}

    # 统计提交数据中的重复安排日期
    duplicate_dates_by_staff_sid = defaultdict(set)
    for config in valid_schedule_configs:
        staff_sid = config['staff_sid']
        shift_type = config['shift_type']
        duplicate_dates = set()
        for date in date_range:
            key = (staff_sid, date, shift_type)
            if key in schedule_counter:
                # 发现重复安排
                schedule_counter[key][0] += 1
                duplicate_dates.add(date)
                is_valid_request = False
            else:
                schedule_counter[key] = [1, 0] # 表示提交数据计数1，数据库计数0
        if duplicate_dates:
            duplicate_dates_by_staff_sid[staff_sid].update(duplicate_dates)

    # 查询数据库中已有的排班
    existing_schedules = Schedule.objects.filter(
        maternity_center_id=maternity_center_id,
        staff__sid__in=staff_dict.keys(),
        schedule_date__range=[start_date, end_date],
        shift_type__in=shift_types
    ).select_related('staff').values(
        'staff__sid', 'staff__name', 'schedule_date', 'shift_type'
    )
    # 统计数据库中已有的排班
    conflict_dates_by_staff_sid = defaultdict(set)
    for schedule in existing_schedules:
        key = (schedule['staff__sid'], schedule['schedule_date'], schedule['shift_type'])
        if key in schedule_counter:
            schedule_counter[key][1] += 1
            conflict_dates_by_staff_sid[schedule['staff__sid']].add(schedule['schedule_date'])
            is_valid_request = False

    # 统计一天之中有多个班次的员工
    multiple_dates_by_staff_sid = defaultdict(set)
    schedule_counter_by_date = {}
    for key, count in schedule_counter.items():
        new_key = (key[0], key[1])
        if new_key not in schedule_counter_by_date:
            schedule_counter_by_date[new_key] = 1
        else:
            # 一天之中有多个班次
            schedule_counter_by_date[new_key] += 1
            if schedule_counter_by_date[new_key] == 2:
                multiple_dates_by_staff_sid[key[0]].add(key[1])
                is_valid_request = False

    # 生成错误信息汇总
    for staff_sid, dates in duplicate_dates_by_staff_sid.items():
        details[staff_sid]['errors'].append({
            'type': 'duplicate',
            'msg': list(dates)
        })
    for staff_sid, dates in conflict_dates_by_staff_sid.items():
        details[staff_sid]['errors'].append({
            'type': 'conflict',
            'msg': list(dates)
        })
    for staff_sid, dates in multiple_dates_by_staff_sid.items():
        details[staff_sid]['errors'].append({
            'type': 'multiple',
            'msg': list(dates)
        })

    return is_valid_request


def validate_schedule_date(schedule_date):
    """
    校验排班日期
    只能安排今天及今天之后的排班
    """
    from datetime import datetime, date
    today = date.today()
    try:
        schedule_date_obj = datetime.strptime(schedule_date, '%Y-%m-%d').date()
    except ValueError:
        return ("日期格式错误，请使用 YYYY-MM-DD 格式", None)
    
    if schedule_date_obj < today:
        return ("日期不能早于今天", None)
    
    return ("", schedule_date_obj)


def validate_schedule_date_range(start_date, end_date):
    """
    校验排班日期范围
    只能安排今天及今天之后的排班

    Args:
        start_date: 开始日期 (str: "2025-05-27")
        end_date: 结束日期 (str: "2025-06-17")

    Returns:
        tuple: (是否有效, 错误信息, 开始日期, 结束日期)
    """
    from datetime import datetime, date

    # 获取今天的日期
    today = date.today()

    # 转换日期格式
    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        return (False, "日期格式错误，请使用 YYYY-MM-DD 格式", None, None)
    
    error_msg = ""
    # 检查开始日期不能小于今天
    if start_date_obj < today:
        error_msg += f"开始日期 {start_date} 不能早于今天 {today.strftime('%Y-%m-%d')}; "

    # 检查结束日期不能小于开始日期
    if end_date_obj < start_date_obj:
        error_msg += f"结束日期 {end_date} 不能早于开始日期 {start_date}; "

    # 检查结束日期不能小于今天
    if end_date_obj < today:
        error_msg += f"结束日期 {end_date} 不能早于今天 {today.strftime('%Y-%m-%d')};"

    return (error_msg == "", error_msg, start_date_obj, end_date_obj)


def validate_schedule_rid(errors, rid, maternity_center_id):
    """
    校验排班rid是否存在
    """
    schedule = Schedule.objects.filter(
        maternity_center_id=maternity_center_id,
        rid=rid
    ).first()
    if not schedule:
        errors.append({
            'type': 'shift',
            'msg': f"排班 {rid} 不存在"
        })
        return None
    return schedule


def comprehensive_single_schedule_validation(data, is_valid_request, schedule_date, outer_original_schedule_list=None, outer_staff_list=None):
    """
    综合排班校验函数，日期范围已经校验过
    区分数据校验失败（安全违规）和业务校验失败
    
    Args:
        data: 请求数据
        is_valid_request: 是否有效请求，初始传入日期校验结果
        schedule_date: 排班日期，已校验
        outer_original_schedule_list: 传出参数，原排班对象，如果校验不通过，则不返回
        outer_staff_list: 传出参数，员工对象，如果校验不通过，则不返回

    Returns:
        dict: {
            "is_valid": bool,               # 整体校验是否通过
            "validation_type": str,         # "success" | "business_error" | "security_violation"
            "is_legitimate_request": bool,  # 是否为正常合法请求
            "errors": [
                {
                    "type": str,            # 错误类型: staff(员工错误)、conflict(排班冲突)、multiple(一天排了两个以上的班)、shift(班次类型错误)、hours(工时过高)（暂时没有）
                    "msg": str | list,      # 员工错误（不存在/已离职/没资质）表示错误信息，排班冲突/双重排班 表示日期列表
                },
            ]
    """
    maternity_center_id = data['maternity_center']
    staff_sid = data['staff_sid']

    errors = []
    # 0. 校验员工可用性（即使日期校验不通过）
    is_legitimate_request, is_valid_staff, staff = validate_single_staff_availability(errors, staff_sid, maternity_center_id)
    # 非法请求，直接返回
    if not is_legitimate_request:
        return {
            "is_valid": False,
            "validation_type": "security_violation",
            "is_legitimate_request": False,
            "errors": errors
        }
    # 1. 校验排班rid是否存在
    original_schedule = validate_schedule_rid(errors, data['rid'], maternity_center_id)
    # 非法请求，直接返回
    if not original_schedule:
        return {
            "is_valid": False,
            "validation_type": "security_violation",
            "is_legitimate_request": False,
            "errors": errors
        }
    # 校验排班状态
    if 'status' in data:
        status = validate_status(data['status'])
        if status is None:
            errors.append({
                'type': 'shift',
                'msg': f"排班状态 {data['status']} 不存在，有效值为: {', '.join([choice.value for choice in OrgScheduleStatusEnum])}"
            })
            is_valid_request = False
        else:
            data['status'] = status
    # 不能修改昨天及之前的排班
    from datetime import date
    if original_schedule.schedule_date < date.today():
        errors.append({
            'type': 'shift',
            'msg': f"排班 {original_schedule.rid} 不能修改昨天及之前的排班"
        })
        return {
            "is_valid": False,
            "validation_type": "business_error",
            "is_legitimate_request": True,
            "errors": errors
        }
    # 2. 日期/状态校验不通过，则不进行后续校验
    is_valid_request = is_valid_request and is_valid_staff
    if not is_valid_request:
        return {
            "is_valid": False,
            "validation_type": "business_error",
            "is_legitimate_request": True,
            "errors": errors
        }
    # 3. 校验排班数据
    is_valid_request = is_valid_request and validate_single_schedule_data(errors, data, schedule_date, maternity_center_id, staff)
    if is_valid_request:
        if outer_original_schedule_list is not None:
            outer_original_schedule_list.append(original_schedule)
        if outer_staff_list is not None:
            outer_staff_list.append(staff)
    return {
        "is_valid": is_valid_request,
        "validation_type": "business_error" if not is_valid_request else "success",
        "is_legitimate_request": is_legitimate_request,
        "errors": errors
    }


def comprehensive_schedule_validation(data, is_valid_date, start_date, end_date, outer_staff_dict=None):
    """
    综合排班校验函数，日期范围已经校验过
    区分数据校验失败（安全违规）和业务校验失败

    Args:
        data: 完整的排班数据
        is_valid_date: 日期是否有效
        start_date: 开始日期
        end_date: 结束日期
        outer_staff_dict: 传出参数，员工姓名字典: key是员工sid, value是员工id和姓名, 如果校验不通过，则不返回

    Returns:
        dict: {
            "is_valid": bool,                   # 整体校验是否通过
            "validation_type": str,             # "success" | "business_error" | "security_violation"
            "is_legitimate_request": bool,      # 是否为正常合法请求
            "details": {                        # 详细的分类校验结果
                "ST2025071122189254848382510": {# 员工的sid
                    “errors”: [
                        {
                            "type": str,        # 错误类型: staff(员工错误)、duplicate(提交数据排班重复)、conflict(排班冲突)、multiple(一天排了两个以上的班)、shift(班次类型错误)、hours(工时过高)（暂时没有）
                            "msg": str | list,  # 员工错误（不存在/已离职/没资质）表示错误信息，提交重复/冲突/双重排班 表示日期列表
                        },
                    ],
                    "week_hours": number,       # 周工时(暂时没有)
                    "month_hours": number,      # 月工时(暂时没有)
                },
                "ST2025071109044598074970813": {...}
            }
        }
    """
    maternity_center_id = data['maternity_center']
    schedule_configs = data['schedules']

    staff_sids = set(config['staff_sid'] for config in schedule_configs)
    details = {}
    for staff_sid in staff_sids:
        details[staff_sid] = {
            'errors': [],
        }
    # 0. 校验员工可用性（即使日期校验不通过）
    # staff_dict: sid, id, name
    is_legitimate_request, is_valid_request, staff_dict = validate_staff_availability(details, staff_sids, maternity_center_id)
    # 非法请求，直接返回
    if not is_legitimate_request:
        return {
            "is_valid": False,  
            "validation_type": "security_violation",
            "is_legitimate_request": False,
            "details": details
        }
    # 1. 日期校验不通过，则不进行后续校验
    if not is_valid_date:
        return {
            "is_valid": False,
            "validation_type": "business_error",
            "is_legitimate_request": True,
            "details": details
        }

    # 2. 校验数据完整性（检查重复/冲突/一天之中多次的排班）
    is_valid_request = is_valid_request and validate_schedule_data_integrity(details, schedule_configs, start_date, end_date, maternity_center_id, staff_dict)
    if is_valid_request and outer_staff_dict is not None:
        outer_staff_dict.update(staff_dict)

    return {
        "is_valid": is_valid_request,
        "validation_type": "business_error" if not is_valid_request else "success",
        "is_legitimate_request": is_legitimate_request,
        "details": details
    }