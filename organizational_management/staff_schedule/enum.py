from django.db import models


class ShiftTypeEnum(models.TextChoices):
    # 早班
    MORNING = 'MORNING', '早班'
    # 中班
    AFTERNOON = 'AFTERNOON', '中班'
    # 晚班
    NIGHT = 'NIGHT', '晚班'


# 排班状态
class OrgScheduleStatusEnum(models.TextChoices):
    # 待上岗
    PENDING = 'PENDING', '待上岗'
    # 在岗中  
    ON_DUTY = 'ON_DUTY', '在岗中'
    # 已下岗
    OFF_DUTY = 'OFF_DUTY', '已下岗'


class ScheduleStatusEnum(models.TextChoices):
    # 待接班
    PENDING = 'PENDING', '待接班'
    # 已接班
    ACCEPTED = 'ACCEPTED', '已接班'
    # 已下班
    FINISHED = 'FINISHED', '已下班'


class ScheduleDetailStatusEnum(models.TextChoices):
    # 待确认
    PENDING = 'PENDING', '待确认'
    # 已确认
    CONFIRMED = 'CONFIRMED', '已确认'
    # 已拒绝
    REJECTED = 'REJECTED', '已拒绝'

class HandoverStatusEnum(models.TextChoices):
    # 草稿
    DRAFT = 'DRAFT', '草稿'
    # 待确认
    PENDING = 'PENDING', '待确认'
    # 已确认
    CONFIRMED = 'CONFIRMED', '已确认'
    # 已拒绝
    REJECTED = 'REJECTED', '已拒绝'

# 培训结果
class TrainingResultEnum(models.TextChoices):
    # 合格
    PASS = 'PASS', '合格'
    # 不合格
    FAIL = 'FAIL', '不合格'
    # 缺勤
    ABSENT = 'ABSENT', '缺勤'
    
