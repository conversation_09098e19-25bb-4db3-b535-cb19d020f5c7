import calendar
from datetime import datetime, timedelta
from django.core.management import BaseCommand
from django.utils import timezone

from organizational_management.charge.models import MaternityCostInfo




class Command(BaseCommand):
    help = '重置支付方式数据格式'

    def handle(self, *args, **options):
        maternity_cost_info = MaternityCostInfo.objects.all()
        for info in maternity_cost_info:
            info.payment_method = [info.payment_method]
            info.save()