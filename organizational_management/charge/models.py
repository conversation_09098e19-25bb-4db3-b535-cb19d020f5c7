from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver

from core.generate_hashid import generate_bill_number, generate_renew_bill_number, generate_resource_uuid
from core.model import BaseModel, JSONListValidator
from customer_service.core_records.models.maternity_admission import MaternityAdmission, MaternityAdmissionRenew
from customer_service.room.models import Room
from maternity_center.models import MaternityCenter
from organizational_management.charge.enum import PaymentMethodEnum, PaymentStatusEnum, PackageStatusEnum
from user.models import Maternity
from dirtyfields import DirtyFieldsMixin


# 套餐管理
class Package(DirtyFieldsMixin, BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心")
    # 套餐名称
    name = models.CharField(max_length=100, verbose_name="套餐名称")
    # 套餐描述
    description = models.TextField(verbose_name="套餐描述", blank=True, default="")
    # 套餐价格
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="套餐价格", default=0)
    # 套餐状态
    status = models.CharField(max_length=20, verbose_name="套餐状态", choices=PackageStatusEnum.choices, default=PackageStatusEnum.ACTIVE)
    # 套餐天数
    stay_days = models.IntegerField(verbose_name="套餐天数", help_text="产妇可在院停留的天数", default=28)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    
    class Meta:
        verbose_name = "套餐"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name
    
    @staticmethod
    def create_package(maternity_center, name, description, price, stay_days, status=PackageStatusEnum.ACTIVE):
        package = Package.objects.create(maternity_center=maternity_center, name=name, description=description, price=price, stay_days=stay_days, status=status)
        return package
    
    @staticmethod
    def get_package_by_rid(rid,maternity_center):
        try:
            return Package.objects.get(rid=rid,maternity_center=maternity_center)
        except Package.DoesNotExist:
            return None
    
    
# 产妇入院费用信息管理
class MaternityCostInfo(DirtyFieldsMixin, BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心")
    # 入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入院记录",related_name="maternity_cost_info",null=True, blank=True)
    # 套餐
    package = models.ForeignKey(Package, on_delete=models.SET_NULL, verbose_name="套餐", null=True, blank=True, related_name="maternity_cost_info")
    # 套餐价格
    package_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="套餐价格", default=0)
    # 押金金额
    deposit_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="押金金额")
    # 定金金额
    earnest_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="定金金额")
    # 应付金额
    payable_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="应付金额")
    # 已付金额
    paid_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="已付金额")
    # 剩余尾款
    remaining_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="剩余尾款")
    # 支付方式
    payment_method = models.JSONField( verbose_name="支付方式", validators=[JSONListValidator(PaymentMethodEnum.choices)],default=list)
    # 备注
    remark = models.TextField(verbose_name="备注", blank=True, default="")
    # 支付状态
    payment_status = models.CharField(max_length=20, verbose_name="支付状态", choices=PaymentStatusEnum.choices, default=PaymentStatusEnum.UNPAID)
    # 结算单号
    bid = models.CharField(max_length=100, verbose_name="结算单号", blank=True, default=generate_bill_number)
    
    
    class Meta:
        verbose_name = "产妇费用信息"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.maternity_admission.maternity.name} - {self.package.name}"
    
    @staticmethod
    def get_maternity_cost_info_by_bid(bid,maternity_center):
        try:
            return MaternityCostInfo.objects.get(bid=bid,maternity_center=maternity_center)
        except MaternityCostInfo.DoesNotExist:
            return None

# 续住费用信息
class MaternityRenewCostInfo(DirtyFieldsMixin, BaseModel):
    # 续住入院记录
    maternity_admission_renew = models.ForeignKey(MaternityAdmissionRenew, on_delete=models.CASCADE, verbose_name="续住入院记录",related_name="maternity_renew_cost_info")
    # 产妇入院费用
    maternity_cost_info = models.ForeignKey(MaternityCostInfo, on_delete=models.CASCADE, verbose_name="产妇入院费用信息",related_name="maternity_renew_cost_info")
    # 续住天数
    renew_days = models.IntegerField(verbose_name="续住天数")
    # 续住费用
    renew_cost = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="续住费用", default=0)
    # 续住费用备注
    renew_cost_remark = models.TextField(verbose_name="续住费用备注", blank=True, default="")
    # 续住单号
    rbid = models.CharField(max_length=100, verbose_name="续住单号", blank=True, default=generate_renew_bill_number)
    
    class Meta:
        verbose_name = "续住费用信息"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.maternity_cost_info.maternity.name} - {self.renew_days}天"
    
    @staticmethod
    def get_maternity_renew_cost_info_by_rbid(rbid,maternity_center):
        try:
            return MaternityRenewCostInfo.objects.get(rbid=rbid,maternity_cost_info__maternity_center=maternity_center)
        except MaternityRenewCostInfo.DoesNotExist:
            return None
    
# 续住费用信息新建后
@receiver(post_save, sender=MaternityRenewCostInfo)
def create_maternity_renew_cost_info(sender, instance, created, **kwargs):
    if created:
        instance.maternity_cost_info.remaining_amount = instance.maternity_cost_info.remaining_amount + instance.renew_cost
        instance.maternity_cost_info.save()
