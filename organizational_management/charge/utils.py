from datetime import timedelta
import calendar
from decimal import Decimal
from django.db.models import Count
from django.utils import timezone
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from organizational_management.charge.enum import PaymentStatusEnum
from organizational_management.charge.models import MaternityCostInfo
from organizational_management.charge.serializers import MaternityCostInfoCreateSerializer
from core.resp import make_response

def calculate_payment_status(remaining_amount, payable_amount):
    remaining = float(remaining_amount or 0)
    payable = float(payable_amount or 0)

    if remaining == 0:
        return PaymentStatusEnum.FULL_PAID
    elif remaining < payable:
        return PaymentStatusEnum.PARTIAL_PAID
    else:
        return PaymentStatusEnum.UNPAID

def create_maternity_cost_info(maternity_admission, package, cost_info_data,maternity_center):
    
    cis = MaternityCostInfoCreateSerializer(data=cost_info_data)
    
    if cis.is_valid():
        cost_info_data = cis.validated_data
        remaining_amount = cost_info_data.get('remaining_amount', 0)
        payable_amount = cost_info_data.get('payable_amount', 0)
        payment_status = calculate_payment_status(remaining_amount, payable_amount)
        
        cis.save(
            maternity_admission=maternity_admission,
            payment_status=payment_status,
            package=package,
            package_price=package.price,
            maternity_center=maternity_center
        )
        return None
    else:
        return cis.errors
        

def get_overview_data(request):
    try:
        from django.db.models import Q
        from customer_service.room.models import Room
        from core.enum import CheckInStatusEnum

        now = timezone.now()
        current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        # 计算上个月的开始和结束日期
        if current_month_start.month == 1:
            last_month_start = current_month_start.replace(year=current_month_start.year - 1, month=12)
        else:
            last_month_start = current_month_start.replace(month=current_month_start.month - 1)

        last_month_end = current_month_start - timedelta(days=1)
        maternity_center = request.user.maternity_center

        # 生成最近6个月的日期范围（包括当前月和上个月）
        def get_month_range(base_date, months_back):
            """获取指定月份前的月份范围"""
            year = base_date.year
            month = base_date.month - months_back

            while month <= 0:
                month += 12
                year -= 1

            # 使用 timezone.make_aware 创建带时区的 datetime
            from datetime import datetime
            month_start_naive = datetime(year, month, 1)
            month_start = timezone.make_aware(month_start_naive)

            # 获取该月最后一天
            last_day = calendar.monthrange(year, month)[1]
            month_end_naive = datetime(year, month, last_day, 23, 59, 59)
            month_end = timezone.make_aware(month_end_naive)

            return month_start, month_end

        # 生成所有需要的月份范围
        month_ranges = []
        for i in range(6):
            month_start, month_end = get_month_range(current_month_start, i)
            month_ranges.append((month_start, month_end, i))

        # 一次性查询所有收入数据（最近6个月）

        # 构建收入查询的条件
        income_conditions = Q()
        for month_start, month_end, _ in month_ranges:
            income_conditions |= Q(created_at__gte=month_start, created_at__lte=month_end)

        # 一次性查询所有收入数据
        all_income_data = MaternityCostInfo.objects.filter(
            maternity_center=maternity_center,
            payment_status__in=[PaymentStatusEnum.FULL_PAID, PaymentStatusEnum.PARTIAL_PAID]
        ).filter(income_conditions).values('created_at', 'paid_amount')

        # 按月份分组收入数据
        income_by_month = {}
        for month_start, month_end, _ in month_ranges:
            month_key = month_start.strftime('%Y-%m')
            income_by_month[month_key] = Decimal('0')

        for income_record in all_income_data:
            created_at = income_record['created_at']
            paid_amount = income_record['paid_amount'] or Decimal('0')

            # 找到对应的月份
            for month_start, month_end, _ in month_ranges:
                if month_start <= created_at <= month_end:
                    month_key = month_start.strftime('%Y-%m')
                    income_by_month[month_key] += paid_amount
                    break

        # 获取当前月和上月收入
        current_month_key = current_month_start.strftime('%Y-%m')
        last_month_key = last_month_start.strftime('%Y-%m')

        current_month_income = income_by_month.get(current_month_key, Decimal('0'))
        last_month_income = income_by_month.get(last_month_key, Decimal('0'))

        print(current_month_income,last_month_income)

        # 收入变化率计算
        if last_month_income > 0:
            income_change_rate = float((current_month_income - last_month_income) / last_month_income * 100)
        else:
            income_change_rate = "暂无数据"

        # 一次性查询入住数据（当前月和上月）
        admissions_data = MaternityAdmission.objects.filter(
            maternity_center=maternity_center,
            actual_check_in_date__gte=last_month_start.date()
        ).values('actual_check_in_date')

        current_month_admissions = 0
        last_month_admissions = 0

        for admission in admissions_data:
            check_in_date = admission['actual_check_in_date']
            if check_in_date >= current_month_start.date():
                current_month_admissions += 1
            elif last_month_start.date() <= check_in_date <= last_month_end.date():
                last_month_admissions += 1

        # 入住人数变化率计算
        if last_month_admissions > 0:
            admissions_change_rate = float((current_month_admissions - last_month_admissions) / last_month_admissions * 100)
        else:
            admissions_change_rate = "暂无数据"

        # 房间使用率统计 - 优化查询
        total_rooms = Room.objects.filter(maternity_center=maternity_center).count()

        if total_rooms > 0:
            # 一次性查询房间使用数据
            room_usage_data = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                actual_check_in_date__gte=last_month_start.date(),
                check_in_status__in=[CheckInStatusEnum.CHECKED_IN, CheckInStatusEnum.CHECKED_OUT],
                room__isnull=False
            ).values('room_id', 'actual_check_in_date').distinct()

            current_month_used_rooms = set()
            last_month_used_rooms = set()

            for usage in room_usage_data:
                room_id = usage['room_id']
                check_in_date = usage['actual_check_in_date']

                if check_in_date >= current_month_start.date():
                    current_month_used_rooms.add(room_id)
                elif last_month_start.date() <= check_in_date <= last_month_end.date():
                    last_month_used_rooms.add(room_id)

            current_month_usage_rate = float(len(current_month_used_rooms) / total_rooms * 100)
            last_month_usage_rate = float(len(last_month_used_rooms) / total_rooms * 100)
            usage_rate_change = current_month_usage_rate - last_month_usage_rate
        else:
            current_month_usage_rate = 0
            usage_rate_change = "暂无数据"

        # 构建收入趋势数据
        income_trends = []
        for month_start, month_end, _ in reversed(month_ranges):  # 按时间正序
            month_key = month_start.strftime('%Y-%m')
            month_key_display = f'{month_start.strftime('%-m')}月'
            income_trends.append({
                'month': month_key_display,
                'amount': float(income_by_month.get(month_key, Decimal('0')))
            })

        # 套餐分布统计 - 使用预加载
        package_stats = MaternityCostInfo.objects.filter(
            maternity_center=maternity_center,
            package__isnull=False
        ).select_related('package').values('package__name').annotate(
            count=Count('id')
        ).order_by('-count')

        total_package_orders = sum(item['count'] for item in package_stats)
        package_distribution = []

        for item in package_stats:
            percentage = float(item['count'] / total_package_orders * 100) if total_package_orders > 0 else 0
            package_distribution.append({
                'package_name': item['package__name'],
                'count': item['count'],
                'percentage': percentage
            })

        data = {
            'recently_bill': build_recently_bill_data(request),
            'monthly_income': {
                'current_month': float(current_month_income),
                'change_rate': income_change_rate
            },
            'monthly_admissions': {
                'current_month': current_month_admissions,
                'change_rate': admissions_change_rate
            },
            'room_usage_rate': {
                'current_month': current_month_usage_rate,
                'change_rate': usage_rate_change
            },
            'income_trends': income_trends,
            'package_distribution': package_distribution
        }

        return make_response(code=0, msg="获取总览成功", data=data)

    except Exception as e:
        return make_response(code=-1, msg=f"获取总览失败: {str(e)}")
    

# 最后 7 条结算单
def build_recently_bill_data(request):
    
    bills = MaternityCostInfo.objects.select_related('maternity_admission__maternity','maternity_admission__room','package').filter(maternity_center=request.user.maternity_center).order_by('-created_at')[:7]
    
    if not bills:
        return []
    
    data = []
    
    for bill in bills:
        
        data.append({
            'bid': bill.bid,   
            # 交易日期
            'transaction_date': bill.created_at.strftime('%Y-%m-%d'),
            # 产妇名字
            'maternity_name': bill.maternity_admission.maternity.name if bill.maternity_admission.maternity else 'N/A',
            # 房间号
            'room_number': bill.maternity_admission.room.room_number if bill.maternity_admission.room else 'N/A',
            # 套餐名字
            'package_name': bill.package.name if bill.package else 'N/A',
            # 总金额
            'total_amount': bill.payable_amount,
            # 支付方式
            'payment_method': bill.payment_method,
            # 支付状态
            'payment_status': bill.payment_status,
        })
    
    return data