from django.contrib import admin
from django import forms
from django.forms.widgets import CheckboxSelectMultiple
from django.utils.html import format_html

from organizational_management.charge.models import MaternityCostInfo, Package
from organizational_management.charge.enum import PaymentMethodEnum, PaymentStatusEnum


class PaymentMethodWidget(CheckboxSelectMultiple):
    """支付方式多选框组件"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.choices = PaymentMethodEnum.choices

    def format_value(self, value):
        """格式化值，确保返回列表"""
        if value is None:
            return []
        if isinstance(value, str):
            try:
                import json
                return json.loads(value)
            except (json.JSONDecodeError, ValueError):
                return []
        if isinstance(value, list):
            return value
        return []


class MaternityCostInfoForm(forms.ModelForm):
    """产妇费用信息表单"""

    payment_method = forms.MultipleChoiceField(
        choices=PaymentMethodEnum.choices,
        widget=PaymentMethodWidget(),
        required=False,
        label="支付方式",
        help_text="可选择多种支付方式"
    )

    class Meta:
        model = MaternityCostInfo
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 设置字段样式和帮助文本
        self.fields['package_price'].help_text = "套餐的原价格"
        self.fields['deposit_amount'].help_text = "客户支付的押金金额"
        self.fields['earnest_amount'].help_text = "客户支付的定金金额"
        self.fields['payable_amount'].help_text = "客户应该支付的总金额"
        self.fields['paid_amount'].help_text = "客户已经支付的金额"
        self.fields['remaining_amount'].help_text = "客户还需要支付的尾款"

        # 如果是编辑模式，设置支付方式的初始值
        if self.instance and self.instance.pk:
            payment_methods = self.instance.payment_method
            if isinstance(payment_methods, list):
                self.fields['payment_method'].initial = payment_methods

    def clean_payment_method(self):
        """验证支付方式"""
        payment_methods = self.cleaned_data.get('payment_method', [])

        # 验证每个选择的支付方式都是有效的
        valid_choices = [choice[0] for choice in PaymentMethodEnum.choices]
        for method in payment_methods:
            if method not in valid_choices:
                raise forms.ValidationError(f"无效的支付方式: {method}")

        return payment_methods



@admin.register(Package)
class PackageAdmin(admin.ModelAdmin):
    list_display = ['name', 'price', 'description', 'stay_days', 'status', 'created_at']
    list_filter = ['status', 'maternity_center', 'created_at']
    search_fields = ['name', 'description', 'rid']
    readonly_fields = ['rid', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('maternity_center', 'name', 'description', 'status')
        }),
        ('套餐详情', {
            'fields': ('price', 'stay_days')
        }),
        ('系统信息', {
            'fields': ('rid', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity_center')


@admin.register(MaternityCostInfo)
class MaternityCostInfoAdmin(admin.ModelAdmin):
    form = MaternityCostInfoForm

    list_display = [
        'bid', 'maternity_admission_info', 'package_info', 'payable_amount',
        'paid_amount', 'remaining_amount', 'payment_status_display',
        'payment_methods_display', 'created_at'
    ]

    list_filter = [
        'payment_status', 'maternity_center', 'created_at',
        ('package', admin.RelatedOnlyFieldListFilter)
    ]

    search_fields = [
        'bid', 'maternity_admission__maternity__name',
        'maternity_admission__maternity__phone', 'package__name'
    ]

    readonly_fields = ['bid', 'created_at', 'updated_at']

    fieldsets = (
        ('关联信息', {
            'fields': ('maternity_center', 'maternity_admission', 'package')
        }),
        ('套餐费用', {
            'fields': ('package_price',)
        }),
        ('费用明细', {
            'fields': ('deposit_amount', 'earnest_amount', 'payable_amount', 'paid_amount', 'remaining_amount')
        }),
        ('支付信息', {
            'fields': ('payment_method', 'payment_status')
        }),
        ('其他信息', {
            'fields': ('remark',)
        }),
        ('系统信息', {
            'fields': ('bid', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'maternity_center', 'maternity_admission', 'maternity_admission__maternity', 'package'
        )

    def maternity_admission_info(self, obj):
        """显示入院记录信息"""
        if obj.maternity_admission:
            maternity = obj.maternity_admission.maternity
            return format_html(
                '<strong>{}</strong><br/>手机: {}',
                maternity.name,
                maternity.phone
            )
        return '-'
    maternity_admission_info.short_description = '产妇信息'

    def package_info(self, obj):
        """显示套餐信息"""
        if obj.package:
            return format_html(
                '<strong>{}</strong><br/>价格: ¥{}',
                obj.package.name,
                obj.package.price
            )
        return '-'
    package_info.short_description = '套餐信息'

    def payment_status_display(self, obj):
        """显示支付状态（带颜色）"""
        status_colors = {
            PaymentStatusEnum.FULL_PAID: 'green',
            PaymentStatusEnum.PARTIAL_PAID: 'orange',
            PaymentStatusEnum.UNPAID: 'red',
            PaymentStatusEnum.REFUNDED: 'gray'
        }
        color = status_colors.get(obj.payment_status, 'black')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_payment_status_display()
        )
    payment_status_display.short_description = '支付状态'

    def payment_methods_display(self, obj):
        """显示支付方式"""
        if not obj.payment_method:
            return '-'

        method_dict = dict(PaymentMethodEnum.choices)
        methods = [method_dict.get(method, method) for method in obj.payment_method]
        return ', '.join(methods)
    payment_methods_display.short_description = '支付方式'