from django.db import models

# 支付方式枚举
class PaymentMethodEnum(models.TextChoices):
    # 现金
    CASH = "CASH", "现金"
    # 银行卡
    BANK_CARD = "BANK_CARD", "银行卡"
    # 信用卡
    CREDIT_CARD = "CREDIT_CARD", "信用卡"
    # 微信
    WECHAT_PAY = "WECHAT_PAY", "微信支付"
    # 支付宝
    ALIPAY_PAY = "ALIPAY_PAY", "支付宝支付"
    # 其他
    OTHER = "OTHER", "其他"
    

# 支付状态枚举
class PaymentStatusEnum(models.TextChoices):
    # 已全款支付
    FULL_PAID = "FULL_PAID", "已全款支付"
    # 已部分支付
    PARTIAL_PAID = "PARTIAL_PAID", "已部分支付"
    # 未支付
    UNPAID = "UNPAID", "未支付"
    # 已退款
    REFUNDED = "REFUNDED", "已退款"
    
# 套餐状态枚举
class PackageStatusEnum(models.TextChoices):
    # 已启用
    ACTIVE = "ACTIVE", "已启用"
    # 已停用
    DISABLED = "DISABLED", "已停用"