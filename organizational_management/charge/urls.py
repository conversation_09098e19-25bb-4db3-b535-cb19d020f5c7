from django.urls import path

from .views import *

urlpatterns = [
    # 套餐列表
    path('packages/list/', PackageListView.as_view(), name='package-list'),
    # 创建套餐
    path('packages/create/', PackageCreateView.as_view(), name='package-create'),
    # # 更新套餐
    path('packages/update/<str:rid>/', PackageUpdateView.as_view(), name='package-update'),
    # 删除套餐
    path('packages/delete/<str:rid>/', PackageDeleteView.as_view(), name='package-delete'),
    
    
    # 结算单列表
    path('bills/list/', BillListView.as_view(), name='bill-list'),
    # 结算单详情
    path('bills/detail/<str:bid>/', BillDetailView.as_view(), name='bill-detail'),
    # 更新结算单
    path('bills/update/<str:bid>/', BillUpdateView.as_view(), name='bill-update'),
    
    
    # 续住费用信息列表
    path('renew/list/', RenewCostInfoListView.as_view(), name='renew-cost-info-list'),
    # 更新续住费用信息
    path('renew/update/<str:rbid>/', RenewCostInfoUpdateView.as_view(), name='renew-cost-info-update'),
    
    # 总览
    path('charge/overview/', OverviewView.as_view(), name='overview'),

] 