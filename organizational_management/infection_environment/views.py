from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.resp import make_response
from core.view import PaginationListBaseView
from file.models import InfectionManagementFile
from organizational_management.infection_environment.enum import ReportStatusEnum
from organizational_management.infection_environment.models import InfectionDetectionAndReport, InfectionDocument
from organizational_management.infection_environment.serializers import InfectionDetectionAndReportCreateSerializer, \
    InfectionDetectionAndReportDetailSerializer, InfectionDetectionAndReportListSerializer, \
    InfectionDetectionAndReportUpdateSerializer, InfectionDocumentCreateSerializer, InfectionDocumentDetailSerializer, \
    InfectionDocumentListSerializer, InfectionDocumentUpdateSerializer
from permissions.enum import PermissionEnum


# 制度文档列表
class DocumentListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_VIEW
    serializer_class = InfectionDocumentListSerializer
    response_msg = "获取院感制度文档列表成功"
    error_response_msg = ""
    search_fields = ["name"]
    
    def get_queryset(self):
        return InfectionDocument.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
    
    

# 院感制度文档详情
class DocumentDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_VIEW

    def get(self,request,rid):
        infection_document = InfectionDocument.get_infection_document_by_rid(rid,request.user.maternity_center)
        
        if not infection_document:
            return make_response(code=-1,msg="院感制度文档不存在")
        
        serializer = InfectionDocumentDetailSerializer(infection_document)
        
        return make_response(code=0,msg="获取院感制度文档详情成功",data=serializer.data)

# 创建院感制度文档
class DocumentCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    
    def post(self,request):
        
        data = request.data.copy()
        
        name = data.get("name",None)
        
        if InfectionDocument.check_duplicate_name(name,request.user.maternity_center):
            return make_response(code=-1,msg="院感制度文档名称已存在，请重新输入")
        
        file = data.get("file",None)
        
        if file:
            file = InfectionManagementFile.get_by_rid(file,request.user.maternity_center)
            if not file:
                return make_response(code=-1,msg="文件不存在")
        
        data["maternity_center"] = request.user.maternity_center.id
        data["creator"] = request.user.id
        
        serializer = InfectionDocumentCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg="创建院感制度文档成功",data=InfectionDocumentDetailSerializer(serializer.instance).data)
        return make_response(code=-1,msg="数据校验失败",data=serializer.errors)
        
        
# 更新院感制度文档
class DocumentUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    
    def put(self,request,rid):
        
        data = request.data.copy()
        
        name = data.get("name",None)
        
        if InfectionDocument.check_duplicate_name(name,request.user.maternity_center,exclude_rid=rid):
            return make_response(code=-1,msg="院感制度文档名称已存在，请重新输入")
        
        infection_document = InfectionDocument.get_infection_document_by_rid(rid,request.user.maternity_center)
        
        if not infection_document:
            return make_response(code=-1,msg="院感制度文档不存在")
                        
        serializer = InfectionDocumentUpdateSerializer(infection_document,data=data)

        if serializer.is_valid():
            serializer.save()
            
            return make_response(code=0,msg="更新院感制度文档成功",data=InfectionDocumentDetailSerializer(serializer.instance).data)

# 删除院感制度文档
class DocumentDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    def delete(self,request,rid):
        
        infection_document = InfectionDocument.get_infection_document_by_rid(rid,request.user.maternity_center)
        
        if not infection_document:
            return make_response(code=-1,msg="院感制度文档不存在")
        
        infection_document.delete()
        
        return make_response(code=0,msg="删除院感制度文档成功")
    
    
    
    
    
    
# 感染检测与报告列表
class InfectionDetectionAndReportListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_VIEW
    serializer_class = InfectionDetectionAndReportListSerializer
    response_msg = "获取感染检测与报告列表成功"
    error_response_msg = ""
    search_fields = ["detection_date"]
    
    def get_queryset(self):
        return InfectionDetectionAndReport.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
    
    

# 感染检测与报告详情
class InfectionDetectionAndReportDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_VIEW

    def get(self,request,rid):
        infection_detection_and_report = InfectionDetectionAndReport.get_infection_detection_and_report_by_rid(rid,request.user.maternity_center)
        
        if not infection_detection_and_report:
            return make_response(code=-1,msg="感染检测与报告不存在")
        
        serializer = InfectionDetectionAndReportDetailSerializer(infection_detection_and_report)
        
        return make_response(code=0,msg="获取感染检测与报告详情成功",data=serializer.data)

# 创建感染检测与报告
class InfectionDetectionAndReportCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    
    def post(self,request):
        
        data = request.data.copy()    
        
        status = data.get("report_status")
        
        if status and status not in ReportStatusEnum.values:
            return make_response(code=-1,msg="上报状态不正确")
        
        data["maternity_center"] = request.user.maternity_center.id
        data["creator"] = request.user.id
        
        serializer = InfectionDetectionAndReportCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg="创建感染检测与报告成功",data=InfectionDetectionAndReportDetailSerializer(serializer.instance).data)
        return make_response(code=-1,msg="数据校验失败",data=serializer.errors)
        
        
# 更新感染检测与报告
class InfectionDetectionAndReportUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    
    def put(self,request,rid):
        
        data = request.data.copy()        
        
        infection_detection_and_report = InfectionDetectionAndReport.get_infection_detection_and_report_by_rid(rid,request.user.maternity_center)
        
        if not infection_detection_and_report:
            return make_response(code=-1,msg="感染检测与报告不存在")
                        
        serializer = InfectionDetectionAndReportUpdateSerializer(infection_detection_and_report,data=data)

        if serializer.is_valid():
            serializer.save()
            
            return make_response(code=0,msg="更新感染检测与报告成功",data=InfectionDetectionAndReportDetailSerializer(serializer.instance).data)

# 删除感染检测与报告
class InfectionDetectionAndReportDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    def delete(self,request,rid):
        
        infection_detection_and_report = InfectionDetectionAndReport.get_infection_detection_and_report_by_rid(rid,request.user.maternity_center)
        
        if not infection_detection_and_report:
            return make_response(code=-1,msg="感染检测与报告不存在")
        
        infection_detection_and_report.delete()
        
        return make_response(code=0,msg="删除感染检测与报告成功")