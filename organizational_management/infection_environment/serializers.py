from rest_framework import serializers

# 院感制度文档列表序列化器
from core.parse_time import ShanghaiFriendlyDateTimeField
from file.models import InfectionManagementFile
from organizational_management.infection_environment.models import InfectionDetectionAndReport, InfectionDocument


class InfectionDocumentListSerializer(serializers.ModelSerializer):
    class Meta:
        model = InfectionDocument
        fields = ['rid','name','version','publish_date']
        
        
# 院感制度文档详情序列化器
class InfectionDocumentDetailSerializer(serializers.ModelSerializer):
    # 创建人姓名
    creator_name = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 文件
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = InfectionDocument
        fields = ['rid','name','version','publish_date','description','file','creator_name','created_at','updated_at','file_url']
        
    def get_creator_name(self,obj):
        return obj.creator.name if obj.creator else "N/A"
    
    def get_file_url(self,obj):
        iff = InfectionManagementFile.get_by_rid(obj.file,obj.maternity_center)
        return iff.file.url if iff else None
    
# 创建院感制度文档序列化器
class InfectionDocumentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = InfectionDocument
        fields = ['name','version','publish_date','description','file','creator','maternity_center']
        
        
# 更新院感制度文档序列化器
class InfectionDocumentUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = InfectionDocument
        fields = ['name','version','publish_date','description','file']
        


# 感染检测与报告列表序列化器
class InfectionDetectionAndReportListSerializer(serializers.ModelSerializer):
    
    # 创建人名字
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = InfectionDetectionAndReport
        fields = ['rid','detection_date','detection_content','problem','report_status','processing_result','creator_name']
        
    def get_creator_name(self,obj):
        return obj.creator.name if obj.creator else "N/A"
        
        
# 感染检测与报告详情序列化器
class InfectionDetectionAndReportDetailSerializer(serializers.ModelSerializer):
    # 创建人姓名
    creator_name = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 上报状态
    report_status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = InfectionDetectionAndReport
        fields = ['rid','detection_date','detection_content','problem','report_status','report_status_display','processing_result','detailed_description','creator_name','created_at','updated_at']
        
    def get_creator_name(self,obj):
        return obj.creator.name if obj.creator else "N/A"
    
    def get_report_status_display(self,obj):
        return obj.get_report_status_display()
    
# 创建感染检测与报告序列化器
class InfectionDetectionAndReportCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = InfectionDetectionAndReport
        fields = ['detection_date','detection_content','problem','report_status','processing_result','detailed_description','creator','maternity_center']
        
        
# 更新感染检测与报告序列化器
class InfectionDetectionAndReportUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = InfectionDetectionAndReport
        fields = ['detection_date','detection_content','problem','report_status','processing_result','detailed_description']
        
