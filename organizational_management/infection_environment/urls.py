from django.urls import path

from .views import DocumentListView, DocumentDetailView, DocumentCreateView, DocumentUpdateView, DocumentDeleteView, \
    InfectionDetectionAndReportListView, InfectionDetectionAndReportDetailView, InfectionDetectionAndReportCreateView, \
    InfectionDetectionAndReportUpdateView, InfectionDetectionAndReportDeleteView

urlpatterns = [
    # 院感制度文档列表
    path('infection-document/list/', DocumentListView.as_view(), name='infection_document_list'),
    # 院感制度文档详情
    path('infection-document/detail/<str:rid>/', DocumentDetailView.as_view(), name='infection_document_detail'),
    # 创建院感制度文档
    path('infection-document/create/', DocumentCreateView.as_view(), name='infection_document_create'),
    # 院感制度文档详情
    path('infection-document/update/<str:rid>/', DocumentUpdateView.as_view(), name='infection_document_update'),
    # 删除院感制度文档
    path('infection-document/delete/<str:rid>/', DocumentDeleteView.as_view(), name='infection_document_delete'),
    
    
    # 感染检测与报告列表
    path('infection-detection/list/', InfectionDetectionAndReportListView.as_view(), name='infection_detection_list'),
    # 感染检测与报告详情
    path('infection-detection/detail/<str:rid>/', InfectionDetectionAndReportDetailView.as_view(), name='infection_detection_detail'),
    # 创建感染检测与报告
    path('infection-detection/create/', InfectionDetectionAndReportCreateView.as_view(), name='infection_detection_create'),
    # 感染检测与报告详情
    path('infection-detection/update/<str:rid>/', InfectionDetectionAndReportUpdateView.as_view(), name='infection_detection_update'),
    # 删除感染检测与报告
    path('infection-detection/delete/<str:rid>/', InfectionDetectionAndReportDeleteView.as_view(), name='infection_detection_delete'),

]