from django.utils import timezone
from rest_framework import serializers

from maternity_center.models import MaternityCenter
from organizational_management.questionnaire.utils import validate_answer_data
from user.models import Maternity, Staff
from .enum import QuestionTypeEnum, QuestionnaireAvailableStageEnum, QuestionnaireStatusEnum, QuestionnaireTypeEnum
from .models import Questionnaire, Question, Choice, QuestionnaireResponse, Answer


# 选项序列化器
class ChoiceSerializer(serializers.ModelSerializer):
    """选项序列化器"""
    class Meta:
        model = Choice
        fields = ['rid', 'content', 'order']
        read_only_fields = ['rid']

# 问题序列化器
class QuestionSerializer(serializers.ModelSerializer):
    """问题序列化器"""
    choices = ChoiceSerializer(many=True, required=False)
    questionnaire = serializers.PrimaryKeyRelatedField(queryset=Questionnaire.objects.all(), write_only=True, required=False)

    class Meta:
        model = Question
        fields = ['rid', 'content', 'question_type','is_required', 'order', 'questionnaire', 'choices']
        read_only_fields = ['rid']
        extra_kwargs = {
            'questionnaire': {'write_only': True},
        }

    def validate(self, data):
        """验证问题数据"""
        question_type = data.get('question_type').upper()
        choices = data.get('choices')

        # 验证选择题必须有选项
        if question_type in [QuestionTypeEnum.SINGLE_CHOICE, QuestionTypeEnum.MULTIPLE_CHOICE]:
            if not choices:
                raise serializers.ValidationError("选择题必须包含选项")
            
            # 验证选项顺序不能重复
            choice_orders = set()
            for choice in choices:
                order = choice.get('order')
                if order in choice_orders:
                    raise serializers.ValidationError("选项顺序不能重复")
                choice_orders.add(order)

        return data

    def create(self, validated_data):
        """创建问题"""
        choices_data = self.initial_data.pop('choices', [])
        # 确保 validated_data 中不包含 choices 字段
        validated_data.pop('choices', None)
        question = Question.objects.create(**validated_data)
        
        # 创建选项
        for choice_data in choices_data:
            Choice.objects.create(question=question, **choice_data)
        
        return question

    def update(self, instance, validated_data):
        """更新问题"""
        choices_data = self.initial_data.pop('choices', [])
        # 确保 validated_data 中不包含 choices 字段
        validated_data.pop('choices', None)
        
        # 更新问题字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新选项
        instance.choices.all().delete()  # 删除旧选项
        for choice_data in choices_data:
            Choice.objects.create(question=instance, **choice_data)
        
        return instance

class QuestionnaireListSerializer(serializers.ModelSerializer):
    """问卷列表序列化器"""
    creator_sid = serializers.CharField(source='creator.sid', read_only=True)
    creator_name = serializers.CharField(source='creator.name', read_only=True)
    available_stage_display = serializers.SerializerMethodField()

    class Meta:
        model = Questionnaire
        fields = ['qid', 'title', 'description', 'questionnaire_type', 'is_active', 'maternity_center', 'creator', 'created_at',
                 'creator_sid', 'creator_name', 'available_stage', 'available_stage_display']
        extra_kwargs = {
            'maternity_center': {'write_only': True},
            'creator': {'write_only': True}
        }

    def get_available_stage_display(self, obj):
        return QuestionnaireAvailableStageEnum(obj.available_stage).label


# 问卷序列化器
class QuestionnaireSerializer(serializers.ModelSerializer):
    """问卷序列化器"""
    questions = QuestionSerializer(many=True, required=False)
    creator_sid = serializers.CharField(source='creator.sid', read_only=True)
    creator_name = serializers.CharField(source='creator.name', read_only=True)
    available_stage_display = serializers.SerializerMethodField()

    class Meta:
        model = Questionnaire
        fields = ['qid', 'title', 'description', 'questionnaire_type', 'is_active', 'maternity_center', 'creator', 'created_at',
                 'creator_sid', 'creator_name', 'questions', 'available_stage', 'available_stage_display']
        read_only_fields = ['qid', 'created_at', 'creator_sid', 'creator_name', 'available_stage_display']
        extra_kwargs = {
            'maternity_center': {'write_only': True},
            'creator': {'write_only': True}
        }


    def validate(self, data):
        """验证问卷数据"""
        available_stage = data.get('available_stage')
        if available_stage not in QuestionnaireAvailableStageEnum.values:
            raise serializers.ValidationError("可见阶段不合法")
        questionnaire_type = data.get('questionnaire_type').upper()
        if questionnaire_type not in QuestionnaireTypeEnum.values:
            raise serializers.ValidationError("问卷类型不合法")

        return data

    def create(self, validated_data):
        """创建问卷"""
        questions_data = validated_data.pop('questions', [])
        questionnaire = Questionnaire.objects.create(**validated_data)
        
        # 创建问题和选项
        for question_data in questions_data:
            # 先弹出 choices_data，再创建 Question
            choices_data = question_data.pop('choices', [])
            question = Question.objects.create(questionnaire=questionnaire, **question_data)
            
            # 创建选项
            for choice_data in choices_data:
                Choice.objects.create(question=question, **choice_data)
        
        return questionnaire

    def update(self, instance, validated_data):
        """更新问卷"""
        questions_data = validated_data.pop('questions', [])
        
        # 更新问卷字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新问题和选项
        instance.questions.all().delete()  # 删除旧问题和选项
        
        # 创建新的问题和选项
        for question_data in questions_data:
            # 先弹出 choices_data，再创建 Question
            choices_data = question_data.pop('choices', [])
            question = Question.objects.create(questionnaire=instance, **question_data)
            
            # 创建选项
            for choice_data in choices_data:
                Choice.objects.create(question=question, **choice_data)
        
        return instance
    
    def get_available_stage_display(self, obj):
        return QuestionnaireAvailableStageEnum(obj.available_stage).label

# 答案序列化器
class AnswerSerializer(serializers.ModelSerializer):
    """答案序列化器"""
    response = serializers.SlugRelatedField(slug_field='rid', read_only=True, required=False)
    # question 字段用于接收和返回 question 的 rid
    question = serializers.CharField()
    question_content = serializers.CharField(source='question.content', read_only=True)
    question_type = serializers.CharField(source='question.question_type', read_only=True)
    selected_choice = serializers.SlugRelatedField(slug_field='rid', queryset=Choice.objects.all(), required=False)
    selected_choices = serializers.SlugRelatedField(slug_field='rid', many=True, queryset=Choice.objects.all(), required=False)

    def to_representation(self, instance):
        """重写序列化方法，根据 context 动态决定是否包含 response 字段"""
        data = super().to_representation(instance)
        
        # 确保 question 字段显示 question 的 rid
        if hasattr(instance, 'question') and instance.question:
            data['question'] = instance.question.rid
        # print(self.context.get('exclude_response', False))
        # # 如果在 QuestionnaireResponse 的嵌套序列化中，移除 response 字段
        # if self.context.get('exclude_response', False):
        #     data.pop('response', None)
        return data

    class Meta:
        model = Answer
        fields = ['rid', 'response', 'question', 'question_content', 'question_type',
                  'selected_choice', 'selected_choices', 'boolean_answer', 'half_star_count', 'text_answer']
        read_only_fields = ['response', 'question_content', 'question_type']

    def validate(self, data):
        """不验证答案数据, 在QuestionnaireResponseSerializer中验证"""
        return data

    def create(self, validated_data):
        """创建答案"""
        selected_choices = validated_data.pop('selected_choices', [])
        answer = Answer.objects.create(**validated_data)
        if selected_choices:
            answer.selected_choices.set(selected_choices)
        return answer

    def update(self, instance, validated_data):
        """更新答案"""
        selected_choices = validated_data.pop('selected_choices', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if selected_choices is not None:
            instance.selected_choices.set(selected_choices)
        return instance


class QuestionnaireResponseListSerializer(serializers.ModelSerializer):
    """问卷回复列表序列化器"""
    questionnaire_qid = serializers.CharField(source='questionnaire.qid', read_only=True)
    questionnaire_title = serializers.CharField(source='questionnaire.title', read_only=True)
    questionnaire_type = serializers.CharField(source='questionnaire.questionnaire_type', read_only=True)
    questionnaire_type_display = serializers.SerializerMethodField()
    questionnaire_description = serializers.CharField(source='questionnaire.description', read_only=True)
    customer_uid = serializers.CharField(source='customer.uid', read_only=True)
    customer_name = serializers.CharField(source='customer.name', read_only=True)

    class Meta:
        model = QuestionnaireResponse
        fields = ['rid', 'questionnaire_qid', 'questionnaire_title', 'questionnaire_type', 'questionnaire_type_display',
                  'questionnaire_description', 'customer_uid', 'customer_name', 'submitted_at', 'updated_at']
    
    def get_questionnaire_type_display(self, obj):
        return QuestionnaireTypeEnum(obj.questionnaire.questionnaire_type).label


# 问卷提交序列化器
class QuestionnaireResponseSerializer(serializers.ModelSerializer):
    """问卷回复序列化器"""
    # questionnaire需要额外校验，设为read_only
    questionnaire_qid = serializers.CharField(source='questionnaire.qid', read_only=True)
    customer_uid = serializers.CharField(source='customer.uid', read_only=True)
    answers = AnswerSerializer(many=True)

    class Meta:
        model = QuestionnaireResponse
        fields = ['rid', 'questionnaire_qid', 'customer_uid', 'submitted_at', 'updated_at', 'answers']
        read_only_fields = ['rid', 'submitted_at', 'updated_at']

    # def to_representation(self, instance):
    #     """重写序列化方法，为嵌套的 AnswerSerializer 设置 context"""
    #     data = super().to_representation(instance)
    #     # 为 answers 重新序列化，排除 response 字段
    #     answers = instance.answers.all()
    #     answer_serializer = AnswerSerializer(
    #         answers, 
    #         many=True, 
    #         context={'exclude_response': True}  # 设置 context 排除 response
    #     )
    #     data['answers'] = answer_serializer.data
    #     return data
    
    def validate(self, data):
        """验证问卷回复数据"""
        # 从 context 获取已验证的 questionnaire 对象
        questionnaire = self.context.get('questionnaire')
        if not questionnaire:
            raise serializers.ValidationError("问卷对象未提供")
        answers = data.get('answers', [])
        question_rids = set(answer.get('question') for answer in answers if isinstance(answer.get('question'), str))
        # 验证是否有重复的问题rid
        if len(question_rids) != len(answers):
            raise serializers.ValidationError("问题回答重复")
        # 验证所有必填问题都已回答
        required_questions = set(questionnaire.questions.filter(is_required=True).values_list('rid', flat=True))
        answered_questions = {answer['question'] for answer in answers}
        missing_questions = required_questions - answered_questions
        
        if missing_questions:
            raise serializers.ValidationError(f"以下必填问题未回答: {', '.join(rid for rid in missing_questions)}")

        if question_rids:
            # 只查询当前问卷的问题，提高安全性
            questions = Question.objects.filter(
                rid__in=question_rids,
                questionnaire=questionnaire
            )
            question_map = {q.rid: q for q in questions}
        else:
            question_map = {}
        new_answers = []
        for answer in answers:
            question_rid = answer.get('question')
            if not question_rid:
                raise serializers.ValidationError("问题不存在")
            
            question = question_map.get(question_rid)
            if not question:
                raise serializers.ValidationError(f"问题 {question_rid} 不存在或不属于当前问卷")
            validate_answer_data(answer, question)
            answer['question'] = question
            new_answers.append(answer)
        data['answers'] = new_answers
        return data

    def create(self, validated_data):
        """创建问卷回复"""
        answers_data = validated_data.pop('answers')
        
        # 从 context 获取已验证的对象，避免重复查询
        questionnaire = self.context.get('questionnaire')
        customer = self.context.get('customer')
        
        if questionnaire:
            validated_data['questionnaire'] = questionnaire
        if customer:
            validated_data['customer'] = customer
            
        response = QuestionnaireResponse.objects.create(**validated_data)
        
        for answer_data in answers_data:
            answer_data['response'] = response
            AnswerSerializer().create(validated_data=answer_data)
        
        return response
    
    def update(self, instance, validated_data):
        """更新问卷回复"""
        answers_data = validated_data.pop('answers', [])
        
        # 更新问题字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        answer_rids = set(answer.get('rid') for answer in answers_data if isinstance(answer.get('rid'), str))
        if answer_rids:
            # 只查询当前问卷回复的答案，提高安全性
            answers = Answer.objects.filter(
                rid__in=answer_rids,
                response=instance
            )
            answer_map = {a.rid: a for a in answers}
            for answer_data in answers_data:
                answer = answer_map.get(answer_data.get('rid'))
                if answer:
                    # 修改答案，不允许修改答案对应的问题
                    answer_data.pop('question', None)
                    AnswerSerializer().update(instance=answer, validated_data=answer_data)
                else:
                    # 再次提交了非必填问题，创建答案
                    AnswerSerializer().create(validated_data=answer_data)
        return instance
