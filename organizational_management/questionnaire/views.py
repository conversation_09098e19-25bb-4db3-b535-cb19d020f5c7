from django.db import transaction
from django.db.models import Q
from django.db.models.aggregates import Count, Sum
from datetime import datetime
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly, MaternityOrStaffWithPermission
from core.resp import make_response
from core.view import PaginationListBaseView
from organizational_management.questionnaire.utils import get_questionnaire_stage_by_maternity
from permissions.enum import PermissionEnum
from user.models import Staff, Maternity
from .enum import QuestionTypeEnum, QuestionnaireAvailableStageEnum, QuestionnaireStatusEnum, QuestionnaireTypeEnum
from .models import Questionnaire, QuestionnaireResponse, Answer
from .serializers import (
    QuestionnaireListSerializer, QuestionnaireResponseListSerializer, QuestionnaireSerializer, QuestionSerializer, ChoiceSerializer,
    QuestionnaireResponseSerializer, AnswerSerializer
)


class QuestionnaireTypeView(APIView):
    """问卷类型视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_VIEW

    def get(self, request):
        """获取问卷类型列表"""
        questionnaire_types = [{'value': choice[0], 'label': choice[1]} for choice in QuestionnaireTypeEnum.choices]
        return make_response(code=0, msg="获取成功", data=questionnaire_types)

class QuestionnaireListView(PaginationListBaseView):
    """问卷列表视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_VIEW
    serializer_class = QuestionnaireListSerializer
    
    response_msg = "获取问卷列表成功"
    error_response_msg = "获取问卷列表失败"

    def get_queryset(self):
        """获取问卷列表"""
        # 获取查询参数
        search = self.request.query_params.get('search', None)
        is_active = self.request.query_params.get('is_active', None)
        questionnaire_type = self.request.query_params.get('questionnaire_type', None)
        questionnaire_available_stage = self.request.query_params.get('questionnaire_available_stage', None)
        creator_sid = self.request.query_params.get('creator_sid', None)
        creator_department_rid = self.request.query_params.get('creator_department_rid', None)
        
        base_queryset = Questionnaire.objects.filter(maternity_center=self.request.user.maternity_center).select_related('creator')
        if isinstance(self.request.user, Maternity): # 产妇只能查询正在生效的问卷，即将过期的排前面
            base_queryset = base_queryset.filter(
                is_active=True,
                available_stage__in=get_questionnaire_stage_by_maternity(self.request.user, self.request.user.maternity_center)
            ).order_by('created_at')
        else:
            if questionnaire_available_stage:
                if questionnaire_available_stage not in QuestionnaireAvailableStageEnum.values:
                    self.error_response_msg = "问卷可用阶段不正确"
                    return None
                base_queryset = base_queryset.filter(available_stage=questionnaire_available_stage)
            if is_active:
                is_active = is_active.lower()
                base_queryset = base_queryset.filter(is_active=is_active=='true')
            if creator_sid:
                base_queryset = base_queryset.filter(creator__sid=creator_sid)
            elif creator_department_rid:
                base_queryset = base_queryset.filter(creator__department__rid=creator_department_rid)
            base_queryset = base_queryset.order_by('-created_at')

        # 应用过滤
        if search:
            base_queryset = base_queryset.filter(Q(title__icontains=search) | Q(description__icontains=search))
        if questionnaire_type:
            questionnaire_type = questionnaire_type.upper()
            if questionnaire_type not in QuestionnaireTypeEnum.values:
                return make_response(code=-1, msg="无效的问卷类型")
            base_queryset = base_queryset.filter(questionnaire_type=questionnaire_type)

        return base_queryset

class QuestionnaireCreateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_EDIT
    serializer_class = QuestionnaireSerializer
    def post(self, request):
        """创建新问卷"""
        # 将请求数据复制一份，并添加maternity_center和creator
        # 序列化器会处理questions字段的嵌套创建/更新
        data = request.data.copy()
        data['maternity_center'] = request.user.maternity_center.id
        data['creator'] = request.user.id

        serializer = QuestionnaireSerializer(data=data)
        
        if serializer.is_valid():
            try:
                questionnaire = serializer.save()
                return make_response(code=0, msg="创建成功", data=QuestionnaireSerializer(questionnaire).data)
            except Exception as e:
                return make_response(code=-1, msg=f"保存问卷失败: {str(e)}", data=None)
        return make_response(code=-1, msg="问卷数据无效", data=serializer.errors)


class QuestionnaireDetailView(APIView):
    """问卷详情视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_VIEW

    def get_object(self, qid):
        """获取问卷对象"""
        try:
            if isinstance(self.request.user, Staff):
                return Questionnaire.objects.prefetch_related('questions__choices').get(
                    qid=qid,
                    maternity_center=self.request.user.maternity_center
                )
            elif isinstance(self.request.user, Maternity):
                return Questionnaire.objects.prefetch_related('questions__choices').get(
                    qid=qid,
                    maternity_center=self.request.user.maternity_center,
                    is_active=True,
                    available_stage__in=get_questionnaire_stage_by_maternity(self.request.user, self.request.user.maternity_center)
                )
            return None
        except Questionnaire.DoesNotExist:
            return None

    def get(self, request, qid):
        """获取问卷详情"""
        questionnaire = self.get_object(qid)
        if not questionnaire:
            return make_response(code=-1, msg="问卷不存在")
        serializer = QuestionnaireSerializer(questionnaire)
        return make_response(code=0, msg="获取成功", data=serializer.data)

    def put(self, request, qid):
        """更新问卷"""
        if not isinstance(request.user, Staff):
            return make_response(code=-1, msg="只有员工可以更新问卷")

        questionnaire = self.get_object(qid)
        if not questionnaire:
            return make_response(code=-1, msg="问卷不存在")
        
        data = request.data.copy()
        data['maternity_center'] = questionnaire.maternity_center.id
        data['creator'] = questionnaire.creator.id
        # 处理问题和选项数据
        questions_data = data.pop('questions', [])
        
        serializer = QuestionnaireSerializer(questionnaire, data=data)
        if not serializer.is_valid():
            return make_response(code=-1, msg="问卷数据无效", data=serializer.errors)
        try:
            with transaction.atomic():
                questionnaire = serializer.save()
                # 更新问题和选项
                # 首先删除所有现有的问题和选项
                questionnaire.questions.all().delete()
                # 创建新的问题和选项
                for question_data in questions_data:
                    question_data['questionnaire'] = questionnaire.id
                    question_serializer = QuestionSerializer(data=question_data)
                    if not question_serializer.is_valid():
                        raise Exception(question_serializer.errors)
                    question_serializer.save()
        except Exception as e:
            return make_response(code=-1, msg=f"更新失败: {str(e)}", data=None)

        return make_response(code=0, msg="更新成功", data=QuestionnaireSerializer(questionnaire).data)

    def delete(self, request, qid):
        """删除问卷"""
        if not isinstance(request.user, Staff):
            return make_response(code=-1, msg="只有员工可以删除问卷")

        questionnaire = self.get_object(qid)
        if not questionnaire:
            return make_response(code=-1, msg="问卷不存在")
        questionnaire.delete()
        return make_response(code=0, msg="删除成功")

class QuestionnairePublishView(APIView):
    """问卷发布视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_EDIT

    def get_object(self, qid):
        """获取问卷对象"""
        try:
            return Questionnaire.objects.get(
                qid=qid,
                maternity_center=self.request.user.maternity_center
            )
        except Questionnaire.DoesNotExist:
            return None

    def post(self, request, qid):
        """发布问卷"""
        questionnaire = self.get_object(qid)
        if not questionnaire:
            return make_response(code=-1, msg="问卷不存在")
        questionnaire.is_active = True
        questionnaire.save()
        return make_response(code=0, msg="问卷发布成功")

class QuestionnaireUnpublishView(APIView):
    """问卷取消发布视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_EDIT

    def get_object(self, qid):
        """获取问卷对象"""
        try:
            return Questionnaire.objects.get(
                qid=qid,
                maternity_center=self.request.user.maternity_center
            )
        except Questionnaire.DoesNotExist:
            return None

    def post(self, request, qid):
        """取消发布问卷"""
        questionnaire = self.get_object(qid)
        if not questionnaire:
            return make_response(code=-1, msg="问卷不存在")
        questionnaire.is_active = False
        questionnaire.save()
        return make_response(code=0, msg="问卷已取消发布")

class QuestionnaireStatisticsView(APIView):
    """问卷统计视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_VIEW

    def get_object(self, qid):
        """获取问卷对象"""
        try:
            return Questionnaire.objects.prefetch_related('questions__choices').get(
                qid=qid,
                maternity_center=self.request.user.maternity_center
            )
        except Questionnaire.DoesNotExist:
            return None

    def get(self, request, qid):
        """获取问卷统计数据"""
        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        try:
            if start_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except Exception as e:
            return make_response(code=-1, msg=f"日期格式不正确: {str(e)}")
        
        if start_date and end_date and start_date > end_date:
            return make_response(code=-1, msg="开始日期不能大于结束日期")

        questionnaire = self.get_object(qid)
        if not questionnaire:
            return make_response(code=-1, msg="问卷不存在")

        # 获取回复数量
        response_queryset = questionnaire.responses.all()
        if start_date:
            response_queryset = response_queryset.filter(updated_at__gte=start_date)
        if end_date:
            response_queryset = response_queryset.filter(updated_at__lte=end_date)
        response_count = response_queryset.count()
        
        # 获取每个问题的回答统计
        questions_stats = []
        for question in questionnaire.questions.all():
            if question.question_type in [QuestionTypeEnum.SINGLE_CHOICE, QuestionTypeEnum.MULTIPLE_CHOICE]:
                # 选择题统计
                choice_stats = []
                for choice in question.choices.all():
                    count = Answer.objects.filter(
                        question=question,
                        selected_choice=choice,
                        response__in=response_queryset
                    ).count() + Answer.objects.filter(
                        question=question,
                        selected_choices=choice,
                        response__in=response_queryset
                    ).count()
                    choice_stats.append({
                        'choice_rid': choice.rid,
                        'content': choice.content,
                        'count': count
                    })
                questions_stats.append({
                    'question_rid': question.rid,
                    'content': question.content,
                    'type': question.question_type,
                    'choice_stats': choice_stats
                })
            elif question.question_type == QuestionTypeEnum.TRUE_FALSE:
                # 判断题统计
                true_count = Answer.objects.filter(
                    question=question,
                    boolean_answer=True,
                    response__in=response_queryset
                ).count()
                false_count = Answer.objects.filter(
                    question=question,
                    boolean_answer=False,
                    response__in=response_queryset
                ).count()
                questions_stats.append({
                    'question_rid': question.rid,
                    'content': question.content,
                    'type': question.question_type,
                    'true_count': true_count,
                    'false_count': false_count
                })
            elif question.question_type == QuestionTypeEnum.STAR_RATING:
                # 星级评分题统计
                details = Answer.objects.filter(
                    question=question,
                    response__in=response_queryset
                ).aggregate(
                    total_count=Count('id'),
                    total_half_star_count=Sum('half_star_count'),
                    star_0_5_count=Count('id', filter=Q(half_star_count=1)),
                    star_1_0_count=Count('id', filter=Q(half_star_count=2)),
                    star_1_5_count=Count('id', filter=Q(half_star_count=3)),
                    star_2_0_count=Count('id', filter=Q(half_star_count=4)),
                    star_2_5_count=Count('id', filter=Q(half_star_count=5)),
                    star_3_0_count=Count('id', filter=Q(half_star_count=6)),
                    star_3_5_count=Count('id', filter=Q(half_star_count=7)),
                    star_4_0_count=Count('id', filter=Q(half_star_count=8)),
                    star_4_5_count=Count('id', filter=Q(half_star_count=9)),
                    star_5_0_count=Count('id', filter=Q(half_star_count=10))
                )
                questions_stats.append({
                    'question_rid': question.rid,
                    'content': question.content,
                    'type': question.question_type,
                    'details': details
                })
            elif question.question_type == QuestionTypeEnum.TEXT:
                # 文本题统计
                text_answers = Answer.objects.filter(
                    question=question,
                    response__in=response_queryset
                ).values_list('text_answer', flat=True)
                questions_stats.append({
                    'question_rid': question.rid,
                    'content': question.content,
                    'type': question.question_type,
                    'text_answers': list(text_answers)
                })

        return make_response(code=0, msg="获取成功", data={
            'response_count': response_count,
            'questions_stats': questions_stats
        })

class QuestionnaireResponseListView(PaginationListBaseView):
    """问卷回复列表视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_VIEW
    serializer_class = QuestionnaireResponseListSerializer
    
    response_msg = "获取问卷回复列表成功"
    error_response_msg = "获取问卷回复列表失败"
    
    def get_queryset(self):
        """获取问卷回复列表"""
        # 获取查询参数
        questionnaire_qid = self.request.query_params.get('questionnaire_qid')
        customer_uid = self.request.query_params.get('customer_uid')
        search = self.request.query_params.get('search', None)
        questionnaire_type = self.request.query_params.get('questionnaire_type')
        
        base_queryset = QuestionnaireResponse.objects.filter(
            questionnaire__maternity_center=self.request.user.maternity_center
        ).select_related('questionnaire', 'customer')
        
        if questionnaire_qid:
            base_queryset = base_queryset.filter(questionnaire__qid=questionnaire_qid)
        
        if isinstance(self.request.user, Maternity):
            base_queryset = base_queryset.filter(customer=self.request.user) # 产妇只能查看自己的问卷回复
        elif customer_uid:
            base_queryset = base_queryset.filter(customer__uid=customer_uid)
        if search:
            base_queryset = base_queryset.filter(Q(questionnaire__title__icontains=search) | Q(customer__name__icontains=search))
        if questionnaire_type:
            questionnaire_type = questionnaire_type.upper()
            if questionnaire_type not in QuestionnaireTypeEnum.values:
                self.error_response_msg = "无效的问卷类型"
                return None
            base_queryset = base_queryset.filter(questionnaire__questionnaire_type=questionnaire_type)
        
        return base_queryset
    

class QuestionnaireResponseDetailView(APIView):
    """问卷回复详情视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_VIEW
    
    def get_object(self, rid):
        """获取问卷回复对象"""
        base_queryset = QuestionnaireResponse.objects.filter(
            questionnaire__maternity_center=self.request.user.maternity_center
        ).select_related('questionnaire', 'customer')
        # 产妇只能查看自己的问卷回复
        if isinstance(self.request.user, Maternity):
            base_queryset = base_queryset.filter(customer=self.request.user)
        try:
            return base_queryset.get(rid=rid)
        except QuestionnaireResponse.DoesNotExist:
            return None
    
    def get(self, request, rid):
        """获取问卷回复列表"""
        response = self.get_object(rid)
        if not response:
            return make_response(code=-1, msg="问卷回复不存在")
        serializer = QuestionnaireResponseSerializer(response)
        return make_response(code=0, msg="获取成功", data=serializer.data)
    
    def put(self, request, rid):
        """更新问卷回复"""
        if not isinstance(request.user, Maternity):
            return make_response(code=-1, msg="只有客户可以更新问卷回复")
        response = self.get_object(rid)
        if not response:
            return make_response(code=-1, msg="问卷回复不存在")
        serializer = QuestionnaireResponseSerializer(response, data=request.data, context={
            'questionnaire': response.questionnaire
        })
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="更新成功", data=serializer.data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)


class QuestionnaireResponseCreateView(APIView):
    """问卷回复创建视图"""
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.QUESTIONNAIRE_VIEW

    def post(self, request):
        """提交问卷回复"""
        if isinstance(request.user, Staff):
            return make_response(code=-1, msg="只有客户可以提交问卷回复")
        # 验证问卷是否存在且已发布
        questionnaire_qid = request.data.get('questionnaire_qid')
        try:
            questionnaire = Questionnaire.objects.get(
                qid=questionnaire_qid,
                maternity_center=request.user.maternity_center,
                is_active=True,
                available_stage__in=get_questionnaire_stage_by_maternity(request.user, request.user.maternity_center)
            )
        except Questionnaire.DoesNotExist:
            return make_response(code=-1, msg="问卷不存在或未发布")

        # 验证是否已经提交过
        if QuestionnaireResponse.objects.filter(
            questionnaire=questionnaire,
            customer=request.user
        ).exists():
            return make_response(code=-1, msg="您已经提交过该问卷")

        # 处理提交数据
        data = request.data.copy()
        serializer = QuestionnaireResponseSerializer(data=data, context={
            'questionnaire': questionnaire,
            'customer': request.user
        })
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="提交成功", data=serializer.data)
        return make_response(code=-1, msg="提交数据无效", data=serializer.errors)
        
#暂时没用
class AnswerDetailView(APIView):
    """答案详情视图"""
    def get_object(self, rid):
        try:
            return Answer.objects.select_related(
                'question', 'response', 'selected_choice'
            ).prefetch_related('selected_choices').get(rid=rid)
        except Answer.DoesNotExist:
            return None

    def get(self, request, rid):
        """获取答案详情"""
        answer = self.get_object(rid)
        if not answer:
            return make_response(code=-1, msg="答案不存在")
        serializer = AnswerSerializer(answer)
        return make_response(data=serializer.data)

    def put(self, request, rid):
        """更新答案"""
        answer = self.get_object(rid)
        if not answer:
            return make_response(code=-1, msg="答案不存在")
        serializer = AnswerSerializer(answer, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return make_response(data=serializer.data)
        return make_response(code=-1, msg="更新失败")

    def delete(self, request, rid):
        """删除答案"""
        answer = self.get_object(rid)
        if not answer:
            return make_response(code=-1, msg="答案不存在")
        answer.delete()
        return make_response(code=0, msg="删除成功")


