from django.urls import path

from . import views

app_name = 'questionnaire'

urlpatterns = [
    # 问卷管理
    path('questionnaires/list/', views.QuestionnaireListView.as_view(), name='questionnaire-list'),
    path('questionnaires/create/', views.QuestionnaireCreateView.as_view(), name='questionnaire-list'),
    path('questionnaires/type/', views.QuestionnaireTypeView.as_view(), name='questionnaire-type'),
    path('questionnaires/detail/<str:qid>/', views.QuestionnaireDetailView.as_view(), name='questionnaire-detail'),
    path('questionnaires/<str:qid>/publish/', views.QuestionnairePublishView.as_view(), name='questionnaire-publish'),
    path('questionnaires/<str:qid>/unpublish/', views.QuestionnaireUnpublishView.as_view(), name='questionnaire-unpublish'),
    path('questionnaires/<str:qid>/statistics/', views.QuestionnaireStatisticsView.as_view(), name='questionnaire-statistics'),
    
    # 问卷回复
    path('questionnaires/responses/create/', views.QuestionnaireResponseCreateView.as_view(), name='response-create'),
    path('questionnaires/responses/', views.QuestionnaireResponseListView.as_view(), name='response-list'),
    path('questionnaires/responses/detail/<str:rid>/', views.QuestionnaireResponseDetailView.as_view(), name='response-detail'),
    
    # 答案管理
    # path('answers/<str:rid>/', views.AnswerDetailView.as_view(), name='answer-detail'),
] 