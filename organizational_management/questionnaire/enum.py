from django.db import models

class QuestionnaireTypeEnum(models.TextChoices):
    """
    定义问卷的类型
    """
    SATISFACTION = 'SATISFACTION', '满意度调查'
    HEALTH_STATUS = 'HEALTH_STATUS', '健康状况'
    SERVICE_EVALUATION = 'SERVICE_EVALUATION', '服务评价'
    CUSTOM = 'CUSTOM', '自定义'

class QuestionTypeEnum(models.TextChoices):
    """
    定义问题的类型
    """
    SINGLE_CHOICE = 'SINGLE_CHOICE', '单选'
    MULTIPLE_CHOICE = 'MULTIPLE_CHOICE', '多选'
    TRUE_FALSE = 'TRUE_FALSE', '判断'
    STAR_RATING = 'STAR_RATING', '星级评分'
    TEXT = 'TEXT', '文本'


class QuestionnaireStatusEnum(models.TextChoices):
    UPCOMING = "UPCOMING", "即将开始"
    IN_PROGRESS = "IN_PROGRESS", "进行中"
    COMPLETED = "COMPLETED", "已结束"
    
# TODO 如果想要用户灵活配置，则需要入库
class QuestionnaireAvailableStageEnum(models.TextChoices):
    AFTER_AWEEK = "AFTER_AWEEK", "入住一周"
    MEDIUM_TERM = "MEDIUM_TERM", "中期"
    BEFORE_DISCHARGE = "BEFORE_DISCHARGE", "出院前"
    GENERAL = "GENERAL", "通用"

# 时间范围列表, 0表示入住第一天，-1表示预计出院的那天
QUESTIONNAIRE_STAGE_DAYS = [
    (QuestionnaireAvailableStageEnum.AFTER_AWEEK, (7, 14)),      # 入住8-14天
    (QuestionnaireAvailableStageEnum.MEDIUM_TERM, (14, -8)),     # 入住15天到出院前7天
    (QuestionnaireAvailableStageEnum.BEFORE_DISCHARGE, (-7, -1)), # 最后一周
    (QuestionnaireAvailableStageEnum.GENERAL, (0, -1)),          # 整个住院期间
]