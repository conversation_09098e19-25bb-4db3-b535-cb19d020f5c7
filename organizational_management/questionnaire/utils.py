from datetime import date, timedelta
from rest_framework import serializers

from .enum import QuestionTypeEnum, QuestionnaireAvailableStageEnum, QUESTIONNAIRE_STAGE_DAYS


def get_questionnaire_stage_by_maternity(maternity, maternity_center_id):
    """
    Args:
        maternity: 产妇
        maternity_center_id: 月子中心的id  
        
    Returns:
        list: 对应的QuestionnaireAvailableStageEnum值列表
    """
    from customer_service.core_records.models.maternity_admission import MaternityAdmission
    from user.models import Maternity
    
    try:
        # 查找该产妇的最新入院记录
        admission = MaternityAdmission.objects.filter(  # type: ignore
            maternity=maternity,
            maternity_center_id=maternity_center_id
        ).order_by('-created_at').first()
        
        if not admission:
            # 如果没有入院记录，返回通用阶段
            return [QuestionnaireAvailableStageEnum.GENERAL.value]
            
        # 获取入住日期和预计出院日期
        check_in_date = admission.actual_check_in_date or admission.expected_check_in_date
        check_out_date = admission.expected_check_out_date
        
        if not check_in_date or not check_out_date:
            # 如果没有入住日期，返回通用阶段
            return [QuestionnaireAvailableStageEnum.GENERAL.value]
        
        return get_questionnaire_stage_by_date(check_in_date, check_out_date)
    except Maternity.DoesNotExist:  # type: ignore
        # 产妇不存在，返回通用阶段
        return [QuestionnaireAvailableStageEnum.GENERAL.value]
    except Exception:
        # 其他异常，返回通用阶段
        return [QuestionnaireAvailableStageEnum.GENERAL.value]


def get_questionnaire_stage_by_date(checkin_date, checkout_date):
    """
    根据入住天数和预计出院天数，返回对应的问卷可见阶段
    """
    today = date.today()
    result = []
    for stage, (start, end) in QUESTIONNAIRE_STAGE_DAYS:
        if start >= 0:
            start_date = checkin_date + timedelta(days=start)
        else:
            start_date = checkout_date + timedelta(days=start+1)
        if end >= 0:
            end_date = checkin_date + timedelta(days=end)
        else:
            end_date = checkout_date + timedelta(days=end+1)
        if start_date <= today <= end_date:
            result.append(stage.value)
    
    return result

def validate_answer_data(data, question):
    
    question_type = question.question_type
    selected_choice = data.get('selected_choice')
    selected_choices = data.get('selected_choices', [])
    boolean_answer = data.get('boolean_answer')
    text_answer = data.get('text_answer')
    half_star_count = data.get('half_star_count')

    if question_type == QuestionTypeEnum.SINGLE_CHOICE:
        if not selected_choice:
            raise serializers.ValidationError("单选问题必须选择一个选项")
        data['selected_choices'] = []
        data['boolean_answer'] = None
        data['text_answer'] = None
        data['half_star_count'] = None
    elif question_type == QuestionTypeEnum.MULTIPLE_CHOICE:
        if not selected_choices:
            raise serializers.ValidationError("多选问题必须至少选择一个选项")
        data['selected_choice'] = None
        data['boolean_answer'] = None
        data['text_answer'] = None
        data['half_star_count'] = None
    elif question_type == QuestionTypeEnum.TRUE_FALSE:
        if boolean_answer is None:
            raise serializers.ValidationError("判断题必须选择是或否")
        data['selected_choice'] = None
        data['selected_choices'] = []
        data['text_answer'] = None
        data['half_star_count'] = None
    elif question_type == QuestionTypeEnum.TEXT:
        if not text_answer:
            raise serializers.ValidationError("文本题必须填写答案")
        data['selected_choice'] = None
        data['selected_choices'] = []
        data['boolean_answer'] = None
        data['half_star_count'] = None
    elif question_type == QuestionTypeEnum.STAR_RATING:
        if not half_star_count or not isinstance(half_star_count, int):
            raise serializers.ValidationError("星级评分问题必须选择一个星级")
        if not half_star_count in range(1, 11):
            raise serializers.ValidationError("星级评分问题必须选择一个1-10的星级")
        data['selected_choice'] = None
        data['selected_choices'] = []
        data['boolean_answer'] = None
        data['text_answer'] = None
