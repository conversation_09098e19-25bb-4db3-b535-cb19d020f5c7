from django.db import models

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from maternity_center.models import MaternityCenter
from organizational_management.equipment.enum import EquipMentTypeEnum, EquipMentStatusEnum
from user.models import Staff


# Create your models here.
class Equipment(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心")
    # 设备名称
    name = models.CharField(max_length=100, verbose_name="设备名称")
    # 设备图片
    image = models.TextField(verbose_name="设备图片" , blank=True , default='')
    # 设备类型
    type = models.CharField(max_length=100, verbose_name="设备类型", choices=EquipMentTypeEnum.choices )
    # 设备功能
    function = models.TextField(verbose_name="设备功能", null=True, blank=True)
    # 设备状态
    status = models.CharField(max_length=100, verbose_name="设备状态", choices=EquipMentStatusEnum.choices )
    # 使用方式
    usage_method = models.TextField(verbose_name="使用方式" , blank=True , default='')
    # 使用时长
    usage_duration = models.TextField(verbose_name="使用时长" , blank=True , default='')
    # 消毒方式
    disinfection_method = models.TextField(verbose_name="消毒方式" , blank=True , default='')
    # 资源id
    rid = models.CharField(max_length=100, unique=True,verbose_name="资源id", blank=True,default=generate_resource_uuid)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人",related_name="creator_equipments")


    class Meta:
        verbose_name = "设备"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name