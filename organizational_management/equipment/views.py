
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.resp import make_response
from core.view import PaginationListBaseView
from file.models import EquipmentImage
from organizational_management.equipment.enum import EquipMentStatusEnum, EquipMentTypeEnum
from organizational_management.equipment.models import Equipment
from organizational_management.equipment.serializers import EquipmentCreateSerializer, EquipmentDetailSerializer, \
    EquipmentListSerializer, EquipmentUpdateSerializer
from permissions.enum import PermissionEnum


class EquipmentBaseView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]


# 设备列表视图
class EquipmentListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.EQUIPMENT_VIEW
    serializer_class = EquipmentListSerializer
    response_msg = "获取设备列表成功"
    search_fields = ["name"]
    
    def get_queryset(self):
        status = self.request.query_params.get('status', None)
        type = self.request.query_params.get('type', None)

        base_queryset = Equipment.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
        if status  :
            if status not in EquipMentStatusEnum.values:
                return  make_response(code=-1, msg="设备状态参数错误")
            base_queryset = base_queryset.filter(status=status)
        if type  :
            if type not in EquipMentTypeEnum.values:
                return  make_response(code=-1, msg="设备类型参数错误")
            base_queryset = base_queryset.filter(type=type)
        return base_queryset

# 设备详情视图
class EquipmentDetailView(EquipmentBaseView):
    
    staff_required_permission = PermissionEnum.EQUIPMENT_VIEW
    
    def get(self, request, rid):
        try:
            instance = Equipment.objects.get(rid=rid, maternity_center=request.user.maternity_center)
        except Equipment.DoesNotExist:
            return make_response(code=-1, msg="设备不存在")
        
        serializer = EquipmentDetailSerializer(instance)
        return make_response(code=0, msg="获取设备详情成功", data=serializer.data)

# 设备创建视图
class EquipmentCreateView(EquipmentBaseView):
    
    staff_required_permission = PermissionEnum.EQUIPMENT_EDIT
    
    
    def post(self, request):
        
        data = request.data.copy()
        
        if data.get('image'):
            image = EquipmentImage.get_by_rid(data['image'],request.user.maternity_center)
            if not image:
                return make_response(code=-1, msg="图片不存在")
        
        data["maternity_center"] = request.user.maternity_center.id
        data["creator"] = request.user.id
        
        serializer = EquipmentCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            resData = EquipmentDetailSerializer(serializer.instance).data
            return make_response(code=0, msg="设备创建成功", data=resData)
        else:
            return make_response(code=-1, msg="设备创建失败：数据校验失败", data=serializer.errors)
        
class EquipmentUpdateView(EquipmentBaseView):
    
    staff_required_permission = PermissionEnum.EQUIPMENT_EDIT
    
    def put(self, request, rid):
        try:
            instance = Equipment.objects.get(rid=rid, maternity_center=request.user.maternity_center)
        except Equipment.DoesNotExist:
            return make_response(code=-1, msg="设备不存在")
        
        data = request.data.copy()
        
        if data.get('image'):
            image = EquipmentImage.get_by_rid(data['image'],request.user.maternity_center)
            if not image:
                return make_response(code=-1, msg="图片不存在")
        

        serializer = EquipmentUpdateSerializer(instance, data=data, partial=True)
        if serializer.is_valid():
            serializer.save()
            resData = EquipmentDetailSerializer(serializer.instance).data
            return make_response(code=0, msg="更新设备信息成功", data=resData)
        else:
            return make_response(code=-1, msg="设备更新失败：数据校验失败", data=serializer.errors)



# 设备删除视图
class EquipmentDeleteView(EquipmentBaseView):
    
    staff_required_permission = PermissionEnum.EQUIPMENT_EDIT
    
    def delete(self, request, rid):
        try:
            instance = Equipment.objects.get(rid=rid, maternity_center=request.user.maternity_center)
        except Equipment.DoesNotExist:
            return make_response(code=-1, msg="设备不存在")
        instance.delete()
        return make_response(code=0, msg="删除设备成功")