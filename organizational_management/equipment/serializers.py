from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from file.models import EquipmentImage
from .enum import EquipMentTypeEnum, EquipMentStatusEnum
from .models import Equipment


# 设备列表序列化器
class EquipmentListSerializer(serializers.ModelSerializer):
    
    # 设备类型
    type_display = serializers.SerializerMethodField()
    # 设备状态
    status_display = serializers.SerializerMethodField()
    # 图片
    image_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Equipment
        exclude = ['maternity_center','creator','id','created_at','updated_at']

    def get_type_display(self, obj):
        return EquipMentTypeEnum(obj.type).label

    def get_status_display(self, obj):
        return EquipMentStatusEnum(obj.status).label
    
    def get_image_url(self, obj):
        image = EquipmentImage.get_by_rid(obj.image,obj.maternity_center)
        return image.file.url if image else None

# 设备详情序列化器
class EquipmentDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 创建人
    creator_name = serializers.SerializerMethodField()
    # 设备类型
    type_display = serializers.SerializerMethodField()
    # 设备状态
    status_display = serializers.SerializerMethodField()
    # 图片
    image_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Equipment
        exclude = ['maternity_center','creator','id']

    def get_type_display(self, obj):
        return EquipMentTypeEnum(obj.type).label

    def get_status_display(self, obj):
        return EquipMentStatusEnum(obj.status).label
    
    def get_creator_name(self, obj):
        return obj.creator.name if obj.creator else ''
    
    def get_image_url(self, obj):
        image = EquipmentImage.get_by_rid(obj.image,obj.maternity_center)
        return image.file.url if image else None

# 设备创建序列化器
class EquipmentCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Equipment
        exclude = ['created_at','updated_at','rid']
        


# 设备更新序列化器
class EquipmentUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Equipment
        exclude = ['maternity_center','creator','rid','created_at','updated_at']
        
    