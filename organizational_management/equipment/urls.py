from django.urls import path

from organizational_management.equipment.views import EquipmentListView, EquipmentCreateView, EquipmentUpdateView, \
    EquipmentDetailView, EquipmentDeleteView

urlpatterns = [
    # 设备列表接口
    path('equipment/list/', EquipmentListView.as_view(), name='equipment-list'),
    # 设备创建接口
    path('equipment/create/', EquipmentCreateView.as_view(), name='equipment-create'),
    # 设备详情接口
    path('equipment/detail/<str:rid>/', EquipmentDetailView.as_view(), name='equipment-detail'),
    # 设备更新接口
    path('equipment/update/<str:rid>/', EquipmentUpdateView.as_view(), name='equipment-update'),
    # 设备删除接口
    path('equipment/delete/<str:rid>/', EquipmentDeleteView.as_view(), name='equipment-delete'),
  ]