from django.db import models

# 设备类型枚举
class EquipMentTypeEnum(models.TextChoices):
    # 医疗型
    MEDICAL = "MEDICAL", "医疗设备"
    # 银行卡
    NURING = "NURING", "护理设备"
    # 清洁型
    CLEAN = "CLEAN", "清洁设备"
    # 厨房型
    KITCHEN = "KITCHEN", "厨房设备"
    # 其他
    OTHER = "OTHER", "其他设备"


# 设备状态枚举
class EquipMentStatusEnum(models.TextChoices):
    # 使用中
    USING = "USING", "使用中"
    # 维护中
    MAINTAINING = "MAINTAINING", "维护中"
    # 停用
    DISABLED = "DISABLED", "停用"