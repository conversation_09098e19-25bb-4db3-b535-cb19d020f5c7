from django.urls import path

from .views import MaternitySelectListView, MaternityWardRoundRecordCreateView, MaternityWardRoundRecordDeleteView, MaternityWardRoundRecordDetailView, MaternityWardRoundRecordListView, MaternityWardRoundRecordUpdateView, NewbornSelectListView, NewbornWardRoundRecordCreateView, NewbornWardRoundRecordDeleteView, NewbornWardRoundRecordDetailView, NewbornWardRoundRecordListView, NewbornWardRoundRecordUpdateView

urlpatterns = [
    
    # 产妇查房记录列表
    path('wardround/maternity/list/', MaternityWardRoundRecordListView.as_view(), name='wardround-list'),
    # 产妇查房记录详情
    path('wardround/maternity/detail/<str:rid>/', MaternityWardRoundRecordDetailView.as_view(), name='wardround-detail'),
    # 产妇查房记录创建
    path('wardround/maternity/create/', MaternityWardRoundRecordCreateView.as_view(), name='wardround-create'),
    # 产妇查房记录更新
    path('wardround/maternity/update/<str:rid>/', MaternityWardRoundRecordUpdateView.as_view(), name='wardround-update'),
    # 产妇查房记录删除
    path('wardround/maternity/delete/<str:rid>/', MaternityWardRoundRecordDeleteView.as_view(), name='wardround-delete'),
    
    
    # 新生儿查房记录列表
    path('wardround/newborn/list/', NewbornWardRoundRecordListView.as_view(), name='wardround-newborn-list'),
    # # 新生儿查房记录详情
    path('wardround/newborn/detail/<str:rid>/', NewbornWardRoundRecordDetailView.as_view(), name='wardround-newborn-detail'),
    # # 新生儿查房记录创建
    path('wardround/newborn/create/', NewbornWardRoundRecordCreateView.as_view(), name='wardround-newborn-create'),
    # # 新生儿查房记录更新
    path('wardround/newborn/update/<str:rid>/', NewbornWardRoundRecordUpdateView.as_view(), name='wardround-newborn-update'),
    # # 新生儿查房记录删除
    path('wardround/newborn/delete/<str:rid>/', NewbornWardRoundRecordDeleteView.as_view(), name='wardround-newborn-delete'),
    
    
    # 在住产妇选择列表
    path('wardround/maternity-select-list/', MaternitySelectListView.as_view(), name='postpartum-maternity-select-list'),
    # 新生儿选择列表
    path('wardround/newborn-select-list/<str:aid>/', NewbornSelectListView.as_view(), name='wardround-newborn-select-list'),
]