from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.core_records.models.baby import Newborn
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.ward_round.models import MaternityWardRoundRecord, NewBornWardRoundRecord
        
        

# 产妇查房记录列表序列化器
class MaternityWardRoundRecordListSerializer(serializers.ModelSerializer):
    
    record_time = ShanghaiFriendlyDateTimeField()
    
    maternity = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityWardRoundRecord
        fields = ['rid','record_time','maternity','doctor_type','doctor','has_abnormal','record_content']
    
    def get_maternity(self,obj):
        return f'{obj.maternity_admission.maternity.name}-({obj.maternity_admission.room.room_number})'

# 产妇查房录详情序列化器
class MaternityWardRoundRecordDetailSerializer(serializers.ModelSerializer):
    
    maternity_admission = serializers.SerializerMethodField()
    
    maternity = serializers.SerializerMethodField()

    created_at = ShanghaiFriendlyDateTimeField()
    
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = MaternityWardRoundRecord
        fields = ['rid','maternity_admission','maternity','record_time','doctor_type','doctor','has_abnormal','record_content','created_at','updated_at']


    def get_maternity_admission(self,obj):
        return obj.maternity_admission.aid
    
    def get_maternity(self,obj):
        return f'{obj.maternity_admission.maternity.name}-({obj.maternity_admission.room.room_number})'

        
# 产妇查房记录创建序列化器
class MaternityWardRoundRecordCreateSerializer(serializers.ModelSerializer):
    
    # 入院单
    maternity_admission = serializers.CharField() 
        
    class Meta:
        model = MaternityWardRoundRecord
        fields = ['maternity_center','maternity_admission','record_time','doctor_type','doctor','record_content','has_abnormal']
    
    def validate_maternity_admission(self,value):
        
        ma_ins = MaternityAdmission.get_maternity_admission_by_aid(value,self.context['maternity_center'])
        
        if not ma_ins:
            raise serializers.ValidationError("产妇入院记录不存在")
        
        return ma_ins
    

# 产妇查房记录更新序列化器
class MaternityWardRoundRecordUpdateSerializer(serializers.ModelSerializer):
    
    
    class Meta:
        model = MaternityWardRoundRecord
        fields = ['record_time','doctor_type','doctor','record_content','has_abnormal']
        
    
    












# 新生儿查房记录列表序列化器
class NewbornWardRoundRecordListSerializer(serializers.ModelSerializer):

    record_time = ShanghaiFriendlyDateTimeField()

    maternity = serializers.SerializerMethodField()
    
    newborn = serializers.SerializerMethodField()
    
    class Meta:
        model = NewBornWardRoundRecord
        fields = ['rid','record_time','maternity','newborn','doctor_type','doctor','has_abnormal','record_content']
    
    def get_maternity(self,obj):
        return f'{obj.maternity_admission.maternity.name}-({obj.maternity_admission.room.room_number})'

    def get_newborn(self,obj):
        return f'{obj.newborn.name}-({obj.newborn.hand_card_number or '无手卡号'})'
    
    
# 新生儿查房录详情序列化器
class NewbornWardRoundRecordDetailSerializer(serializers.ModelSerializer):
    
    maternity_admission = serializers.SerializerMethodField()
    
    maternity = serializers.SerializerMethodField()
    
    newborn = serializers.SerializerMethodField()
    
    nid = serializers.SerializerMethodField()
    

    created_at = ShanghaiFriendlyDateTimeField()
    
    updated_at = ShanghaiFriendlyDateTimeField()
    
    record_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = NewBornWardRoundRecord
        fields = ['rid','maternity_admission','maternity','nid','newborn','record_time','doctor_type','doctor','has_abnormal','record_content','created_at','updated_at']


    def get_maternity_admission(self,obj):
        return obj.maternity_admission.aid
    
    def get_maternity(self,obj):
        return f'{obj.maternity_admission.maternity.name}-({obj.maternity_admission.room.room_number})'
    
    def get_newborn(self,obj):
        return f'{obj.newborn.name}-({obj.newborn.hand_card_number or '无手卡号'})'
    
    def get_nid(self,obj):
        return obj.newborn.nid
    
# 产妇查房记录创建序列化器
class NewbornWardRoundRecordCreateSerializer(serializers.ModelSerializer):
    
    # 入院单
    maternity_admission = serializers.CharField() 
    # 新生儿
    newborn = serializers.CharField()
        
    class Meta:
        model = NewBornWardRoundRecord
        fields = ['maternity_center','maternity_admission','newborn','record_time','doctor_type','doctor','record_content','has_abnormal']
    
    def validate_maternity_admission(self,value):
        
        ma_ins = MaternityAdmission.get_maternity_admission_by_aid(value,self.context['maternity_center'])
        
        if not ma_ins:
            raise serializers.ValidationError("产妇入院记录不存在")
        
        return ma_ins
    
    def validate_newborn(self,value):
        
        newborn = Newborn.get_newborn_by_nid(value,self.context['maternity_center'])
        
        if not newborn:
            raise serializers.ValidationError("新生儿不存在")
        
        return newborn


# 新生儿查房记录更新序列化器
class NewbornWardRoundRecordUpdateSerializer(serializers.ModelSerializer):
    
    
    class Meta:
        model = NewBornWardRoundRecord
        fields = ['record_time','doctor_type','doctor','record_content','has_abnormal']
            
    
    
    
    

    

# 在住产妇选择列表序列化器
class MaternitySelectListSerializer(serializers.ModelSerializer):
    
    maternity = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityAdmission
        fields = ['aid','maternity']
    
    def get_maternity(self,obj):
        
        name = obj.maternity.name or '-'
        room_number = obj.room.room_number or '-'
        
        return f'{name}-({room_number})'
    
# 新生儿选择列表序列化器
class NewbornSelectListSerializer(serializers.ModelSerializer):
    
    newborn = serializers.SerializerMethodField()
    
    class Meta:
        model = Newborn
        fields = ['nid','newborn']
    
    def get_newborn(self,obj):
        return f'{obj.name}-({obj.hand_card_number or '无手卡号'})'