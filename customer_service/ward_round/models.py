from django.db import models

from core.generate_hashid import generate_resource_uuid
from customer_service.core_records.models.baby import Newborn
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from core.model import BaseModel
from maternity_center.models import MaternityCenter


# 产妇查房记录
class  MaternityWardRoundRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name="ward_round_records")
    # 产妇入院单
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="产妇入院单",related_name="ward_round_records")
    # 查房时间
    record_time = models.DateTimeField(verbose_name="查房时间")
    # 查房医生类型
    doctor_type = models.CharField(max_length=20, verbose_name="查房医生类型")
    # 查房医生
    doctor = models.CharField(max_length=20, verbose_name="查房医生")
    # 有无异常
    has_abnormal = models.BooleanField(verbose_name="有无异常")
    # 查房结果
    record_content = models.TextField(verbose_name="查房结果")
    # rid
    rid = models.CharField(max_length=100,verbose_name="rid",help_text="查房记录资源 id",default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "查房记录"
        verbose_name_plural = "查房记录"
        
    def __str__(self):
        return f"{self.maternity_admission.maternity.name} 查房记录"
    
    @classmethod
    def get_by_rid(cls,rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
        
# 新生儿查房记录
class NewBornWardRoundRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name="newborn_ward_round_records")
    # 产妇入院单
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="产妇入院单",related_name="newborn_ward_round_records")
    # 新生儿
    newborn = models.ForeignKey(Newborn, on_delete=models.CASCADE, verbose_name="新生儿",related_name="newborn_ward_round_records")
    # 查房时间
    record_time = models.DateTimeField(verbose_name="查房时间")
    # 查房医生类型
    doctor_type = models.CharField(max_length=20, verbose_name="查房医生类型")
    # 查房医生
    doctor = models.CharField(max_length=20, verbose_name="查房医生")
    # 有无异常
    has_abnormal = models.BooleanField(verbose_name="有无异常")
    # 查房结果
    record_content = models.TextField(verbose_name="查房结果")
    # rid
    rid = models.CharField(max_length=100,verbose_name="rid",help_text="查房记录资源 id",default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "新生儿查房记录"
        verbose_name_plural = "新生儿查房记录"
        
    def __str__(self):
        return f"{self.maternity_admission.maternity.name} 新生儿查房记录"
    
    @classmethod
    def get_by_rid(cls,rid,maternity_center):
        try:
            return cls.objects.select_related('maternity_admission__maternity','maternity_admission__room','newborn').get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
