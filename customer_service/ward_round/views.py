from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import CheckInStatusEnum
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.baby import Newborn
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from .models import MaternityWardRoundRecord, NewBornWardRoundRecord
from .serializers import MaternityWardRoundRecordCreateSerializer, MaternityWardRoundRecordDetailSerializer, MaternityWardRoundRecordListSerializer, MaternityWardRoundRecordUpdateSerializer, NewbornSelectListSerializer, MaternitySelectListSerializer, NewbornWardRoundRecordCreateSerializer, NewbornWardRoundRecordDetailSerializer, NewbornWardRoundRecordListSerializer, NewbornWardRoundRecordUpdateSerializer
from permissions.enum import PermissionEnum


# 产妇查房项目列表
class MaternityWardRoundRecordListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_VIEW
    serializer_class = MaternityWardRoundRecordListSerializer
    response_msg = "获取产妇查房记录列表成功"
    error_response_msg = "获取产妇查房记录列表失败"
    search_fields = ['maternity_admission__maternity__name','maternity_admission__room__room_number','doctor']
    
    def get_queryset(self):
        
        base_queryset = MaternityWardRoundRecord.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-record_time')
        
        has_abnormal = self.request.query_params.get('has_abnormal',None)
        
        if has_abnormal:
            base_queryset = base_queryset.filter(has_abnormal=has_abnormal)

        return base_queryset


# 产妇查房记录详情
class MaternityWardRoundRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_VIEW
    
    def get(self,request,rid):
        
        record = MaternityWardRoundRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not record:
            return make_response(code=-1,msg='产妇查房记录不存在')
        
        return make_response(code=0,msg='获取产妇查房记录详情成功',data=MaternityWardRoundRecordDetailSerializer(record).data)


# 产妇查房记录创建
class MaternityWardRoundRecordCreateView(APIView): 
    
    authentication_classes = [CareCenterAuthentication] 
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_EDIT
    
    def post(self,request):
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = MaternityWardRoundRecordCreateSerializer(data=data,context={'maternity_center':request.user.maternity_center})
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='查房记录创建成功',data=MaternityWardRoundRecordDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='查房记录创建失败' )
        

# 产妇查房记录更新
class MaternityWardRoundRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_EDIT
    
    def put(self,request,rid):
        record = MaternityWardRoundRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not record:
            return make_response(code=-1,msg='产妇查房记录不存在')
        
        data = request.data.copy()
                
        serializer = MaternityWardRoundRecordUpdateSerializer(record,data=data)
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='产妇查房记录更新成功',data=MaternityWardRoundRecordDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='产妇查房记录更新失败')


# 产妇查房记录删除
class MaternityWardRoundRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_EDIT
    
    def delete(self,request,rid):
        ins = MaternityWardRoundRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not ins:
            return make_response(code=-1,msg="产妇查房记录不存在")
        
        ins.delete()
        
        return make_response(code=0,msg='产妇查房记录删除成功')
    

# 新生儿查房项目列表
class NewbornWardRoundRecordListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_VIEW
    serializer_class = NewbornWardRoundRecordListSerializer
    response_msg = "获取新生儿查房记录列表成功"
    error_response_msg = "获取新生儿查房记录列表失败"
    search_fields = ['maternity_admission__maternity__name','maternity_admission__room__room_number','doctor','newborn__name','newborn__hand_card_number']
    
    def get_queryset(self):
        
        base_queryset = NewBornWardRoundRecord.objects.select_related('maternity_admission__maternity','maternity_admission__room','newborn').filter(maternity_center=self.request.user.maternity_center).order_by('-record_time')
        
        has_abnormal = self.request.query_params.get('has_abnormal',None)
        
        if has_abnormal:
            base_queryset = base_queryset.filter(has_abnormal=has_abnormal)

        return base_queryset   


# 新生儿查房记录详情
class NewbornWardRoundRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_VIEW
    
    def get(self,request,rid):
        
        record = NewBornWardRoundRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not record:
            return make_response(code=-1,msg='新生儿查房记录不存在')
        
        return make_response(code=0,msg='获取新生儿查房记录详情成功',data=NewbornWardRoundRecordDetailSerializer(record).data)


# 新生儿查房记录创建
class NewbornWardRoundRecordCreateView(APIView): 
    
    authentication_classes = [CareCenterAuthentication] 
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_EDIT
    
    def post(self,request):
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = NewbornWardRoundRecordCreateSerializer(data=data,context={'maternity_center':request.user.maternity_center})
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='新生儿查房记录创建成功',data=NewbornWardRoundRecordDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='新生儿查房记录创建失败')


# 新生儿查房记录更新
class NewbornWardRoundRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_EDIT
    
    def put(self,request,rid):
        record = NewBornWardRoundRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not record:
            return make_response(code=-1,msg='新生儿查房记录不存在')
        
        data = request.data.copy()
        
        serializer = NewbornWardRoundRecordUpdateSerializer(record,data=data)
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='新生儿查房记录更新成功',data=NewbornWardRoundRecordDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='新生儿查房记录更新失败')
    

# 新生儿查房记录删除

class NewbornWardRoundRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_EDIT
    
    def delete(self,request,rid):
        ins = NewBornWardRoundRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not ins:
            return make_response(code=-1,msg="新生儿查房记录不存在")
        
        ins.delete()
        
        return make_response(code=0,msg='新生儿查房记录删除成功')


# 在住产妇选择列表
class MaternitySelectListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_VIEW
    
    def get(self,request):
        
        queryset = MaternityAdmission.objects.filter(maternity_center=request.user.maternity_center,check_in_status=CheckInStatusEnum.CHECKED_IN).order_by('-actual_check_in_date')
        
        return make_response(code=0,msg='获取在住产妇选择列表成功',data=MaternitySelectListSerializer(queryset,many=True).data)
        
# 新生儿选择列表
class NewbornSelectListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.WARD_ROUND_VIEW
    
    def get(self,request,aid):
        
        queryset = Newborn.objects.select_related('maternity_admission').filter(
            maternity_admission__maternity_center=request.user.maternity_center,
            maternity_admission__aid=aid
            ).order_by('-birth_time')
        
        return make_response(code=0,msg='获取新生儿选择列表成功',data=NewbornSelectListSerializer(queryset,many=True).data)