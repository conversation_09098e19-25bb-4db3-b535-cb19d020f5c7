from rest_framework import serializers

from core.enum import ApprovalStatusEnum
from core.parse_time import ShanghaiFriendlyDateTimeField
from core.utils import calculate_age
from customer_service.outing_management.enum import OutingStatusEnum
from customer_service.outing_management.models import OutingApplication


# 外出申请列表序列化器
class OutingApplicationSerializer(serializers.ModelSerializer):
    
    outing_time = ShanghaiFriendlyDateTimeField()
    expected_return_time = ShanghaiFriendlyDateTimeField()
    actual_return_time = ShanghaiFriendlyDateTimeField()
    
    
    maternity_name = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    
    approval_status_label = serializers.SerializerMethodField()
    
    outing_status_label = serializers.SerializerMethodField()
    
    class Meta:
        model = OutingApplication
        fields = ["oid","maternity_name","room_number","outing_time","expected_return_time","actual_return_time",
                  "outing_reason","need_accompany","approval_status","approval_status_label","outing_status","outing_status_label"]

    
    def get_approval_status_label(self, obj):
        return ApprovalStatusEnum(obj.approval_status).label
    
    def get_maternity_name(self, obj):
        return obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else "-"
    
    def get_room_number(self, obj):
        return obj.maternity_admission.room.room_number if obj.maternity_admission.room else "-"
    
    def get_outing_status_label(self, obj):
        return OutingStatusEnum(obj.outing_status).label
    

# 外出申请详情序列化器
class OutingApplicationDetailSerializer(serializers.ModelSerializer):
    
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    application_time = ShanghaiFriendlyDateTimeField()
    outing_time = ShanghaiFriendlyDateTimeField()
    expected_return_time = ShanghaiFriendlyDateTimeField()
    actual_return_time = ShanghaiFriendlyDateTimeField()
    approval_time = ShanghaiFriendlyDateTimeField()
    approval_status_label = serializers.SerializerMethodField()
    maternity_name = serializers.SerializerMethodField()
    maternity_age = serializers.SerializerMethodField()
    outing_status_label = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    approval_by = serializers.SerializerMethodField()
    accompany_staff_name = serializers.SerializerMethodField()
    accompany_staff = serializers.SerializerMethodField()
    aid = serializers.SerializerMethodField()

    
    class Meta:
        model = OutingApplication
        exclude = ["maternity_center",'id','maternity_admission']

    def get_maternity_name(self, obj):
        return obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else "-"
    
    def get_maternity_age(self, obj):
        return calculate_age(obj.maternity_admission.maternity.birth_date) if obj.maternity_admission.maternity else "-"
    
    def get_outing_status_label(self, obj):
        return OutingStatusEnum(obj.outing_status).label
    
    def get_room_number(self, obj):
        return obj.maternity_admission.room.room_number if obj.maternity_admission.room else "-"
    
    def get_approval_status_label(self, obj):
        return ApprovalStatusEnum(obj.approval_status).label
    
    def get_approval_by(self, obj):
        return obj.approval_by.name if obj.approval_by else "-"
    
    def get_accompany_staff_name(self, obj):
        return obj.accompany_staff.name if obj.accompany_staff else "-"
    
    def get_accompany_staff(self, obj):
        return obj.accompany_staff.sid if obj.accompany_staff else "-"
    
    def get_aid(self, obj):
        return obj.maternity_admission.aid if obj.maternity_admission else None
    
# 外出申请创建序列化器
class OutingApplicationCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = OutingApplication
        fields = ["maternity_center","maternity_admission","outing_time","expected_return_time","outing_reason",
                  "need_accompany","accompany_staff","remark","emergency_contact_phone"]
        
# 外出申请更新序列化器
class OutingApplicationUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = OutingApplication
        fields = ["outing_time","expected_return_time","outing_reason",
                  "need_accompany","accompany_staff","remark","emergency_contact_phone"]