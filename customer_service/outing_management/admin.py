from django.contrib import admin

from customer_service.outing_management.models import OutingApplication


@admin.register(OutingApplication)
class OutingApplicationAdmin(admin.ModelAdmin):
    list_display = ["get_maternity_name", "get_room_number", "outing_time", "expected_return_time", "actual_return_time", "outing_reason", "need_accompany", "approval_status"]

    def get_maternity_name(self, obj):
        if obj.maternity_admission and obj.maternity_admission.maternity:
            return obj.maternity_admission.maternity.name
        return "-"
    get_maternity_name.short_description = "产妇姓名"
    
    def get_room_number(self, obj):
        if obj.maternity_admission and obj.maternity_admission.room:
            return obj.maternity_admission.room.room_number
        return "-"
    get_room_number.short_description = "房间号"
    