from dirtyfields import DirtyFieldsMixin
from django.db import models

from core.enum import ApprovalStatusEnum
from core.generate_hashid import generate_outing_application_code
from core.model import BaseModel
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.outing_management.enum import OutingStatusEnum
from maternity_center.models import MaternityCenter
from user.models import Staff


# 外出申请单
class OutingApplication(DirtyFieldsMixin,BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name='outing_applications')
    # 入住单
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入住单",related_name='outing_applications')
    # 外出时间
    outing_time = models.DateTimeField(verbose_name="外出时间")
    # 预计返回时间
    expected_return_time = models.DateTimeField(verbose_name="预计返回时间")
    # 实际返回时间
    actual_return_time = models.DateTimeField(verbose_name="实际返回时间",blank=True,null=True)
    # 外出原因
    outing_reason = models.TextField(verbose_name="外出原因",blank=True,default='')
    # 需要陪同
    need_accompany = models.BooleanField(verbose_name="需要陪同",default=False)
    # 陪同人员
    accompany_staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="陪同人员",blank=True,null=True,related_name='accompanied_outing_applications')
    # 备注说明
    remark = models.TextField(verbose_name="备注说明",blank=True,default='')
    # 紧急联系电话
    emergency_contact_phone = models.CharField(max_length=20, verbose_name="紧急联系电话",blank=True,default='')
    # 外出状态
    outing_status = models.CharField(max_length=20, verbose_name="外出状态", choices=OutingStatusEnum.choices,default=OutingStatusEnum.NOT_OUT)
    # 申请时间
    application_time = models.DateTimeField(verbose_name="申请时间",auto_now_add=True)
    # 审核状态
    approval_status = models.CharField(max_length=20, verbose_name="审核状态", choices=ApprovalStatusEnum.choices,default=ApprovalStatusEnum.PENDING)
    # 审核时间
    approval_time = models.DateTimeField(verbose_name="审核时间",blank=True,null=True)
    # 审核人员
    approval_by = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="审核人员",blank=True,null=True)
    # 返回备注
    return_remark = models.TextField(verbose_name="返回备注",blank=True,default='')
    # 外出申请单号
    oid = models.CharField(verbose_name="外出申请单号",max_length=20,blank=True,unique=True,default=generate_outing_application_code)
    
    class Meta:
        verbose_name = "外出申请单"
        verbose_name_plural = "外出申请单"

    def __str__(self):
        return f"{self.maternity_admission.maternity.name} - {self.maternity_admission.room.room_number} - {self.outing_time}"
    
    
    @staticmethod
    def has_pending_outing(maternity_admission):
        return OutingApplication.objects.filter(maternity_admission=maternity_admission,approval_status=ApprovalStatusEnum.PENDING).exists()