from django.urls import path

from .views import (
    OutingApplicationListView, OutingApplicationDetailView, OutingApplicationCreateView, OutingApplicationUpdateView,
    OutingApplicationDeleteView,
    OutingApplicationAuditView, OutingApplicationReturnView
)

urlpatterns = [
    # 外出申请列表
    path('outing-application/list/', OutingApplicationListView.as_view(), name='outing-application-list'),
    # 外出申请详情
    path('outing-application/detail/<str:oid>/', OutingApplicationDetailView.as_view(), name='outing-application-detail'),
    # 外出申请创建
    path('outing-application/create/<str:aid>/', OutingApplicationCreateView.as_view(), name='outing-application-create'),
    # 外出申请更新
    path('outing-application/update/<str:oid>/', OutingApplicationUpdateView.as_view(), name='outing-application-update'),
    # 外出申请删除
    path('outing-application/delete/<str:oid>/', OutingApplicationDeleteView.as_view(), name='outing-application-delete'),
    # 外出申请审核
    path('outing-application/audit/<str:oid>/', OutingApplicationAuditView.as_view(), name='outing-application-audit'),
    # 外出返回
    path('outing-application/return/<str:oid>/', OutingApplicationReturnView.as_view(), name='outing-application-return'),
]