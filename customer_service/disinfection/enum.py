from django.db import models


# 消毒规范文档状态
class DisinfectionDocumentStatusEnum(models.TextChoices):
    # 草稿
    DRAFT = 'DRAFT', '草稿'
    # 生效中
    IN_EFFECT = 'IN_EFFECT', '生效中'
    # 已停用
    DISABLED = 'DISABLED', '已停用'
    

# 执行结果枚举
class CleanDisinfectionResultEnum(models.TextChoices):
    # 未执行
    NOT_EXECUTED = 'NOT_EXECUTED', '未执行'
    # 等待结果中
    PENDING = 'PENDING', '等待中'
    # 合格
    PASS = 'PASS', '合格'
    # 不合格
    FAIL = 'FAIL', '不合格'
    
    