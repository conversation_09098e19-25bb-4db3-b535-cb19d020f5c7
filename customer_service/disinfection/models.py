from django.db import models

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from customer_service.disinfection.enum import CleanDisinfectionResultEnum, DisinfectionDocumentStatusEnum
from maternity_center.models import MaternityCenter
from user.models import Staff


# 消毒规范文档
class DisinfectionDocument(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 文件
    file = models.TextField(verbose_name="文件")
    # 标题
    name = models.CharField(max_length=100, verbose_name="标题")
    # 版本号
    version = models.CharField(max_length=100, verbose_name="版本号")
    # 适用范围
    applicable_scope = models.TextField(verbose_name="适用范围")
    # 发布日期
    publish_date = models.DateField(verbose_name="发布日期")
    # 描述
    description = models.TextField(verbose_name="描述")
    # 状态
    status = models.CharField(max_length=30, verbose_name="状态", choices=DisinfectionDocumentStatusEnum.choices)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "消毒规范文档"
        verbose_name_plural = "消毒规范文档"
        
    def __str__(self):
        return self.name
    
    @classmethod
    def get_disinfection_document_by_rid(cls,rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
    @classmethod
    def check_duplicate_name(cls,name,maternity_center,exclude_rid=None):
        if exclude_rid:
            return cls.objects.filter(name=name,maternity_center=maternity_center).exclude(rid=exclude_rid).exists()
        else:
            return cls.objects.filter(name=name,maternity_center=maternity_center).exists()
        
        
# 清洁消毒记录
class CleanDisinfectionRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 清洁区域
    clean_area = models.CharField(max_length=100, verbose_name="清洁区域")
    # 清洁类型
    clean_type = models.CharField(max_length=100, verbose_name="清洁类型")
    # 参照消毒规范文档
    disinfection_document = models.ForeignKey(DisinfectionDocument, on_delete=models.SET_NULL, verbose_name="参照消毒规范文档", null=True)
    # 清洁时间
    clean_time = models.DateTimeField(verbose_name="清洁时间")
    # 清洁人
    cleaner = models.CharField(max_length=100, verbose_name="清洁人")
    # 监督人
    supervisor = models.CharField(max_length=100, verbose_name="监督人")
    # 清洁结果
    result = models.CharField(max_length=100, verbose_name="清洁结果", choices=CleanDisinfectionResultEnum.choices)
    # 消毒剂名称/浓度/用量
    disinfectant_info = models.CharField(max_length=200, verbose_name="消毒剂名称/浓度/用量")
    # 清洁/消毒过程简述
    disinfection_process = models.TextField(verbose_name="清洁/消毒过程简述")
    # 备注信息
    remark = models.TextField(verbose_name="备注信息")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="创建人", null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    
    class Meta:
        verbose_name = "清洁消毒记录"
        verbose_name_plural = "清洁消毒记录"
        
    def __str__(self):
        return self.clean_area
    
    @classmethod
    def get_clean_disinfection_record_by_rid(cls,rid,maternity_center):
        try:
            return cls.objects.select_related("disinfection_document").get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
