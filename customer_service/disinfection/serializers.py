from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.disinfection.models import CleanDisinfectionRecord, DisinfectionDocument
from file.models import DisinfectFile


# 消毒规范文库列表序列化器
class DisinfectionDocumentListSerializer(serializers.ModelSerializer):
    
    # 创建人名字
    creator_name = serializers.SerializerMethodField()
    # file_url
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = DisinfectionDocument
        fields = ['rid','name','version','file','file_url','publish_date','applicable_scope','status','creator_name']
        
    def get_creator_name(self,obj):
        return obj.creator.name if obj.creator else "N/A"
    
    def get_file_url(self,obj):
        file = DisinfectFile.get_by_rid(obj.file,obj.maternity_center)
        return file.file.url if file else None
        
# 消毒规范文库详情序列化器
class DisinfectionDocumentDetailSerializer(serializers.ModelSerializer):
    # 创建人姓名
    creator_name = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 上报状态
    status_display = serializers.SerializerMethodField()
    # file_url
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = DisinfectionDocument
        fields = ['rid','name','version','file','file_url','publish_date','applicable_scope','status','status_display','description','creator_name','created_at','updated_at']
        
    def get_creator_name(self,obj):
        return obj.creator.name if obj.creator else "N/A"
    
    def get_status_display(self,obj):
        return obj.get_status_display()
    
    def get_file_url(self,obj):
        file = DisinfectFile.get_by_rid(obj.file,obj.maternity_center)
        return file.file.url if file else None

# 创建消毒规范文库序列化器
class DisinfectionDocumentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = DisinfectionDocument
        fields = ['name','version','publish_date','applicable_scope','status','description','creator','maternity_center','file']
        
        
# 更新消毒规范文库序列化器
class DisinfectionDocumentUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = DisinfectionDocument
        fields = ['name','version','publish_date','applicable_scope','status','description','file']
        





# 清洁消毒记录列表序列化器
class CleanDisinfectionRecordListSerializer(serializers.ModelSerializer):
    
    # 参照消毒规范文档
    disinfection_document_name = serializers.SerializerMethodField()
    # clean_time 格式化
    clean_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = CleanDisinfectionRecord
        fields = ['rid','clean_area','clean_type','disinfection_document_name','clean_time','cleaner','disinfectant_info','result','supervisor']
        
    def get_creator_name(self,obj):
        return obj.creator.name if obj.creator else "N/A"
    
    def get_disinfection_document_name(self,obj):
        return obj.disinfection_document.name if obj.disinfection_document else "N/A"
        
# 清洁消毒记录详情序列化器
class CleanDisinfectionRecordDetailSerializer(serializers.ModelSerializer):
    # 创建人姓名
    creator_name = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 参照消毒规范文档
    disinfection_document = DisinfectionDocumentDetailSerializer()
    # 清洁结果
    result_display = serializers.SerializerMethodField()
    # clean_time 格式化
    clean_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = CleanDisinfectionRecord
        fields = ['rid','clean_area','clean_type','disinfection_document','clean_time','cleaner','disinfectant_info','disinfection_process','result','result_display','supervisor','remark','creator_name','created_at','updated_at']
        
    def get_creator_name(self,obj):
        return obj.creator.name if obj.creator else "N/A"
    
    def get_result_display(self,obj):
        return obj.get_result_display()
    
    
# 创建清洁消毒记录序列化器
class CleanDisinfectionRecordCreateSerializer(serializers.ModelSerializer):
    
    # 参照消毒规范文档
    disinfection_document = serializers.CharField(required=False)
    
    class Meta:
        model = CleanDisinfectionRecord
        fields = ['clean_area','clean_type','disinfection_document','clean_time','cleaner','disinfectant_info','disinfection_process','result','supervisor','remark','creator','maternity_center']

    def validate_disinfection_document(self,value):
        if value:
            didr = DisinfectionDocument.get_disinfection_document_by_rid(value,self.context['request'].user.maternity_center)
            if not didr:
                raise serializers.ValidationError("消毒规范文库不存在")
            return didr
        raise serializers.ValidationError("请选择消毒规范文库")


# 更新清洁消毒记录序列化器
class CleanDisinfectionRecordUpdateSerializer(serializers.ModelSerializer):
    
    # 参照消毒规范文档
    disinfection_document = serializers.CharField(required=False)
    
    class Meta:
        model = CleanDisinfectionRecord
        fields = ['clean_area','clean_type','disinfection_document','clean_time','cleaner','disinfectant_info','disinfection_process','result','supervisor','remark']
        
        
    def validate_disinfection_document(self,value):
        if value:
            didr = DisinfectionDocument.get_disinfection_document_by_rid(value,self.context['request'].user.maternity_center)
            if not didr:
                raise serializers.ValidationError("消毒规范文库不存在")
            return didr
        raise serializers.ValidationError("请选择消毒规范文库")