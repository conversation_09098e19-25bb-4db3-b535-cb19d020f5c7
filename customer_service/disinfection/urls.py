from django.urls import path

from .views import CleanDisinfectionRecordCreateView, CleanDisinfectionRecordDeleteView, \
    CleanDisinfectionRecordDetailView, CleanDisinfectionRecordListView, CleanDisinfectionRecordUpdateView, \
    DocumentCreateView, DocumentDeleteView, DocumentDetailView, DocumentListView, DocumentUpdateView

urlpatterns = [
    # 消毒规范文库列表
    path('disinfection-document/list/', DocumentListView.as_view(), name='disinfection_document_list'),
    # 消毒规范文库详情
    path('disinfection-document/detail/<str:rid>/', DocumentDetailView.as_view(), name='disinfection_document_detail'),
    # 创建消毒规范文库
    path('disinfection-document/create/', DocumentCreateView.as_view(), name='disinfection_document_create'),
    # 消毒规范文库更新
    path('disinfection-document/update/<str:rid>/', DocumentUpdateView.as_view(), name='disinfection_document_update'),
    # 删除消毒规范文库
    path('disinfection-document/delete/<str:rid>/', DocumentDeleteView.as_view(), name='disinfection_document_delete'),
    
    
    # 清洁消毒记录列表
    path('disinfection/list/', CleanDisinfectionRecordListView.as_view(), name='disinfection_record_list'),
    # 清洁消毒记录详情
    path('disinfection/detail/<str:rid>/', CleanDisinfectionRecordDetailView.as_view(), name='disinfection_record_detail'),
    # 创建清洁消毒记录
    path('disinfection/create/', CleanDisinfectionRecordCreateView.as_view(), name='disinfection_record_create'),
    # 清洁消毒记录详情
    path('disinfection/update/<str:rid>/', CleanDisinfectionRecordUpdateView.as_view(), name='disinfection_record_update'),
    # 删除清洁消毒记录
    path('disinfection/delete/<str:rid>/', CleanDisinfectionRecordDeleteView.as_view(), name='disinfection_record_delete'),
]