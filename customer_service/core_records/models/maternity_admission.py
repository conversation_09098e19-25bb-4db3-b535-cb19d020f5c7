from datetime import datetime, timedelta

from django.db import models
from django.db.models import Q
from django.utils import timezone

from core.enum import CheckInStatusEnum, DeliveryMethodEnum, GenderEnum
from core.generate_hashid import generate_maternity_admission_code, generate_maternity_recent_activity_code, generate_resource_uuid
from core.model import BaseModel
from customer_service.core_records.enums.maternal import CheckInSourceEnum
from customer_service.core_records.utils import format_relative_time
from customer_service.room.models import Room
from maternity_center.models import MaternityCenter
from user.models import Maternity, Staff


# 产妇入院记录
class MaternityAdmission(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心")
    # 产妇
    maternity = models.ForeignKey(Maternity, on_delete=models.SET_NULL, verbose_name="产妇", blank=True, null=True,related_name="maternity_admission")
    # 预计分娩日期
    expected_delivery_date = models.DateField(verbose_name="预计分娩日期", blank=True, null=True)
    # 实际分娩日期
    actual_delivery_date = models.DateField(verbose_name="实际分娩日期", blank=True, null=True)
    # 分娩医院
    delivery_hospital = models.CharField(max_length=100, verbose_name="分娩医院", blank=True, default="")
    # 分娩方式
    delivery_method = models.CharField(max_length=20, choices=DeliveryMethodEnum.choices, default=DeliveryMethodEnum.UNBORN, verbose_name="分娩方式")
    # 孕周（非负整数）
    pregnancy_week = models.PositiveIntegerField(verbose_name="孕周", blank=True, null=True)
    # 过敏史
    allergy_history = models.TextField(verbose_name="过敏史", blank=True, default="")
    # 预计入住日期
    expected_check_in_date = models.DateField(verbose_name="预计入住日期", blank=True, null=True)
    # 实际入住日期
    actual_check_in_date = models.DateField(verbose_name="实际入住日期", blank=True, null=True)
    # 房间
    room = models.ForeignKey(Room, on_delete=models.SET_NULL, verbose_name="房间", blank=True, null=True,related_name="maternity_admission")
    # 合同编号
    contract_number = models.CharField(max_length=100, verbose_name="合同编号", blank=True, default="")
    # 合同文件
    contract_file = models.JSONField(default=dict, verbose_name="合同文件", blank=True)
    # 合同备注
    contract_remark = models.TextField(verbose_name="合同备注", blank=True, default="")
    # 入院来源
    check_in_source = models.CharField(max_length=20, verbose_name="入院来源", choices=CheckInSourceEnum.choices, default=CheckInSourceEnum.OTHER)
    # 分娩医院出院记录文件
    discharge_records_file = models.JSONField(default=dict, verbose_name="分娩医院出院记录文件", blank=True)
    # 出院记录备注
    discharge_records_remark = models.TextField(verbose_name="出院记录备注", blank=True, default="")
    # 入住状态
    check_in_status = models.CharField(verbose_name="入住状态", choices=CheckInStatusEnum.choices, default=CheckInStatusEnum.CHECKED_IN, max_length=20)
    # 需要关注
    need_attention = models.BooleanField(verbose_name="需要关注", default=False)
    # 主任护理
    chief_nurse = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="主任护理", blank=True, null=True, related_name="chief_nurse_maternity_admission")
    # 预计退房日期
    expected_check_out_date = models.DateField(verbose_name="预计退房日期", blank=True, null=True)
    # 实际退房日期
    actual_check_out_date = models.DateField(verbose_name="实际退房日期", blank=True, null=True)
    # 入住天数
    residence_days=models.IntegerField(verbose_name="入住天数")
    # 最后更新时间
    last_update_time = models.DateTimeField(verbose_name="最后更新时间", auto_now_add=True)
    # 编号
    aid = models.CharField(max_length=50, verbose_name="入院编号",blank=True, default=generate_maternity_admission_code)
    # creator 创建人
    creator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="创建人", blank=True, null=True, related_name="creator_maternity_admission")
    
    class Meta:
        verbose_name = "产妇入院记录"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.maternity.name} - {self.actual_check_in_date}"

    def update_expected_check_out_date(self):
        if self.expected_check_out_date is None:
            if self.residence_days and self.residence_days > 0:
                base_date = None
                if self.check_in_status == CheckInStatusEnum.CHECKED_IN and self.actual_check_in_date:
                    # 已入住状态：使用实际入住日期
                    base_date = self.actual_check_in_date
                elif self.expected_check_in_date:
                    # 预约状态：使用预计入住日期
                    base_date = self.expected_check_in_date

                if base_date:
                    expected_checkout = base_date + timedelta(days=self.residence_days)

                    self.expected_check_out_date = expected_checkout
                    self.save()

    def update_last_update_time(self):
        self.last_update_time = timezone.now()
        self.save()
        

    @staticmethod
    def get_maternity_admission_list(request):
        # 获取分页参数
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        search = request.query_params.get('sk', None)
        # 重点关注
        need_attention = request.query_params.get('need_attention', None)
        # 入住状态
        check_in_status = request.query_params.get('cis', None)
        # 预计入住日期
        expected_check_in_date = request.query_params.get('ecid', None)
        
        # 实际入住日期范围
        actual_check_in_date_start = request.query_params.get('acid_start', None)
        actual_check_in_date_end = request.query_params.get('acid_end', None)
    
        
         # 计算分页偏移量
        offset = (page - 1) * page_size
        limit = page_size
        
        admissions = MaternityAdmission.objects.filter(maternity_center=request.user.maternity_center).select_related(
            'maternity', 'room', 'chief_nurse'
        ).prefetch_related(
            'newborns_by_admission',
            'newborns_by_admission__newborn_check_in_assessments',
            'newborns_by_admission__newborn_care_one_records',
            'newborns_by_admission__newborn_care_two_records',
            'newborns_by_admission__newborn_feeding_records',
            'newborns_by_admission__newborn_care_operation_records',
            'newborns_by_admission__newborn_month_assessments',
            'maternity_check_in_assessments',
            'maternity_rehabilitation_assessments',
            'maternity_daily_physical_care_records',
            'maternity_diet_records',
        )
        
        if search:
            admissions = admissions.filter(
                Q(maternity__name__icontains=search) |
                Q(room__room_number__icontains=search) |
                Q(newborns_by_admission__name__icontains=search)
            )
        
        if need_attention:
            admissions = admissions.filter(need_attention=need_attention == 'true')
        
        if check_in_status:
            admissions = admissions.filter(check_in_status=check_in_status)
            
        # 判断是否是日期格式
        if expected_check_in_date:
            try:
                expected_check_in_date = datetime.strptime(expected_check_in_date, '%Y-%m-%d')
            except ValueError:
                return False, '预计入住日期格式错误'
            admissions = admissions.filter(expected_check_in_date__gte=expected_check_in_date)

        
        
        if actual_check_in_date_start and actual_check_in_date_end:
            try:
                actual_check_in_date_start = datetime.strptime(actual_check_in_date_start, '%Y-%m-%d')
                actual_check_in_date_end = datetime.strptime(actual_check_in_date_end, '%Y-%m-%d')
            except ValueError:
                return False, '入住日期范围筛选格式错误'
            
            admissions = admissions.filter(
                Q(actual_check_in_date__gte=actual_check_in_date_start) &
                Q(actual_check_in_date__lte=actual_check_in_date_end)
            )
        
        resData = []
        listData = []
        
        # 获取总记录数（用于计算总页数）
        total_count = admissions.distinct().count()
        
        for admission in admissions.distinct().order_by('-created_at')[offset:offset+limit]:
            # 预计算新生儿数
            newborns = list(admission.newborns_by_admission.all())
            newborn_count = len(newborns)
            
            # 计算记录总数
            records_count = (
                len(admission.maternity_check_in_assessments.all()) +
                len(admission.maternity_rehabilitation_assessments.all()) +
                len(admission.maternity_daily_physical_care_records.all()) +
                len(admission.maternity_diet_records.all()) 
            )
            
            newborns_info = []
            
            # 计算新生儿记录数
            for newborn in newborns:
                records_count += (
                    len(newborn.newborn_check_in_assessments.all()) +
                    len(newborn.newborn_care_one_records.all()) +
                    len(newborn.newborn_care_two_records.all()) +
                    len(newborn.newborn_feeding_records.all()) +
                    len(newborn.newborn_care_operation_records.all()) +
                    len(newborn.newborn_month_assessments.all())
                )
            
            # 构建新生儿信息列表
            newborns_info = [
                f'{newborn.name} ({GenderEnum(newborn.gender).label})'
                for newborn in newborns
            ]
            
            listData.append({
                'aid': admission.aid,
                'maternity': admission.maternity.name if admission.maternity else '',
                'phone': admission.maternity.phone if admission.maternity else '',
                'is_multiple_birth': newborn_count > 1,
                'need_attention': admission.need_attention,
                'check_in_status': CheckInStatusEnum(admission.check_in_status).label,
                'newborns': newborns_info,
                'room_number': admission.room.room_number if admission.room else '暂无',
                'expected_check_in_date': admission.expected_check_in_date,
                'actual_check_in_date': admission.actual_check_in_date,
                'expected_check_out_date': admission.expected_check_out_date,
                'actual_check_out_date': admission.actual_check_out_date,
                'main_nurse': admission.chief_nurse.name if admission.chief_nurse else '',
                'stay_days': (timezone.now().date() - admission.actual_check_in_date).days if admission.actual_check_in_date else 0,
                'records_count': records_count,
                'last_update_time': format_relative_time(admission.last_update_time)
            })
            
        resData = {
            'list': listData,
            'total_count': total_count,
            'page': page,
            'page_size': page_size,
            'total_page': (total_count + page_size - 1) // page_size        
}
        return True, resData
    
    
    # 获取单个用户的数据
    @staticmethod
    def get_maternity_admission_detail_by_id(request,admission_id):
        if not admission_id:
            return False, '数据校验失败'
   
        
        admission = MaternityAdmission.objects.filter(id=admission_id,maternity_center=request.user.maternity_center).select_related(
            'maternity', 'room', 'chief_nurse'
        ).prefetch_related(
            'newborns_by_admission',
            'newborns_by_admission__newborn_check_in_assessments',
            'newborns_by_admission__newborn_care_one_records',
            'newborns_by_admission__newborn_care_two_records',
            'newborns_by_admission__newborn_feeding_records',
            'newborns_by_admission__newborn_care_operation_records',
            'newborns_by_admission__newborn_month_assessments',
            'maternity_check_in_assessments',
            'maternity_rehabilitation_assessments',
            'maternity_daily_physical_care_records',
            'maternity_diet_records',
        ).first()
        

        
        newborns = list(admission.newborns_by_admission.all())
        newborn_count = len(newborns)
            
        # 计算记录总数
        records_count = (
            len(admission.maternity_check_in_assessments.all()) +
            len(admission.maternity_rehabilitation_assessments.all()) +
            len(admission.maternity_daily_physical_care_records.all()) +
            len(admission.maternity_diet_records.all()) 
        )
        
        newborns_info = []
            
        # 计算新生儿记录数
        for newborn in newborns:
            records_count += (
                len(newborn.newborn_check_in_assessments.all()) +
                len(newborn.newborn_care_one_records.all()) +
                len(newborn.newborn_care_two_records.all()) +
                len(newborn.newborn_feeding_records.all()) +
                len(newborn.newborn_care_operation_records.all()) +
                len(newborn.newborn_month_assessments.all())
            )
            
            # 构建新生儿信息列表
            newborns_info = [
                f'{newborn.name} ({GenderEnum(newborn.gender).label})'
                for newborn in newborns
            ]
            
        resData = {
            'aid': admission.aid,
            'maternity': admission.maternity.name if admission.maternity else '',
            'phone': admission.maternity.phone if admission.maternity else '',
            'is_multiple_birth': newborn_count > 1,
            'need_attention': admission.need_attention,
            'check_in_status': CheckInStatusEnum(admission.check_in_status).label,
            'newborns': newborns_info,
            'room_number': admission.room.room_number if admission.room else '暂无',
            'expected_check_in_date': admission.expected_check_in_date,
            'actual_check_in_date': admission.actual_check_in_date,
            'expected_check_out_date': admission.expected_check_out_date,
            'actual_check_out_date': admission.actual_check_out_date,
            'main_nurse': admission.chief_nurse.name if admission.chief_nurse else '',
            'stay_days': (timezone.now().date() - admission.actual_check_in_date).days if admission.actual_check_in_date else 0,
            'records_count': records_count,
            'last_update_time': format_relative_time(admission.last_update_time)
        }
            
        return True, resData
    
    
    @classmethod
    def check_maternity_admission_exists(cls,maternity_admission_id,maternity_center_id):
        return cls.objects.filter(id=maternity_admission_id,maternity_center=maternity_center_id).exists()
    
        
    @staticmethod
    def get_maternity_admission_by_aid(aid,maternity_center,maternity=None):
        try:
            if maternity:
                return MaternityAdmission.objects.get(aid=aid,maternity_center=maternity_center,maternity=maternity)
            else:
                return MaternityAdmission.objects.get(aid=aid,maternity_center=maternity_center)
        except MaternityAdmission.DoesNotExist:
            return None
    
    @staticmethod
    def get_maternity_name_by_id(id):
        try:
            return MaternityAdmission.objects.get(id=id).maternity.name
        except MaternityAdmission.DoesNotExist:
            return None
        

# 续住入院记录
class MaternityAdmissionRenew(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name="maternity_admission_renew")
    # 入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入院记录",related_name="maternity_admission_renew")
    # 续住天数
    renew_days = models.IntegerField(verbose_name="续住天数")
    # 续住原因
    renew_reason = models.TextField(verbose_name="续住原因", blank=True, default="")
    # 原预计退房日期
    original_expected_check_out_date = models.DateField(verbose_name="原预计退房日期")
    # 续住后预计退房日期
    new_expected_check_out_date = models.DateField(verbose_name="续住后预计退房日期")
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "续住入院记录"
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return f"{self.maternity_admission.maternity.name} - {self.renew_days}天"



# 最近活动
class MaternityRecentActivity(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name="maternity_recent_activity")
    # 入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入院记录",related_name="maternity_recent_activity")
    # 活动类型
    activity_type = models.CharField(max_length=100, verbose_name="活动类型", )
    # 活动时间
    activity_time = models.DateTimeField(verbose_name="活动时间", auto_now_add=True)
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_maternity_recent_activity_code)
    # 创建人
    creator = models.CharField(max_length=100, verbose_name="创建人")
    
    class Meta:
        verbose_name = "最近活动"
        verbose_name_plural = verbose_name
        
    @staticmethod
    def create_record(maternity_center_id,maternity_admission_id,activity_type,creator):
        MaternityRecentActivity.objects.create(
            maternity_center_id=maternity_center_id,
            maternity_admission_id=maternity_admission_id,
            activity_type=activity_type,
            creator=creator
        )
        