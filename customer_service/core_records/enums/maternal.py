from django.db import models

# 入院来源枚举
class CheckInSourceEnum(models.TextChoices):
    # 院内转入
    IN_HOSPITAL = "IN_HOSPITAL", "院内转入"
    # 一联体转入
    OUT_HOSPITAL = "OUT_HOSPITAL", "一联体转入"
    # 其他
    OTHER = "OTHER", "其他"

# 情绪枚举
class EmotionEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 稳定
    STABLE = "STABLE", "稳定"
    # 兴奋
    EXCITED = "EXCITED", "兴奋"
    # 郁闷
    DEPRESSED = "DEPRESSED", "郁闷"
    # 焦虑
    ANXIETY = "ANXIETY", "焦虑"
    # 恐惧
    FEAR = "FEAR", "恐惧"
    

# 饮食要求枚举
class DietaryRequirementsEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 普食
    ORDINARY = "ORDINARY", "普食"
    # 其他
    OTHER = "OTHER", "其他"

#  食欲枚举
class AppetiteEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = "NORMAL", "正常"
    # 不佳
    POOR = "POOR", "不佳"
    # 厌食
    ANTIAE = "ANTIAE", "厌食"
    
# 排尿枚举
class UrinationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = "NORMAL", "正常"
    # 尿频
    FREQUENT = "FREQUENT", "尿频"


# 排便枚举
class DefecationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = "NORMAL", "正常"
    # 便秘
    CONSTIPATION = "CONSTIPATION", "便秘"
    # 腹泻
    DIARRHEA = "DIARRHEA", "腹泻"
    

# 对康复知识熟悉程度枚举
class KnowledgeOfRehabilitationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 熟悉
    FAMILIAR = "FAMILIAR", "熟悉"
    # 了解
    KNOWLEDGE = "KNOWLEDGE", "了解"
    # 不了解
    UNKNOWLEDGE = "UNKNOWLEDGE", "不了解"
    
    
# 参加孕期教育情况枚举
class ParticipationInPregnancyEducationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 共同
    TOGETHER = "TOGETHER", "共同参与"
    # 本人
    PERSONAL = "PERSONAL", "本人"
    # 家属
    FAMILY = "FAMILY", "家属"
    
    
# 家属对产妇的态度枚举
class FamilyAttitudeToPatientEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 恰当的关怀
    APPROPRIATE_CARE = "APPROPRIATE_CARE", "恰当的关怀"
    # 不关心
    NOT_CARE = "NOT_CARE", "不关心"
    # 过于关心
    TOO_CARE = "TOO_CARE", "过于关心"
    # 其他
    OTHER = "OTHER", "其他"
    
    
# 面色枚举
class FaceColorEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 红润
    RED = "RED", "红润"
    # 苍白
    WHITE = "WHITE", "苍白"
    # 黄染
    YELLOW = "YELLOW", "黄染"
    

# 口腔黏膜枚举
class OralMucosaEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 完整
    INTACT = "INTACT", "完整"
    # 溃疡
    ULCER = "ULCER", "溃疡"
    

# 活动枚举
class ActivityEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = "NORMAL", "正常"
    # 受限
    LIMITED = "LIMITED", "受限"
    
    

# 切口位置枚举
class IncisionPositionEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 腹壁
    ABDOMINAL_WALL = "ABDOMINAL_WALL", "腹壁"
    # 会阴侧切
    PERINEAL_INCISION = "PERINEAL_INCISION", "会阴侧切"
    # 会阴正中
    PERINEAL_MIDDLE = "PERINEAL_MIDDLE", "会阴正中"
    
    
# 切口情况枚举
class IncisionSituationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = "NORMAL", "正常"
    # 异常
    ABNORMAL = "ABNORMAL", "异常"
    
# 切口异常枚举
class IncisionAbnormalEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 红
    RED = "RED", "红"
    # 肿
    SWOLLEN = "SWOLLEN", "肿"
    # 痛
    PAIN = "PAIN", "痛"
    # 渗出
    SECRETION = "SECRETION", "渗出"
    

# 乳房情况枚举
class BreastSituationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = "NORMAL", "正常"
    # 充盈
    FUL = "FUL", "充盈"
    # 肿胀
    SWOLLEN = "SWOLLEN", "肿胀"
    # 副乳
    SECONDARY_BREASTS = "SECONDARY_BREASTS", "副乳"

    
# 乳头情况枚举
class NippleSituationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = "NORMAL", "正常"
    # 异常
    ABNORMAL = "ABNORMAL", "异常"
    

# 乳头异常枚举
class NippleAbnormalEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 内陷
    INVAGINATED = "INVAGINATED", "内陷"
    # 皲裂
    CRACKED = "CRACKED", "皲裂"
    # 水泡
    BLISTER = "BLISTER", "水泡"
    # 畸形
    DEFORMITY = "DEFORMITY", "畸形"


# 乳汁情况枚举
class MilkSituationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 多
    MORE = "MORE", "多"
    # 中
    MIDDLE = "MIDDLE", "中"
    # 少
    LESS = "LESS", "少"
    # 无
    NONE = "NONE", "无"
    
    

# 日常护理切口情况
class RehabilitationIncisionSituationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = "NORMAL", "正常"
    # 红肿
    RED = "RED", "红肿"
    # 渗液
    SECRETION = "SECRETION", "渗液"
    
    
# 运动后评估枚举
class ExerciseAfterEvaluationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 较好
    GOOD = "GOOD", "较好"
    # 一般
    AVERAGE = "AVERAGE", "一般"
    # 较差
    POOR = "POOR", "较差"
    
    
# 进食情况枚举
class EatingSituationEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 吃完
    FINISHED = "FINISHED", "吃完"
    # 剩余少量
    REMAINING_LITTLE = "REMAINING_LITTLE", "剩余少量"
    # 剩余多量
    REMAINING_MUCH = "REMAINING_MUCH", "剩余多量"
    # 未吃
    NOT_EATEN = "NOT_EATEN", "未吃"
    
    
# 特殊饮食特点
class SpecialDietFeaturesEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 糖尿病饮食
    DIABETES_DIET = "DIABETES_DIET", "糖尿病饮食"
    # 低盐饮食
    LOW_SALT_DIET = "LOW_SALT_DIET", "低盐饮食"
    # 高蛋白饮食
    HIGH_PROTEIN_DIET = "HIGH_PROTEIN_DIET", "高蛋白饮食"