from django.db import models


class FeedingMethodChoice(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 母乳喂养
    BREAST_FEEDING = "BREAST_FEEDING", "母乳喂养"
    # 人工喂养
    ARTIFICIAL_FEEDING = "ARTIFICIAL_FEEDING", "人工喂养"
    # 混合喂养
    MIXED_FEEDING = "MIXED_FEEDING", "混合喂养"


class UrinationChoice(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = 'NORMAL', '正常'
    # 尿少
    LOW = 'LOW', '尿少'

class BowelMovementChoice(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 正常
    NORMAL = 'NORMAL', '正常'
    # 便秘
    CONSTIPATION = 'CONSTIPATION', '便秘'
    # 腹泻
    DIARRHEA = 'DIARRHEA', '腹泻'
    # 未排
    NO_MOVEMENT = 'NO_MOVEMENT', '未排'

class SkinStatusChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 红润
    NORMAL = 'NORMAL', '红润'
    # 异常
    ABNORMAL = 'ABNORMAL', '异常'

class SkinAbnormalityChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 黄染
    JAUNDICE = 'JAUNDICE', '黄染'
    # 完整
    INTACT = 'INTACT', '完整'
    # 皮损
    LESION = 'LESION', '皮损'
    # 红斑
    ERYTHEMA = 'ERYTHEMA', '红斑'
    # 皮疹
    RASH = 'RASH', '皮疹'
    # 脓包
    PUSTULE = 'PUSTULE', '脓包'
    # 硬肿
    INDURATION = 'INDURATION', '硬肿'
    # 水肿
    EDEMA = 'EDEMA', '水肿'
    # 其他
    OTHER = 'OTHER', '其他'


class ComplexionChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 红润
    RUDDY = 'RUDDY', '红润'
    # 黄染
    YELLOW = 'YELLOW', '黄染'

class CryChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 响
    LOUD = 'LOUD', '响'
    # 弱
    WEAK = 'WEAK', '弱'


class ReactionChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 好
    GOOD = 'GOOD', '良好'
    # 差
    POOR = 'POOR', '差'

    
    
# 四肢
class ExtremitiesChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 温暖
    WARM = 'WARM', '温暖'
    # 湿冷
    COLD = 'COLD', '湿冷'
    # 花斑纹
    STRIPES = 'STRIPES', '花斑纹'

# 四肢张力及活动
class ExtremityToneAndMovementChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 良好
    GOOD = 'GOOD', '良好'
    # 受限
    LIMITED = 'LIMITED', '受限'


# 产伤类型
class BirthInjuryTypeChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 皮损部位
    SKIN_LESIONS = 'SKIN_LESIONS', '皮损部位'
    # 头血肿
    HEAD_HUMOR = 'HEAD_HUMOR', '头血肿'
    
# 神经反射
class ReflexesChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 吸吮
    SUCKING = 'SUCKING', '吸吮'
    # 吞咽
    SWALLOWING = 'SWALLOWING', '吞咽'
    # 觅食
    ROOTING = 'ROOTING', '觅食'
    # 握持
    GRASPING = 'GRASPING', '握持'
    # 拥抱反射
    MORO = 'MORO', '拥抱反射'

# 营养发育
class NutritionsDevelopmentChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 正常
    NORMAL = 'NORMAL', '正常'
    # 过剩
    EXCESS = 'EXCESS', '过剩'
    # 滞后
    DELAYED = 'DELAYED', '滞后'
    
    
# 前囟
class AnteriorFontanelleChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 平坦
    FLAT = 'FLAT', '平坦'
    # 肿胀
    BULGING = 'BULGING', '肿胀'
    # 凹陷
    INTACT = 'INTACT', '凹陷'
    

# 口腔黏膜
class OralMucosaChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 完整
    INTACT = 'INTACT', '完整'
    # 异常
    ABNORMAL = 'ABNORMAL', '异常'

# 口腔黏膜异常
class OralMucosaAbnormalityChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 破溃
    BREAKDOWN = 'BREAKDOWN', '破溃'
    # 鹅口疮
    CANDIDA = 'CANDIDA', '鹅口疮'


# 畸形类型
class AnomalyTypeChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 唇腭裂
    LIP_PALATE = 'LIP_PALATE', '唇腭裂'
    # 鼻
    NOSE = 'NOSE', '鼻'
    # 耳
    EAR = 'EAR', '耳'
    # 四肢
    EXTREMITY = 'EXTREMITY', '四肢'
    # 外阴
    VAGINA = 'VAGINA', '外阴'
    # 尿道下裂
    URETHRA_HYPOSPADIAS = 'URETHRA_HYPOSPADIAS', '尿道下裂'
    # 肛门闭锁
    ANAL_ATRESIA = 'ANAL_ATRESIA', '肛门闭锁'
    # 其他
    OTHER = 'OTHER', '其他'
    
    
# 脐部
class UmbilicalCordChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 干燥
    DRY = 'DRY', '干燥'
    # 异常
    ABNORMAL = 'ABNORMAL', '异常'
    
    
# 脐部异常
class UmbilicalCordAbnormalityChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 红肿
    RED_AND_SWOLLEN = 'RED_AND_SWOLLEN', '红肿'
    # 渗液
    SEEPAGE = 'SEEPAGE', '渗液'
    # 渗血
    HEMORRHAGE = 'HEMORRHAGE', '渗血'


# 吸吮情况
class SuckingAbilitysChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 好
    GOOD = 'GOOD', '好'
    # 一般
    AVERAGE = 'AVERAGE', '一般'
    # 差
    POOR = 'POOR', '差'
    

# 臀部
class ButtocksChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 正常
    NORMAL = 'NORMAL', '正常'
    # 异常
    ABNORMAL = 'ABNORMAL', '异常'

# 臀部异常
class ButtocksAbnormalityChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 稍红
    SLIGHTLY_RED = 'SLIGHTLY_RED', '稍红'
    # 红臀
    RED_BUTTOCKS = 'RED_BUTTOCKS', '红臀'
    # 皮损
    LESION = 'LESION', '皮损'
    # 结痂
    SCAB = 'SCAB', '结痂'
    

# 疫苗接种
class VaccineInjectionChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 乙肝疫苗第一针
    HEPATITIS_B_VACCINE_FIRST_SHOT = 'HEPATITIS_B_VACCINE_FIRST_SHOT', '乙肝疫苗第一针'
    # 卡介苗
    BCG = 'BCG', '卡介苗'
    # 其他
    OTHER = 'OTHER', '其他'
    

# 部位
class LocationChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 前额
    FRONTAL = 'FRONTAL', '前额'
    # 前胸
    FRONT_CHEST = 'FRONT_CHEST', '前胸'
    # 大腿
    THIGH = 'THIGH', '大腿'
    # 小腿外侧
    LOWER_LEG_OUTSIDE = 'LOWER_LEG_OUTSIDE', '小腿外侧'
    
    

# 满月营养发育
class MonthNutritionsDevelopmentChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 良好
    GOOD = 'GOOD', '良好'
    # 过剩
    EXCESS = 'EXCESS', '过剩'
    # 中等
    AVERAGE = 'AVERAGE', '中等'
    # 差
    POOR = 'POOR', '差'
    
    
    
# 面色
class SkinColorChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 红润
    RED = 'RED', '红润'
    # 黄染
    YELLOW = 'YELLOW', '黄染'
    

# 哭声
class MonthCryChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 响亮  
    LOUD = 'LOUD', '响亮'
    # 微弱
    WEAK = 'WEAK', '微弱'


# 反应
class MonthReactionChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 好
    GOOD = 'GOOD', '良好'
    # 迟钝
    DULL = 'DULL', '迟钝'
    # 差
    POOR = 'POOR', '差'
    

# 皮肤
class MonthSkinStatusChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 完整
    INTACT = 'INTACT', '完整'
    # 破损
    DAMAGED = 'DAMAGED', '破损'
    # 红润
    RED = 'RED', '红润'
    # 黄染
    YELLOW = 'YELLOW', '黄染'
    # 红臀
    RED_BUTTOCKS = 'RED_BUTTOCKS', '红臀'
    # 皮疹
    RASH = 'RASH', '皮疹'
    
    
# 喂养情况
class MonthFeedingSituationChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 母乳喂养
    BREASTFEEDING = 'BREASTFEEDING', '母乳喂养'
    # 人工喂养
    ARTIFICIAL_FEEDING = 'ARTIFICIAL_FEEDING', '人工喂养'
    # 混合喂养
    MIXED_FEEDING = 'MIXED_FEEDING', '混合喂养'
    
    
# 喂养指导
class MonthFeedingGuidanceChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 母乳
    BREAST = 'BREAST', '母乳'
    # 奶粉
    FORMULA = 'FORMULA', '奶粉'
    # 混合喂养
    MIXED = 'MIXED', '混合喂养'
    # 按时添加辅食
    ADD_SUPPLEMENT = 'ADD_SUPPLEMENT', '按时添加辅食'
    # 少食多餐
    FEW_TIMES_MORE_FOOD = 'FEW_TIMES_MORE_FOOD', '少食多餐'
    # 其他
    OTHER = 'OTHER', '其他'
    
    

# 预防感冒
class MonthPreventColdChoice(models.TextChoices):
    # 未知
    UNKNOWN = 'UNKNOWN', '未知'
    # 注意保暖
    WARM = 'WARM', '注意保暖'
    # 减少探视
    REDUCE_VISIT = 'REDUCE_VISIT', '减少探视'
    # 日光浴
    SUNLIGHT_BATH = 'SUNLIGHT_BATH', '日光浴'
    # 少到公共场合
    FEW_PUBLIC_PLACES = 'FEW_PUBLIC_PLACES', '少到公共场合'
    # 如有不适随时到医院就诊
    IF_UNWELL_SEEK_MEDICAL_ATTENDANCE = 'IF_UNWELL_SEEK_MEDICAL_ATTENDANCE', '如有不适随时到医院就诊'
    
    
