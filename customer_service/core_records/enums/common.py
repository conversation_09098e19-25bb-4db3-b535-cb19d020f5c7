"""
通用枚举类型
"""
from django.db import models

class YesNoChoice(models.TextChoices):
    """是否选择"""
    YES = 'YES', '是'
    NO = 'NO', '否'

class GenderChoice(models.TextChoices):
    """性别选择"""
    MALE = 'MALE', '男'
    FEMALE = 'FEMALE', '女'

class ComplexionChoice(models.TextChoices):
    """面色选择"""
    RUDDY = 'RUDDY', '红润'
    PALE = 'PALE', '苍白'
    YELLOW = 'YELLOW', '黄染'
    NORMAL = 'NORMAL', '正常'

class UrinationStatusChoice(models.TextChoices):
    """排尿情况"""
    NORMAL = 'NORMAL', '正常'
    FREQUENT = 'FREQUENT', '尿频'
    PAINFUL = 'PAINFUL', '尿痛'

class BowelMovementChoice(models.TextChoices):
    """排便情况"""
    NORMAL = 'NORMAL', '正常'
    CONSTIPATION = 'CONSTIPATION', '便秘'
    DIARRHEA = 'DIARRHEA', '腹泻'

class WoundStatusChoice(models.TextChoices):
    """伤口情况"""
    NORMAL = 'NORMAL', '正常'
    REDNESS = 'REDNESS', '红肿'
    SWELLING = 'SWELLING', '肿胀'
    PAIN = 'PAIN', '疼痛'
    DISCHARGE = 'DISCHARGE', '渗液'
    BLEEDING = 'BLEEDING', '渗血' 