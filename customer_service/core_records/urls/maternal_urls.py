from django.urls import path

from ..views import maternal_views

# 产妇相关接口的URL配置
urlpatterns  = [
    
    # 产妇每日必填记录接口
    path('mdr-record/list/<str:aid>/', maternal_views.MaternityDailyRequiredRecordListView.as_view(), name='maternity_daily_required_record_list'),
    path('mdr-record/detail/<str:record_id>/', maternal_views.MaternityDailyRequiredRecordDetailView.as_view(), name='maternity_daily_required_record_detail'),
    path('mdr-record/create/<str:aid>/', maternal_views.MaternityDailyRequiredRecordCreateView.as_view(), name='maternity_daily_required_record_create'),
    path('mdr-record/update/<str:record_id>/', maternal_views.MaternityDailyRequiredRecordUpdateView.as_view(), name='maternity_daily_required_record_update'),
    path('mdr-record/delete/<str:record_id>/', maternal_views.MaternityDailyRequiredRecordDeleteView.as_view(), name='maternity_daily_required_record_delete'),

    # 产妇入住评估接口
    path('mcia-record/create/<str:aid>/', maternal_views.MaternityCheckInAssessmentCreateView.as_view(), name='maternity_check_in_assessment_create'),
    path('mcia-record/detail/<str:aid>/', maternal_views.MaternityCheckInAssessmentDetailView.as_view(), name='maternity_check_in_assessment_detail'), 
    path('mcia-record/update/<str:aid>/', maternal_views.MaternityCheckInAssessmentUpdateView.as_view(), name='maternity_check_in_assessment_update'),
    path('mcia-record/delete/<str:aid>/', maternal_views.MaternityCheckInAssessmentDeleteView.as_view(), name='maternity_check_in_assessment_delete'),

    # 产妇康复护理评估记录接口
    path('mra-record/list/<str:aid>/', maternal_views.MaternityRehabilitationAssessmentRecordListView.as_view(), name='maternity_rehabilitation_assessment_record_list'),
    path('mra-record/detail/<str:record_id>/', maternal_views.MaternityRehabilitationAssessmentRecordDetailView.as_view(), name='maternity_rehabilitation_assessment_record_detail'),
    path('mra-record/create/<str:aid>/', maternal_views.MaternityRehabilitationAssessmentRecordCreateView.as_view(), name='maternity_rehabilitation_assessment_record_create'),
    path('mra-record/update/<str:record_id>/', maternal_views.MaternityRehabilitationAssessmentRecordUpdateView.as_view(), name='maternity_rehabilitation_assessment_record_update'),
    path('mra-record/delete/<str:record_id>/', maternal_views.MaternityRehabilitationAssessmentRecordDeleteView.as_view(), name='maternity_rehabilitation_assessment_record_delete'),


    # 产妇每日生理护理记录接口
    path('mdpc-record/list/<str:aid>/', maternal_views.MaternityDailyPhysicalCareRecordListView.as_view(), name='maternity_daily_physical_care_record_list'),
    path('mdpc-record/detail/<str:record_id>/', maternal_views.MaternityDailyPhysicalCareRecordDetailView.as_view(), name='maternity_daily_physical_care_record_detail'),
    path('mdpc-record/create/<str:aid>/', maternal_views.MaternityDailyPhysicalCareRecordCreateView.as_view(), name='maternity_daily_physical_care_record_create'),
    path('mdpc-record/update/<str:record_id>/', maternal_views.MaternityDailyPhysicalCareRecordUpdateView.as_view(), name='maternity_daily_physical_care_record_update'),
    path('mdpc-record/delete/<str:record_id>/', maternal_views.MaternityDailyPhysicalCareRecordDeleteView.as_view(), name='maternity_daily_physical_care_record_delete'),

    # 产妇膳食记录表接口
    path('md-record/list/<str:aid>/', maternal_views.MaternityDailyDietRecordListView.as_view(), name='maternity_daily_diet_record_list'),
    path('md-record/detail/<str:record_id>/', maternal_views.MaternityDailyDietRecordDetailView.as_view(), name='maternity_daily_diet_record_detail'),
    path('md-record/create/<str:aid>/', maternal_views.MaternityDailyDietRecordCreateView.as_view(), name='maternity_daily_diet_record_create'),
    path('md-record/update/<str:record_id>/', maternal_views.MaternityDailyDietRecordUpdateView.as_view(), name='maternity_daily_diet_record_update'),
    path('md-record/delete/<str:record_id>/', maternal_views.MaternityDailyDietRecordDeleteView.as_view(), name='maternity_daily_diet_record_delete'),


]