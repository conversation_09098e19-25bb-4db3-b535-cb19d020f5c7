from django.urls import path

from ..views import maternity_admission_views

# 婴儿相关接口的URL配置
urlpatterns = [
    # 入院记录列表
    path('maternity-admission/list/', maternity_admission_views.MaternityAdmissionListView.as_view(), name='maternity_admission_list'),  # 获取入院记录列表
    # 入院记录创建
    path('maternity-admission/create/<str:uid>/', maternity_admission_views.MaternityAdmissionCreateView.as_view(), name='maternity_admission_create'),  # 创建入院记录
    # 入院记录详情
    path('maternity-admission/detail/<str:aid>/', maternity_admission_views.MaternityAdmissionDetailView.as_view(), name='maternity_admission_detail'),  # 获取入院记录详情
    # 入院记录更新
    path('maternity-admission/update/<str:aid>/', maternity_admission_views.MaternityAdmissionUpdateView.as_view(), name='maternity_admission_update'),  # 更新入院记录
    # 入院记录删除
    path('maternity-admission/delete/<str:aid>/', maternity_admission_views.MaternityAdmissionDeleteView.as_view(), name='maternity_admission_delete'),  # 删除入院记录
    # 入院记录续住
    path('maternity-admission/renew/<str:aid>/', maternity_admission_views.MaternityAdmissionRenewView.as_view(), name='maternity_admission_renew'),  # 续住入院记录
    # 入院记录办理入住
    path('maternity-admission/check-in/<str:aid>/', maternity_admission_views.MaternityAdmissionCheckInView.as_view(), name='maternity_admission_check_in'),  # 办理入住
    
    # 客户详情
    path('maternity-admission/customer/detail/<str:aid>/', maternity_admission_views.MaternityAdmissionCustomerDetailView.as_view(), name='maternity_admission_customer_detail'),  # 获取客户详情
    
    # 员工选择列表
    path('staff/select/list/', maternity_admission_views.StaffSelectListView.as_view(), name='staff-select-list'),    
    
    # 在住产妇选择列表
    path('maternity-admission/in-house/select/list/', maternity_admission_views.MaternityAdmissionInHouseSelectListView.as_view(), name='maternity_admission_in_house_select_list'),  # 获取在住产妇选择列表
    
    # 可办理入住产妇选择列表
    path('maternity/select/list/', maternity_admission_views.MaternitySelectListView.as_view(), name='maternity_admission_check_in_select_list'),  # 获取可办理入住产妇选择列表
    # 条件搜索 入院记录列表 - 在住
    # path('maternity-admission/list/in-house/', maternity_admission_views.MaternityAdmissionInHouseListView.as_view(), name='maternity_admission_in_house_list'),  # 获取在住入院记录列表

]