from django.urls import path

from customer_service.core_records.views import baby_views

# 婴儿相关接口的URL配置
urlpatterns = [
    # 新生儿基本信息接口
    path('newborn/list/', baby_views.NewbornListView.as_view(), name='newborn_list'),  # 获取所有新生儿列表
    path('newborn/create/<str:aid>/', baby_views.NewbornCreateView.as_view(), name='newborn_create'),  
    path('newborn/detail/<str:nid>/', baby_views.NewbornDetailView.as_view(), name='newborn_detail'),  
    path('newborn/update/<str:nid>/', baby_views.NewbornUpdateView.as_view(), name='newborn_update'), 
    path('newborn/delete/<str:nid>/', baby_views.NewbornDeleteView.as_view(), name='newborn_delete'), 
    
    # 新生儿每日必填接口
    path('ndr-record/list/<str:nid>/', baby_views.NewbornDailyRequiredRecordListBaseView.as_view(), name='newborn_daily_required_list'), 
    path('ndr-record/detail/<str:record_id>/', baby_views.NewbornDailyRequiredRecordDetailView.as_view(), name='newborn_daily_required_detail'),  
    path('ndr-record/create/<str:nid>/', baby_views.NewbornDailyRequiredRecordCreateView.as_view(), name='newborn_daily_required_create'), 
    path('ndr-record/update/<str:record_id>/', baby_views.NewbornDailyRequiredRecordUpdateView.as_view(), name='newborn_daily_required_update'),  
    path('ndr-record/delete/<str:record_id>/', baby_views.NewbornDailyRequiredRecordDeleteView.as_view(), name='newborn_daily_required_delete'),  

    # 新生儿入住评估接口】
    path('newborn-check-in-assessment/detail/<str:nid>/', baby_views.NewbornCheckInAssessmentDetailView.as_view(), name='newborn_check_in_assessment_detail'),  
    path('newborn-check-in-assessment/create/<str:nid>/', baby_views.NewbornCheckInAssessmentCreateView.as_view(), name='newborn_check_in_assessment_create'),  
    path('newborn-check-in-assessment/update/<str:nid>/', baby_views.NewbornCheckInAssessmentUpdateView.as_view(), name='newborn_check_in_assessment_update'), 
    path('newborn-check-in-assessment/delete/<str:nid>/', baby_views.NewbornCheckInAssessmentDeleteView.as_view(), name='newborn_check_in_assessment_delete'), 


    # 新生儿护理记录单（1）接口
    path('newborn-care-one/list/<str:nid>/', baby_views.NewbornCareOneRecordListView.as_view(), name='newborn_care_one_list'),  
    path('newborn-care-one/detail/<str:record_id>/', baby_views.NewbornCareOneRecordDetailView.as_view(), name='newborn_care_one_detail'), 
    path('newborn-care-one/create/<str:nid>/', baby_views.NewbornCareOneRecordCreateView.as_view(), name='newborn_care_one_create'),  
    path('newborn-care-one/update/<str:record_id>/', baby_views.NewbornCareOneRecordUpdateView.as_view(), name='newborn_care_one_update'),  
    path('newborn-care-one/delete/<str:record_id>/', baby_views.NewbornCareOneRecordDeleteView.as_view(), name='newborn_care_one_delete'),  


    # 新生儿护理记录单（2）接口
    path('newborn-care-two/list/<str:nid>/', baby_views.NewbornCareTwoRecordListView.as_view(), name='newborn_care_two_list'),  
    path('newborn-care-two/detail/<str:record_id>/', baby_views.NewbornCareTwoRecordDetailView.as_view(), name='newborn_care_two_detail'),  
    path('newborn-care-two/create/<str:nid>/', baby_views.NewbornCareTwoRecordCreateView.as_view(), name='newborn_care_two_create'),  
    path('newborn-care-two/update/<str:record_id>/', baby_views.NewbornCareTwoRecordUpdateView.as_view(), name='newborn_care_two_update'),  
    path('newborn-care-two/delete/<str:record_id>/', baby_views.NewbornCareTwoRecordDeleteView.as_view(), name='newborn_care_two_delete'), 
    

    # 新生儿喂养记录接口
    path('newborn-feeding/list/<str:nid>/', baby_views.NewbornFeedingRecordListView.as_view(), name='newborn_feeding_list'),  # 获取新生儿喂养记录列表
    path('newborn-feeding/detail/<str:record_id>/', baby_views.NewbornFeedingRecordDetailView.as_view(), name='newborn_feeding_detail'),  # 获取单个新生儿喂养记录详情
    path('newborn-feeding/create/<str:nid>/', baby_views.NewbornFeedingRecordCreateView.as_view(), name='newborn_feeding_create'),  # 创建新生儿喂养记录
    path('newborn-feeding/update/<str:record_id>/', baby_views.NewbornFeedingRecordUpdateView.as_view(), name='newborn_feeding_update'),  # 更新新生儿喂养记录
    path('newborn-feeding/delete/<str:record_id>/', baby_views.NewbornFeedingRecordDeleteView.as_view(), name='newborn_feeding_delete'),  # 删除新生儿喂养记录

    # 新生儿护理操作记录接口
    path('newborn-care-operation/list/<str:nid>/', baby_views.NewbornCareOperationRecordListView.as_view(), name='newborn_care_operation_list'),  # 获取新生儿护理操作记录列表
    path('newborn-care-operation/detail/<str:record_id>/', baby_views.NewbornCareOperationRecordDetailView.as_view(), name='newborn_care_operation_detail'),  # 获取单个新生儿护理操作记录详情
    path('newborn-care-operation/create/<str:nid>/', baby_views.NewbornCareOperationRecordCreateView.as_view(), name='newborn_care_operation_create'),  # 创建新生儿护理操作记录
    path('newborn-care-operation/update/<str:record_id>/', baby_views.NewbornCareOperationRecordUpdateView.as_view(), name='newborn_care_operation_update'),  # 更新新生儿护理操作记录
    path('newborn-care-operation/delete/<str:record_id>/', baby_views.NewbornCareOperationRecordDeleteView.as_view(), name='newborn_care_operation_delete'),  # 删除新生儿护理操作记录


    # 新生儿满月评估接口
    path('newborn-month-assessment/detail/<str:nid>/', baby_views.NewbornMonthAssessmentDetailView.as_view(), name='newborn_month_assessment_detail'),  # 获取单个新生儿满月评估详情
    path('newborn-month-assessment/create/<str:nid>/', baby_views.NewbornMonthAssessmentCreateView.as_view(), name='newborn_month_assessment_create'),  # 创建新生儿满月评估
    path('newborn-month-assessment/update/<str:nid>/', baby_views.NewbornMonthAssessmentUpdateView.as_view(), name='newborn_month_assessment_update'),  # 更新新生儿满月评估
    path('newborn-month-assessment/delete/<str:nid>/', baby_views.NewbornMonthAssessmentDeleteView.as_view(), name='newborn_month_assessment_delete'),  # 删除新生儿满月评估
    
]