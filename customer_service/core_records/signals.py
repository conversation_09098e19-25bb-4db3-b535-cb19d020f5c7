import logging
from datetime import datetime

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

from .models.baby import (
    NewbornCheckInAssessment,
    NewbornCareOneRecord,
    NewbornCareTwoRecord,
    NewbornFeedingRecord,
    NewbornCareOperationRecord,
    NewbornMonthAssessment,
    NewbornDailyRequiredRecord,
)
from .models.maternal import (
    MaternityDailyDietRecord,
    MaternityDailyPhysicalCareRecord,
    MaternityCheckInAssessment,
    MaternityRehabilitationAssessmentRecord,
    MaternityDailyRequiredRecord,
)
from .models.maternity_admission import MaternityRecentActivity

logger = logging.getLogger(__name__)


def _get_activity_info(sender, instance):
    """
    根据模型类型和实例获取活动类型描述和创建人信息
    返回: (activity_type, creator)
    """
    try:
        # 获取当前日期
        today = datetime.now().strftime('%Y-%m-%d')

        # 获取创建人信息
        creator = ""
        if hasattr(instance, 'creator') and instance.creator:
            if hasattr(instance.creator, 'name'):
                creator = instance.creator.name
            else:
                creator = str(instance.creator)

        # 根据模型类型生成活动描述
        model_name = sender.__name__

        # 新生儿相关记录
        if model_name == 'NewbornCheckInAssessment':
            activity_type = f"新生儿入院评估"
        elif model_name == 'NewbornCareOneRecord':
            activity_type = f"新生儿护理记录(一)"
        elif model_name == 'NewbornCareTwoRecord':
            activity_type = f"新生儿护理记录(二)"
        elif model_name == 'NewbornFeedingRecord':
            activity_type = f"新生儿喂养记录"
        elif model_name == 'NewbornCareOperationRecord':
            activity_type = f"新生儿护理操作记录"
        elif model_name == 'NewbornMonthAssessment':
            activity_type = f"新生儿满月评估"
        elif model_name == 'NewbornDailyRequiredRecord':
            activity_type = f"新生儿每日必填记录"

        # 产妇相关记录
        elif model_name == 'MaternityDietRecord':
            activity_type = f"产妇膳食记录"
        elif model_name == 'MaternityDailyPhysicalCareRecord':
            activity_type = f"产妇每日生理护理记录"
        elif model_name == 'MaternityDailyRequiredRecord':
            activity_type = f"产妇每日必填记录"
        elif model_name == 'MaternityCheckInAssessment':
            activity_type = f"产妇入院评估"
        elif model_name == 'MaternityRehabilitationAssessmentRecord':
            activity_type = f"产妇康复评估记录"
        else:
            activity_type = f"记录更新"

        return activity_type, creator

    except Exception as e:
        logger.error(f"生成活动信息失败: {e}")
        return None, None


@receiver(post_save, sender=NewbornCheckInAssessment)
@receiver(post_save, sender=NewbornCareOneRecord)
@receiver(post_save, sender=NewbornCareTwoRecord)
@receiver(post_save, sender=NewbornFeedingRecord)
@receiver(post_save, sender=NewbornCareOperationRecord)
@receiver(post_save, sender=NewbornMonthAssessment)
@receiver(post_save, sender=NewbornDailyRequiredRecord)
@receiver(post_save, sender=MaternityDailyDietRecord)
@receiver(post_save, sender=MaternityDailyPhysicalCareRecord)
@receiver(post_save, sender=MaternityDailyRequiredRecord)
@receiver(post_save, sender=MaternityCheckInAssessment)
@receiver(post_save, sender=MaternityRehabilitationAssessmentRecord)
def update_maternity_admission_on_record_save(sender, instance, created, **kwargs):

    try:
        maternity_admission = None

        # 新生儿相关记录
        if hasattr(instance, 'newborn') and instance.newborn:
            maternity_admission = instance.newborn.maternity_admission

        # 产妇相关记录
        elif hasattr(instance, 'maternity_admission') and instance.maternity_admission:
            maternity_admission = instance.maternity_admission

        # 更新时间
        if maternity_admission:
            maternity_admission.update_last_update_time()

            if created:
                activity_type, creator = _get_activity_info(sender, instance)

                if activity_type and creator:
                    MaternityRecentActivity.create_record(
                        maternity_center_id=maternity_admission.maternity_center.id,
                        maternity_admission_id=maternity_admission.id,
                        activity_type=activity_type,
                        creator=creator
                    )

    except Exception as e:
        logger.error(f"更新产妇入院记录时间失败: {e}")


@receiver(post_delete, sender=NewbornCheckInAssessment)
@receiver(post_delete, sender=NewbornCareOneRecord)
@receiver(post_delete, sender=NewbornCareTwoRecord)
@receiver(post_delete, sender=NewbornFeedingRecord)
@receiver(post_delete, sender=NewbornCareOperationRecord)
@receiver(post_delete, sender=NewbornMonthAssessment)
@receiver(post_delete, sender=NewbornDailyRequiredRecord)
@receiver(post_delete, sender=MaternityDailyDietRecord)
@receiver(post_delete, sender=MaternityDailyPhysicalCareRecord)
@receiver(post_delete, sender=MaternityDailyRequiredRecord)
@receiver(post_delete, sender=MaternityCheckInAssessment)
@receiver(post_delete, sender=MaternityRehabilitationAssessmentRecord)
def update_maternity_admission_on_record_delete(sender, instance, **kwargs):
    try:
        maternity_admission = None
        
        # 新生儿相关记录
        if hasattr(instance, 'newborn') and instance.newborn:
            maternity_admission = instance.newborn.maternity_admission
        
        # 产妇相关记录
        elif hasattr(instance, 'maternity_admission') and instance.maternity_admission:
            maternity_admission = instance.maternity_admission
        
        # 更新时间
        if maternity_admission:
            maternity_admission.update_last_update_time()

    except Exception as e:
        logger.error(f"删除记录后更新产妇入院记录时间失败: {e}")

