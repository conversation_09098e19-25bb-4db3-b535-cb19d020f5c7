from django.contrib import admin
from django.utils.html import format_html

from core.enum import CheckInStatusEnum
from customer_service.core_records.models.baby import NewbornDailyRequiredRecord
from customer_service.core_records.models.maternal import MaternityDailyRequiredRecord
from .models import (
    MaternityAdmission
)


# 入院记录
@admin.register(MaternityAdmission)
class MaternityAdmissionAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'maternity_name', 'maternity_phone', 'room_info',
        'check_in_status_display', 'expected_check_in_date',
        'actual_check_in_date', 'residence_days', 'need_attention_display',
        'chief_nurse_name', 'created_at'
    )

    list_filter = (
        'check_in_status', 'need_attention', 'delivery_method',
        'check_in_source', 'maternity_center', 'created_at',
        'expected_check_in_date', 'actual_check_in_date'
    )

    search_fields = (
        'maternity__name', 'maternity__phone', 'maternity__identity_number',
        'room__room_number', 'contract_number', 'chief_nurse__name'
    )

    readonly_fields = (
        'id', 'expected_check_out_date', 'actual_check_out_date',
        'last_update_time', 'created_at', 'updated_at'
    )

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'maternity_center', 'maternity', 'need_attention')
        }),
        ('分娩信息', {
            'fields': (
                'expected_delivery_date', 'actual_delivery_date',
                'delivery_hospital', 'delivery_method', 'pregnancy_week',
                'allergy_history'
            )
        }),
        ('入住信息', {
            'fields': (
                'expected_check_in_date', 'actual_check_in_date',
                'expected_check_out_date', 'actual_check_out_date',
                'room', 'check_in_status', 'residence_days',
                'check_in_source', 'chief_nurse'
            )
        }),
        ('合同信息', {
            'fields': (
                'contract_number', 'contract_file', 'contract_remark'
            ),
            'classes': ('collapse',)
        }),
        ('出院记录', {
            'fields': (
                'discharge_records_file', 'discharge_records_remark'
            ),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('last_update_time', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def maternity_name(self, obj):
        if obj.maternity:
            return obj.maternity.name
        return '-'
    maternity_name.short_description = '产妇姓名'

    def maternity_phone(self, obj):
        if obj.maternity:
            return obj.maternity.phone
        return '-'
    maternity_phone.short_description = '联系电话'

    def room_info(self, obj):
        if obj.room:
            return f"{obj.room.room_number}"
        return '-'
    room_info.short_description = '房间号'

    def check_in_status_display(self, obj):
        status_colors = {
            CheckInStatusEnum.RESERVED: '#ffc107',  
            CheckInStatusEnum.CHECKED_IN: '#28a745',  
            CheckInStatusEnum.CHECKED_OUT: '#6c757d',  
            CheckInStatusEnum.CANCELLED: '#dc3545',  
            CheckInStatusEnum.INVALID: '#dc3545',  
        }
        color = status_colors.get(obj.check_in_status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            CheckInStatusEnum(obj.check_in_status).label
        )
    check_in_status_display.short_description = '入住状态'

    def need_attention_display(self, obj):
        if obj.need_attention:
            return format_html(
                '<span style="color: #dc3545; font-weight: bold;">⚠️ 需关注</span>'
            )
        return format_html('<span style="color: #28a745;">正常</span>')
    need_attention_display.short_description = '关注状态'

    def chief_nurse_name(self, obj):
        if obj.chief_nurse:
            return obj.chief_nurse.name
        return '-'
    chief_nurse_name.short_description = '主任护理'

    # 优化查询性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'maternity', 'room', 'chief_nurse', 'maternity_center'
        )

    list_per_page = 20
    ordering = ('-created_at',)
    
    
    

# 新生儿每日必填记录
@admin.register(NewbornDailyRequiredRecord)
class NewbornDailyRequiredRecordAdmin(admin.ModelAdmin):
    list_display = ('id', 'newborn_name', 'record_date', 'jaundice', 'weight', 'temperature', 'creator_name', 'created_at')
    list_filter = ('record_date', 'creator', 'created_at', 'newborn__maternity_admission__maternity_center')
    search_fields = ('newborn__name', 'creator__name', 'newborn__maternity_admission__maternity__name')
    readonly_fields = ('id', 'created_at', 'updated_at')
    date_hierarchy = 'record_date'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'newborn', 'record_date', 'creator')
        }),
        ('生理指标', {
            'fields': ('jaundice', 'weight', 'temperature'),
            'description': '新生儿每日必填的生理指标数据'
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def newborn_name(self, obj):
        return obj.newborn.name if obj.newborn else '-'
    newborn_name.short_description = '新生儿姓名'
    newborn_name.admin_order_field = 'newborn__name'
    
    def creator_name(self, obj):
        return obj.creator.name if obj.creator else '-'
    creator_name.short_description = '创建人'
    creator_name.admin_order_field = 'creator__name'
    
    # 优化查询性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'newborn', 'creator', 'newborn__maternity_admission__maternity_center'
        )
    
    # 按月子中心过滤数据
    def get_list_filter(self, request):
        filters = list(self.list_filter)
        if not request.user.is_superuser:
            # 如果不是超级用户，可以根据用户权限过滤
            pass
        return filters
    
    list_per_page = 25
    ordering = ('-record_date', '-created_at')


# 产妇每日必填记录
@admin.register(MaternityDailyRequiredRecord)
class MaternityDailyRequiredRecordAdmin(admin.ModelAdmin):
    list_display = ('id', 'maternity_name', 'record_date', 'blood_pressure', 'weight', 'temperature', 'creator_name', 'created_at')
    list_filter = ('record_date', 'creator', 'created_at', 'maternity_admission__maternity_center')
    search_fields = ('maternity_admission__maternity__name', 'creator__name', 'maternity_admission__maternity__phone')
    readonly_fields = ('id', 'created_at', 'updated_at')
    date_hierarchy = 'record_date'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'maternity_admission', 'record_date', 'creator')
        }),
        ('生理指标', {
            'fields': ('blood_pressure', 'weight', 'temperature'),
            'description': '产妇每日必填的生理指标数据'
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def maternity_name(self, obj):
        if obj.maternity_admission and obj.maternity_admission.maternity:
            return obj.maternity_admission.maternity.name
        return '-'
    maternity_name.short_description = '产妇姓名'
    maternity_name.admin_order_field = 'maternity_admission__maternity__name'
    
    def creator_name(self, obj):
        return obj.creator.name if obj.creator else '-'
    creator_name.short_description = '创建人'
    creator_name.admin_order_field = 'creator__name'
    
    # 优化查询性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'maternity_admission__maternity', 
            'maternity_admission__maternity_center',
            'creator'
        )
    
    # 自定义表单验证
    def clean(self):
        from django.core.exceptions import ValidationError
        cleaned_data = super().clean()
        
        # 可以添加自定义验证逻辑
        weight = cleaned_data.get('weight')
        temperature = cleaned_data.get('temperature')
        
        if weight and (weight < 30 or weight > 200):
            raise ValidationError('体重数值异常，请检查输入')
            
        if temperature and (temperature < 35 or temperature > 42):
            raise ValidationError('体温数值异常，请检查输入')
            
        return cleaned_data
    
    list_per_page = 25
    ordering = ('-record_date', '-created_at')
    
    # 添加自定义操作
    actions = ['export_to_excel']
    
    def export_to_excel(self, request, queryset):
        # 这里可以实现导出Excel的功能
        self.message_user(request, f"已选择 {queryset.count()} 条记录进行导出")
    export_to_excel.short_description = "导出选中记录到Excel"