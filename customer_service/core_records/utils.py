from datetime import timedelta

from django.utils import timezone

from core.enum import CheckInStatusEnum


def format_relative_time(datetime_obj):

   # 将datetime对象转换为人性化的相对时间描述

    if not datetime_obj:
        return ""
        
    now = timezone.now()
    diff = now - datetime_obj
    
    # 转换为本地时间便于比较日期
    local_now = timezone.localtime(now)
    local_dt = timezone.localtime(datetime_obj)
    
    # 判断是否是今天
    if local_now.date() == local_dt.date():
        # 如果在过去的一小时内
        if diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            if minutes < 1:
                return "刚刚"
            return f"{minutes}分钟前"
        else:
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}小时前"
    
    # 判断是否是昨天
    elif local_now.date() - local_dt.date() == timedelta(days=1):
        return "昨日"
    
    # 判断是否是前天
    elif local_now.date() - local_dt.date() == timedelta(days=2):
        return "前日"
    
    # 一周内
    elif diff < timedelta(days=7):
        days = int(diff.total_seconds() / 86400)
        return f"{days}天前"
    
    # 一个月内
    elif diff < timedelta(days=30):
        days = int(diff.total_seconds() / 86400)
        return f"{days}天前"
    
    # 更早的时间
    else:
        return local_dt.strftime("%m-%d")  # 月-日格式 
    
# 计算预计退房日期
def calculate_expected_checkout_date(data):
    # 计算预计退房日期
    residence_days = data.get('residence_days')
    check_in_status = data.get('check_in_status')
    expected_check_in_date = data.get('expected_check_in_date')
    actual_check_in_date = data.get('actual_check_in_date')
    
    # 如果没有设置入住天数，不进行计算
    if not residence_days:
        return None
        
    # 根据入住状态选择基准日期
    if check_in_status == CheckInStatusEnum.CHECKED_IN and actual_check_in_date:
        # 已入住状态：实际入住日期 + 入住天数
        base_date = actual_check_in_date
    elif expected_check_in_date:
        # 预约状态：预计入住日期 + 入住天数
        base_date = expected_check_in_date
    else:
        return None
        
    # 计算预期退房日期
    return base_date + timedelta(days=residence_days) 