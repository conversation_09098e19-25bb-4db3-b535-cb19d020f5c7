import json

from django.conf import settings
from rest_framework import serializers

from core.build_record import build_babies_daily_records_data, build_maternity_daily_records_data
from core.enum import CheckInStatusEnum, DeliveryMethodEnum, GenderEnum
from core.parse_time import ShanghaiFriendlyDateTimeField
from core.utils import calculate_age
from customer_service.core_records.models.baby import Newborn, NewbornDailyRequiredRecord
from customer_service.core_records.models.maternal import MaternityDailyRequiredRecord
from customer_service.core_records.models.maternity_admission import MaternityAdmission, MaternityAdmissionRenew, \
    MaternityRecentActivity
from customer_service.core_records.serializers.fields import RelativeDateTimeField
from customer_service.room.models import Room
from customer_service.room.serializers import RoomSerializer
from file.models import MaternityContractFile, MaternityDischargeRecordsFile
from organizational_management.charge.enum import PaymentStatusEnum
from organizational_management.charge.models import MaternityCostInfo
from organizational_management.charge.serializers import PackageSerializer
from user.models import Maternity, Staff
from user.serializers import MaternityDesensitizedSerializer


class NewbornSerializer(serializers.ModelSerializer):
    
    birth_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = Newborn
        fields = [
            'nid','name', 'gender', 'birth_time', 'birth_weight', 
            'birth_length', 'allergy_history','birth_week','hand_card_number'
        ]


class MaternitySerializer(serializers.ModelSerializer):
    
    age = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = Maternity
        fields = [
            'name', 'age','phone', 'birth_date', 'ethnicity', 'native_place', 
            'gender', 'blood_type', 'home_address', 'identity_number',
            'emergency_contact', 'emergency_contact_phone', 'emergency_contact_relation'
        ]

    def get_age(self, obj):
        return calculate_age(obj.birth_date) if obj.birth_date else "-"


class MaternityCostInfoSerializer(serializers.ModelSerializer):
    
    package_details = serializers.SerializerMethodField(read_only=True)
    payment_status = serializers.SerializerMethodField(read_only=True)
    package = serializers.SerializerMethodField(read_only=True)
    class Meta:
        model = MaternityCostInfo
        fields = [
            'bid','package', 'package_details', 'deposit_amount', 'earnest_amount',
            'payable_amount', 'paid_amount', 'remaining_amount',
            'payment_method', 'remark', 'payment_status','package_price'
        ]

    def get_package(self, obj):
        if obj.package:
            return obj.package.rid
        return None
    
    def get_package_details(self, obj):
        if obj.package:
            return PackageSerializer(obj.package).data
        return None

    def get_payment_status(self, obj):
        return PaymentStatusEnum(obj.payment_status).label

    def _calculate_payment_status(self, remaining_amount, payable_amount):
        # 计算支付状态的辅助方法
        remaining = float(remaining_amount or 0)
        payable = float(payable_amount or 0)

        if remaining == 0:
            return PaymentStatusEnum.FULL_PAID
        elif remaining < payable:
            return PaymentStatusEnum.PARTIAL_PAID
        else:
            return PaymentStatusEnum.UNPAID

    def validate(self, data):
        remaining_amount = data.get('remaining_amount', 0)
        payable_amount = data.get('payable_amount', 0)

        # 使用辅助方法计算支付状态
        data['payment_status'] = self._calculate_payment_status(remaining_amount, payable_amount)

        return data


class DetailedMaternityAdmissionSerializer(serializers.ModelSerializer):
    
    baby_list = serializers.SerializerMethodField()
    maternity = MaternityDesensitizedSerializer(read_only=True)
    cost_info = serializers.SerializerMethodField()
    room = RoomSerializer(read_only=True)
    chief_nurse_name = serializers.SerializerMethodField(read_only=True)
    chief_nurse = serializers.SerializerMethodField(read_only=True)
    relative_last_update = RelativeDateTimeField(source='last_update_time', read_only=True)
    age = serializers.SerializerMethodField(read_only=True)
    created_at = ShanghaiFriendlyDateTimeField(required=False)
    updated_at = ShanghaiFriendlyDateTimeField(required=False)
    contract_file_urls = serializers.SerializerMethodField(read_only=True)
    discharge_records_file_urls = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = MaternityAdmission
        fields = [
            'aid', 'maternity', 'age', 'expected_delivery_date', 'actual_delivery_date',
            'delivery_hospital', 'delivery_method', 'pregnancy_week',
            'allergy_history', 'expected_check_in_date', 'actual_check_in_date',
            'room', 'contract_number', 'contract_file', 'contract_file_urls', 'contract_remark',
            'check_in_source', 'discharge_records_file','discharge_records_file_urls', 'discharge_records_remark',
            'check_in_status', 'baby_list', 'cost_info',  'need_attention', 'chief_nurse','chief_nurse_name', 'relative_last_update',
            'residence_days','expected_check_out_date', 'actual_check_out_date', 'created_at', 'updated_at',
        ]
        
    def get_age(self, obj):
        return calculate_age(obj.maternity.birth_date) if obj.maternity.birth_date else "-"
    
    def get_baby_list(self, obj):
        babies = obj.newborns_by_admission.all()
        print(f"babies: {babies}")
        if not babies.exists():
            babies = Newborn.objects.filter(maternity_admission=obj)
        return NewbornSerializer(babies, many=True).data
    
    def get_chief_nurse_name(self, obj):
        if obj.chief_nurse:
            return obj.chief_nurse.name
        return None
    
    def get_chief_nurse(self, obj):
        if obj.chief_nurse:
            return obj.chief_nurse.sid
        return None
    
    def get_cost_info(self, obj):
        cost_info = MaternityCostInfo.objects.filter(maternity_admission=obj).first()
        if cost_info:
            return MaternityCostInfoSerializer(cost_info).data
        return None

    def get_package(self, obj):
        if obj.maternity_cost_info.package:
            return obj.maternity_cost_info.package.rid
        return None
    
    def get_contract_file_urls(self, obj):
        if obj.contract_file:
            
            urls = []
            
            for rid in obj.contract_file:
                file_obj = MaternityContractFile.get_by_rid(rid,self.context['request'].user.maternity_center)
                
                if file_obj:
                    urls.append(file_obj.file.url)
                else:
                    urls.append(settings.FILE_NOT_FOUND_URL)
            
            return urls
            
        return []
    
    def get_discharge_records_file_urls(self, obj):
        if obj.discharge_records_file:
            
            urls = []
            
            for rid in obj.discharge_records_file:
                file_obj = MaternityDischargeRecordsFile.get_by_rid(rid,self.context['request'].user.maternity_center)
                
                if file_obj:
                    urls.append(file_obj.file.url)
                else:
                    urls.append(settings.FILE_NOT_FOUND_URL)
            
            
            return urls
            
        return []
    


# 入院记录创建序列化器
class MaternityAdmissionCreateSerializer(serializers.ModelSerializer):
    
    chief_nurse = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    room = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    
    class Meta:
        model = MaternityAdmission
        fields = [
            'maternity', 'expected_delivery_date', 'actual_delivery_date',
            'delivery_hospital', 'delivery_method', 'pregnancy_week',
            'allergy_history', 'expected_check_in_date', 'actual_check_in_date',
            'room', 'contract_number', 'contract_file', 'contract_remark',
            'check_in_source', 'discharge_records_file', 'discharge_records_remark',
            'check_in_status', 'maternity_center', 'need_attention', 'chief_nurse',
            'residence_days'
        ]
    
    def validate_check_in_status(self, value):
        
        eci = self.initial_data.get('expected_check_in_date', None)
        aci = self.initial_data.get('actual_check_in_date', None)
        
        if value == CheckInStatusEnum.RESERVED and aci:
                raise serializers.ValidationError("预定状态下不允许选择实际入住日期")
        if value == CheckInStatusEnum.CHECKED_IN and not all([eci, aci]):
            raise serializers.ValidationError("入住状态下必须选择预计入住日期与实际入住日期")
        return value
        
        
    def validate_contract_file(self, value):
        
        if not isinstance(value, list):
            raise serializers.ValidationError("合同文件必须是列表格式")
        
        if value:
            rids = value
            
            existing_files = MaternityContractFile.objects.filter(
                rid__in=rids,
                maternity_center=self.context['request'].user.maternity_center
            )
            
            existing_rids = set(existing_files.values_list('rid', flat=True))
            
            input_rids = set(rids)
            missing_rids = input_rids - existing_rids
            
            if missing_rids:
                raise serializers.ValidationError(f"有不存在的合同文件，请删除后重新上传")
            
            return value
        return value
        
    def validate_discharge_records_file(self, value):
        
        if not isinstance(value, list):
            raise serializers.ValidationError("出院记录文件必须是列表格式")
        
        if value:
            rids = value
            
            existing_files = MaternityDischargeRecordsFile.objects.filter(
                rid__in=rids,
                maternity_center=self.context['request'].user.maternity_center
            )
            
            existing_rids = set(existing_files.values_list('rid', flat=True))
            
            input_rids = set(rids)
            missing_rids = input_rids - existing_rids
            
            if missing_rids:
                raise serializers.ValidationError(f"有不存在的出院记录文件，请删除后重新上传")
            
            return value
        return value
    
    def validate_residence_days(self, value):
        if value <= 0:
            raise serializers.ValidationError("入住天数必须大于0")
        return value
    
    def validate_chief_nurse(self, value):
        maternity_center = self.context.get('request').user.maternity_center
        if value:
            chief_nurse = Staff.get_staff_by_sid(value, maternity_center)
            if not chief_nurse:
                raise serializers.ValidationError("主责护士不存在")            
            return chief_nurse
        return value
    
    def validate_room(self, value):
        maternity_center = self.context.get('request').user.maternity_center
        if value:
            room = Room.get_room_by_rid(value, maternity_center)
            if not room:
                raise serializers.ValidationError("房间不存在")        
            return room
        return value


# 入院记录更新序列化器
class MaternityAdmissionUpdateSerializer(serializers.ModelSerializer):
    
    chief_nurse = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    room = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    
    class Meta:
        model = MaternityAdmission
        fields = [
            'expected_delivery_date', 'actual_delivery_date',
            'delivery_hospital', 'delivery_method', 'pregnancy_week',
            'allergy_history', 'expected_check_in_date', 'actual_check_in_date',
            'room', 'contract_number', 'contract_file', 'contract_remark',
            'check_in_source', 'discharge_records_file', 'discharge_records_remark',
            'check_in_status', 'need_attention', 'chief_nurse',
            'residence_days','expected_check_out_date', 
        ]
        
        
            
    def validate_contract_file(self, value):
        
        if not isinstance(value, list):
            raise serializers.ValidationError("合同文件必须是列表格式")
        
        if value:
            rids = value
            
            existing_files = MaternityContractFile.objects.filter(
                rid__in=rids,
                maternity_center=self.context['request'].user.maternity_center
            )
            
            existing_rids = set(existing_files.values_list('rid', flat=True))
            
            input_rids = set(rids)
            missing_rids = input_rids - existing_rids
            
            if missing_rids:
                raise serializers.ValidationError(f"有不存在的合同文件，请删除后重新上传")
            
            return value
        return value
        
    def validate_discharge_records_file(self, value):
        print(f"开始验证出院记录文件: {value}")
        
        if not isinstance(value, list):
            raise serializers.ValidationError("出院记录文件必须是列表格式")
        
        if value:
            rids = value
            
            existing_files = MaternityDischargeRecordsFile.objects.filter(
                rid__in=rids,
                maternity_center=self.context['request'].user.maternity_center
            )
            
            existing_rids = set(existing_files.values_list('rid', flat=True))
            
            input_rids = set(rids)
            missing_rids = input_rids - existing_rids
            
            if missing_rids:
                raise serializers.ValidationError(f"有不存在的出院记录文件，请删除后重新上传")
            
            return value
        return value
    
    def validate_residence_days(self, value):
        if value <= 0:
            raise serializers.ValidationError("入住天数必须大于0")
        return value
    
    def validate_chief_nurse(self, value):
        maternity_center = self.context.get('request').user.maternity_center
        if value:
            chief_nurse = Staff.get_staff_by_sid(value, maternity_center)
            if not chief_nurse:
                raise serializers.ValidationError("主责护士不存在")            
            return chief_nurse
        return value
    
    def validate_room(self, value):
        maternity_center = self.context.get('request').user.maternity_center
        if value:
            room = Room.get_room_by_rid(value, maternity_center)
            if not room:
                raise serializers.ValidationError("房间不存在")        
            return room
        return value
    
    def update(self, instance, validated_data):
        
        actual_check_in = validated_data.get('actual_check_in_date', instance.actual_check_in_date)
        expected_check_in = validated_data.get('expected_check_in_date', instance.expected_check_in_date)
        residence_days = validated_data.get('residence_days', instance.residence_days)
        
        check_in_date = actual_check_in or expected_check_in
        
        # 如果有入住日期和住院天数，计算预期出院日期
        if check_in_date and residence_days:
            from datetime import timedelta
            validated_data['expected_check_out_date'] = check_in_date + timedelta(days=residence_days)
        
        return super().update(instance, validated_data)

# 客户详情序列化器
class MaternityAdmissionCustomerDetailSerializer(serializers.ModelSerializer):
    
    # 产妇名字
    maternity_name = serializers.SerializerMethodField(read_only=True)
    # 入住状态
    check_in_status_display = serializers.SerializerMethodField(read_only=True)
    # 产妇年龄
    maternity_age = serializers.SerializerMethodField(read_only=True)
    # 产妇房间号
    room_number = serializers.SerializerMethodField(read_only=True)
    # 入住日期
    check_in_date = serializers.SerializerMethodField(read_only=True)
    # 退房日期
    check_out_date = serializers.SerializerMethodField(read_only=True)
    # 主责护士
    chief_nurse_name = serializers.SerializerMethodField(read_only=True)
    # 分娩方式
    delivery_method = serializers.SerializerMethodField(read_only=True)
    # 是否是多胞胎
    is_multiple_birth = serializers.SerializerMethodField(read_only=True)
    # 婴儿数量
    baby_count = serializers.SerializerMethodField(read_only=True)
    # 最近活动
    recent_activity = serializers.SerializerMethodField(read_only=True)
    # 孩子数据
    babies_data = serializers.SerializerMethodField(read_only=True)
    # 产妇数据
    maternity_data = serializers.SerializerMethodField(read_only=True)
    # 入院编号
    aid = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = MaternityAdmission
        fields = [
            'aid','maternity_name', 'check_in_status','check_in_status_display','maternity_age', 'room_number', 
            'check_in_date', 'check_out_date', 'chief_nurse_name', 'is_multiple_birth',
            'delivery_method','baby_count','recent_activity','babies_data','maternity_data'
        ]
        
    def get_aid(self, obj):
        return obj.aid
    
    def get_maternity_name(self, obj):
        return obj.maternity.name if obj.maternity else "-"
    
    def get_check_in_status_display(self, obj):
        return CheckInStatusEnum(obj.check_in_status).label
    
    def get_maternity_age(self, obj):
        if obj.maternity and obj.maternity.birth_date:
            return calculate_age(obj.maternity.birth_date)
        else:
            return "-"
    
    def get_room_number(self, obj):
        return obj.room.room_number if obj.room else "-"
    
    def get_check_in_date(self, obj):
        if obj.actual_check_in_date:
            return obj.actual_check_in_date.strftime('%Y-%m-%d')
        elif obj.expected_check_in_date:
            return f"{obj.expected_check_in_date.strftime('%Y-%m-%d')}（预计）"
        else:
            return "N/A"
    
    def get_check_out_date(self, obj):
        if obj.actual_check_out_date:
            return obj.actual_check_out_date.strftime('%Y-%m-%d')
        elif obj.expected_check_out_date:
            return f"{obj.expected_check_out_date.strftime('%Y-%m-%d')}（预计）"
        else:
            return "N/A"

    def get_chief_nurse_name(self, obj):
        return obj.chief_nurse.name if obj.chief_nurse else "-"
    
    def get_is_multiple_birth(self, obj):
        # 使用预加载的数据，避免额外查询
        return len(obj.newborns_by_admission.all()) > 1

    def get_delivery_method(self, obj):
        return DeliveryMethodEnum(obj.delivery_method).label if obj.delivery_method else "-"

    def get_baby_count(self, obj):
        return len(obj.newborns_by_admission.all())

    def get_recent_activity(self, obj):
        recent_activities = obj.maternity_recent_activity.all()[:5]
        return MaternityRecentActivitySerializer(recent_activities, many=True).data

    def get_babies_data(self, obj):
        all_babies = Newborn.objects.filter(maternity_admission_id=obj.id)

        all_baby_daily_records = NewbornDailyRequiredRecord.objects.filter(
            newborn__maternity_admission_id=obj.id
        ).select_related('newborn').order_by('-record_date')

        return build_babies_daily_records_data(all_baby_daily_records, all_babies)
    
    def get_maternity_data(self, obj):
        all_maternity_daily_records = MaternityDailyRequiredRecord.objects.filter(
            maternity_admission_id=obj.id
        ).only('id', 'record_date', 'weight', 'temperature', 'blood_pressure').order_by('-record_date')

        return build_maternity_daily_records_data(all_maternity_daily_records)
    
# 在住产妇选择列表
class MaternityAdmissionInHouseSelectListSerializer(serializers.ModelSerializer):
    
    maternity_name = serializers.SerializerMethodField(read_only=True)
    maternity_phone = serializers.SerializerMethodField(read_only=True)
    room_number = serializers.SerializerMethodField(read_only=True)
    check_in_status = serializers.SerializerMethodField(read_only=True)
    gender_display = serializers.SerializerMethodField(read_only=True)
    age = serializers.SerializerMethodField(read_only=True)
    birth_date = serializers.SerializerMethodField(read_only=True)
    
    
    class Meta:
        model = MaternityAdmission
        fields = ['aid', 'age','maternity_name', 'maternity_phone', 'room_number','check_in_status','gender_display','birth_date']
        
    def get_maternity_name(self, obj):
        return obj.maternity.name if obj.maternity else "-"
    
    def get_maternity_phone(self, obj):
        return obj.maternity.phone if obj.maternity else "-"
    
    def get_room_number(self, obj):
        return obj.room.room_number if obj.room else "-"
    
    def get_check_in_status(self, obj):
        return CheckInStatusEnum(obj.check_in_status).label

    def get_gender_display(self, obj):
        return GenderEnum(obj.maternity.gender).label if obj.maternity.gender else "-"
    
    def get_age(self, obj):
        return calculate_age(obj.maternity.birth_date)

    def get_birth_date(self, obj):
        return obj.maternity.birth_date if obj.maternity else "-"

# 续住入院记录序列化器
class MaternityAdmissionRenewSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaternityAdmissionRenew
        fields = ['rid',  'renew_days', 'renew_reason', 'original_expected_check_out_date', 'new_expected_check_out_date']
        

# 最近活动序列化器
class MaternityRecentActivitySerializer(serializers.ModelSerializer):
    
    activity_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = MaternityRecentActivity
        fields = ['record_id', 'activity_type', 'activity_time', 'creator']
        
        