from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.core_records.enums.maternal import ActivityEnum, DietaryRequirementsEnum, ExerciseAfterEvaluationEnum, \
    FamilyAttitudeToPatientEnum
from customer_service.core_records.models.maternal import MaternityDailyPhysicalCareRecord, \
    MaternityDailyRequiredRecord, MaternityDailyDietRecord, MaternityCheckInAssessment, \
    MaternityRehabilitationAssessmentRecord


# 产妇每日必填记录列表序列化器
class MaternityDailyRequiredRecordListSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人名字
    creator_name = serializers.SerializerMethodField(read_only=True)
    # record_date
    record_date = ShanghaiFriendlyDateTimeField(read_only=True)

    class Meta:
        model = MaternityDailyRequiredRecord
        fields = [
            'record_id',
            'record_date',
            'creator_name',
            'created_at',
            'updated_at',
        ]

    def get_creator_name(self, obj):
        return obj.creator.name if obj.creator else ""


# 产妇每日必填记录详情序列化器
class MaternityDailyRequiredRecordDetailSerializer(serializers.ModelSerializer):

    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人名字
    creator_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = MaternityDailyRequiredRecord
        exclude = [
            'id',
            'maternity_admission',
            'creator',
        ]

    def get_creator_name(self, obj):
        return obj.creator.name


# 创建产妇每日必填记录序列化器
class MaternityDailyRequiredRecordCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = MaternityDailyRequiredRecord
        exclude = [
            'record_id',
            'created_at',
            'updated_at',
        ]
        

# 更新产妇每日必填记录序列化器
class MaternityDailyRequiredRecordUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityDailyRequiredRecord
        exclude = [
            'record_id',
            'maternity_admission',
            'creator',
            'created_at',
            'updated_at',
        ]



# 产妇入住评估详情序列化器
class MaternityCheckInAssessmentDetailSerializer(serializers.ModelSerializer):
    # 入所时间
    admission_time = ShanghaiFriendlyDateTimeField()
    # 评估时间
    assessment_time = ShanghaiFriendlyDateTimeField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()

    
    
    class Meta:
        model = MaternityCheckInAssessment
        exclude = [
            'id',
            "creator",
            "maternity_admission"
        ]
        
        

# 产妇入住评估创建序列化器
class MaternityCheckInAssessmentCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityCheckInAssessment
        exclude = [
            "created_at",
            "updated_at",
        ]
        
    # 校验食物过敏反应
    def validate_food_allergic_reaction(self, value):
        has_allergic_food = self.initial_data.get('has_allergic_food', False)
        
        if value and not has_allergic_food:
            raise serializers.ValidationError("无过敏食物时，不允许填写食物过敏反应")
        
        if has_allergic_food and not value:
            raise serializers.ValidationError("有过敏食物时，必须填写食物过敏反应")
            
        return value
    
    # 校验药物过敏反应
    def validate_drug_allergic_reaction(self, value):
        has_allergic_drug = self.initial_data.get('has_allergic_drug', False)
        
        if value and not has_allergic_drug:
            raise serializers.ValidationError("无过敏药物时，不允许填写药物过敏反应")
        
        if has_allergic_drug and not value:
            raise serializers.ValidationError("有过敏药物时，必须填写药物过敏反应")
        
        return value
        
    # 校验饮食要求其他描述
    def validate_dro_description(self, value):
        dietary_requirements = self.initial_data.get('dietary_requirements', DietaryRequirementsEnum.UNKNOWN)
        
        if dietary_requirements == DietaryRequirementsEnum.OTHER and not value:
            raise serializers.ValidationError("饮食要求为其他时，必须填写饮食要求其他描述")
        
        if dietary_requirements != DietaryRequirementsEnum.OTHER and value:
            raise serializers.ValidationError("饮食要求不为其他时，不允许填写饮食要求其他描述")
            
        return value
    
    # 校验饮食嗜好描述
    def validate_food_preference_description(self, value):
        has_food_preference = self.initial_data.get('has_food_preference', False)
        
        if not has_food_preference and value:
            raise serializers.ValidationError("无饮食嗜好时，不允许填写饮食嗜好描述")
        
        if has_food_preference and not value:
            raise serializers.ValidationError("有饮食嗜好时，必须填写饮食嗜好描述")
            
        return value
    
    # 校验家属对产妇的态度其他描述
    def validate_fatp_pther_description(self, value):
        family_attitude_to_patient = self.initial_data.get('family_attitude_to_patient', FamilyAttitudeToPatientEnum.UNKNOWN)
        
        if family_attitude_to_patient == FamilyAttitudeToPatientEnum.OTHER and not value:
            raise serializers.ValidationError("家属对产妇的态度为其他时，必须填写家属对产妇的态度其他描述")
        
        if family_attitude_to_patient != FamilyAttitudeToPatientEnum.OTHER and value:
            raise serializers.ValidationError("家属对产妇的态度不为其他时，不允许填写家属对产妇的态度其他描述")
        
        return value
    
    # 校验活动受限部位
    def validate_activity_limited_part(self, value):
        activity = self.initial_data.get('activity', ActivityEnum.UNKNOWN)
        
        if activity == ActivityEnum.LIMITED and not value:
            raise serializers.ValidationError("活动受限时，必须填写活动受限部位")
        
        if activity != ActivityEnum.LIMITED and value:
            raise serializers.ValidationError("活动不受限时，不允许填写活动受限部位")
        
        return value
    
    # 
    
    
    
        
# 产妇入住评估更新序列化器
class MaternityCheckInAssessmentUpdateSerializer(MaternityCheckInAssessmentCreateSerializer):
    
    class Meta:
        model = MaternityCheckInAssessment
        exclude = [
            'id',
            'maternity_admission',
            'creator', 
            'created_at', 
            'updated_at',
        ]
        
 

# 产妇康复护理评估记录序列化器

# 获取产妇康复护理评估记录列表
class MaternityRehabilitationAssessmentRecordListSerializer(serializers.ModelSerializer):

    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityRehabilitationAssessmentRecord
        fields = [
            'record_id',
            'record_date',
            'creator_name',
            'signature',
            'created_at',
            'updated_at',

        ]

    def get_creator_name(self, obj):
        return obj.creator.name if obj.creator else ""


# 获取产妇康复护理评估记录详情
class MaternityRehabilitationAssessmentRecordDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityRehabilitationAssessmentRecord
        exclude = [
            'id',
            'maternity_admission',
            'creator', 
        ]

    def get_creator_name(self, obj):
        return obj.creator.name if obj.creator else ""
        

# 创建产妇康复护理评估记录
class MaternityRehabilitationAssessmentRecordCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityRehabilitationAssessmentRecord
        exclude = [
            'record_id',
            "created_at",
            "updated_at",
        ]
        

# 更新产妇康复护理评估记录
class MaternityRehabilitationAssessmentRecordUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaternityRehabilitationAssessmentRecord
        exclude = [
            'record_id',
            'maternity_admission',
            'creator',
            'created_at',
            'updated_at',
        ]
        
        
        

# 产妇每日生理护理记录序列化器
# 获取产妇每日生理护理记录列表
class MaternityDailyPhysicalCareRecordListSerializer(serializers.ModelSerializer):

    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.SerializerMethodField()
    class Meta:
        model = MaternityDailyPhysicalCareRecord
        fields = [
            'record_id',
            'record_date',
            'creator_name',
            'signature',
            'created_at',
            'updated_at',

        ]

    def get_creator_name(self, obj):
        return obj.creator.name if obj.creator else ""


# 获取产妇每日生理护理记录详情
class MaternityDailyPhysicalCareRecordDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.SerializerMethodField()
    # 产后天数
    postpartum_days = serializers.SerializerMethodField()
    
    # 耐受性文本
    exercise_tolerance_display = serializers.SerializerMethodField()
    # 理解程度文本
    exercise_understanding_display = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityDailyPhysicalCareRecord
        exclude = [
            'id',
            'maternity_admission',
            'creator', 
        ]

    def get_creator_name(self, obj):
        return obj.creator.name if obj.creator else ""
    
    def get_postpartum_days(self, obj):
        return (obj.record_date - obj.maternity_admission.actual_delivery_date).days if obj.maternity_admission.actual_delivery_date else None
    
    def get_exercise_tolerance_display(self, obj):
        return ExerciseAfterEvaluationEnum(obj.exercise_tolerance).label
    
    def get_exercise_understanding_display(self, obj):
        return ExerciseAfterEvaluationEnum(obj.exercise_understanding).label

# 创建产妇每日生理护理记录
class MaternityDailyPhysicalCareRecordCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityDailyPhysicalCareRecord
        exclude = [
            'record_id',
            "created_at",
            "updated_at",
        ]
        

# 更新产妇每日生理护理记录
class MaternityDailyPhysicalCareRecordUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaternityDailyPhysicalCareRecord
        exclude = [
            'record_id',
            'maternity_admission',
            'creator',
            'created_at',
            'updated_at',
        ]
        
        






# 产妇膳食记录表序列化器
# 获取产妇膳食记录表列表
class MaternityDietRecordListSerializer(serializers.ModelSerializer):

    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityDailyDietRecord
        fields = [
            'record_id',
            'record_date',
            'creator_name',
            'created_at',
            'updated_at',

        ]

    def get_creator_name(self, obj):
        return obj.creator.name if obj.creator else ""


# 获取产妇膳食记录表详情
class MaternityDietRecordDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityDailyDietRecord
        exclude = [
            'id',
            'maternity_admission',
            'creator', 
        ]

    def get_creator_name(self, obj):
        return obj.creator.name if obj.creator else ""
        

# 创建产妇膳食记录表
class MaternityDietRecordCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityDailyDietRecord
        exclude = [
            "created_at",
            "updated_at",
        ]
        

# 更新产妇膳食记录表
class MaternityDietRecordUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaternityDailyDietRecord
        exclude = [
            'record_id',
            'maternity_admission',
            'creator',
            'created_at',
            'updated_at',
        ]