from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.core_records.enums.baby import BirthInjuryTypeChoice, ButtocksChoice, \
    ExtremityToneAndMovementChoice, FeedingMethodChoice, MonthFeedingSituationChoice, MonthSkinStatusChoice, \
    NutritionsDevelopmentChoice, OralMucosaChoice, SkinAbnormalityChoice, SkinStatusChoice, UmbilicalCordChoice, \
    VaccineInjectionChoice
from customer_service.core_records.models import (
    Newborn,
    NewbornCheckInAssessment,
)
from customer_service.core_records.models.baby import NewbornCareOneRecord, NewbornCareOperationRecord, \
    NewbornCareTwoRecord, NewbornDailyRequiredRecord, NewbornFeedingRecord, NewbornMonthAssessment
from user.serializers import MaternityDesensitizedSerializer


# 新生儿每日必填记录列表序列化器
class NewbornDailyRequiredRecordListSerializer(serializers.ModelSerializer):

    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 记录时间
    record_date = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人名字
    creator_name = serializers.SerializerMethodField(read_only=True)


    class Meta:
        model = NewbornDailyRequiredRecord
        fields = [
            'record_id',
            'record_date',
            'creator_name',
            'sleep_status',
            'bowel_movement_status',
            'created_at',
            'updated_at',
        ]

    def get_creator_name(self, obj):
        return obj.creator.name or '-'

# 新生儿每日必填记录详情序列化器
class NewbornDailyRequiredRecordDetailSerializer(serializers.ModelSerializer):

    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人名字
    creator_name = serializers.SerializerMethodField(read_only=True)
    # 记录时间
    record_date = ShanghaiFriendlyDateTimeField(read_only=True)

    class Meta:
        model = NewbornDailyRequiredRecord
        exclude = [
            'id',
            'creator',
            'newborn'
        ]

    def get_creator_name(self, obj):
        return obj.creator.name or '-'


# 创建新生儿每日必填记录序列化器
class NewbornDailyRequiredRecordCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = NewbornDailyRequiredRecord
        exclude = [
            'created_at',
            'updated_at',
        ]
        

# 更新新生儿每日必填记录序列化器
class NewbornDailyRequiredRecordUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = NewbornDailyRequiredRecord
        exclude = [
            'id',
            'record_id',
            'newborn',
            'creator',
            'created_at',
            'updated_at',
        ]



            
# 新生儿创建序列化器
class NewbornCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Newborn
        fields = [
            'maternity_admission',
            'name',
            'gender',
            'birth_time',
            'birth_weight',
            'birth_length',
            'allergy_history',
            'birth_week',
            'hand_card_number',
        ]
        

# 新生儿详情序列化器
class NewbornDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 母亲信息
    maternity = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = Newborn
        exclude = [
            'id',
            'maternity_admission',
        ]

    def get_maternity(self, obj):
        return MaternityDesensitizedSerializer(obj.maternity_admission.maternity).data
    
    
    
    
# 新生儿入住评估创建序列化器
class NewbornCheckInAssessmentCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = NewbornCheckInAssessment
        exclude = [
            "created_at",
            "updated_at",
        ]
        
    # 校验疾病名称
    def validate_hospitalization_disease(self, value):
        hospitalization_history = self.initial_data.get('hospitalization_history', False)
        
        if value and not hospitalization_history:
            raise serializers.ValidationError("无住院治疗史时，不允许填写疾病名称")
        
        if hospitalization_history and not value:
            raise serializers.ValidationError("有住院治疗史时，必须填写疾病名称")
            
        return value
    
    # 校验现是否用药—有—药物名称
    def validate_medication_name(self, value):
        current_medication = self.initial_data.get('current_medication', False)
        
        if value and not current_medication:
            raise serializers.ValidationError("现无用药时，不允许填写药物名称")
        
        if current_medication and not value:
            raise serializers.ValidationError("现有用药时，必须填写药物名称")
            
        return value
    
    # 校验奶粉品牌
    def validate_formula_brand(self, value):
        feeding_type = self.initial_data.get('feeding_type', FeedingMethodChoice.UNKNOWN)
        
        if feeding_type != FeedingMethodChoice.ARTIFICIAL_FEEDING and value:
            raise serializers.ValidationError("非人工喂养时，不允许填写奶粉品牌")
        
        if feeding_type == FeedingMethodChoice.ARTIFICIAL_FEEDING and not value:
            raise serializers.ValidationError("人工喂养时，必须填写奶粉品牌")
        
        return value
    
    # 校验配方奶与母乳量
    def validate_formula_breastmilk_ratio(self, value):
        feeding_type = self.initial_data.get('feeding_type', FeedingMethodChoice.UNKNOWN)
        
        if feeding_type != FeedingMethodChoice.MIXED_FEEDING and value:
            raise serializers.ValidationError("非混合喂养时，不允许填写配方奶与母乳量")
        
        if feeding_type == FeedingMethodChoice.MIXED_FEEDING and not value:
            raise serializers.ValidationError("混合喂养时，必须填写配方奶与母乳量")
        
        return value
        
    
    # 校验四肢张力及活动受限部位
    def validate_extremity_restricted_area(self, value):
        extremity_tone = self.initial_data.get('extremity_tone', ExtremityToneAndMovementChoice.UNKNOWN)
        
        if extremity_tone == ExtremityToneAndMovementChoice.GOOD and value:
            raise serializers.ValidationError("四肢张力正常时，不允许填写四肢张力及活动受限部位")
        
        if extremity_tone != ExtremityToneAndMovementChoice.GOOD and not value:
            raise serializers.ValidationError("四肢张力受限时，必须填写四肢张力及活动受限部位")
        
        return value
    
    # 校验产伤类型
    def validate_birth_injury_type(self, value):
        birth_injury = self.initial_data.get('birth_injury', False)
        
        if not birth_injury and value:
            raise serializers.ValidationError("无产伤时，不允许选择产伤类型")
        
        if birth_injury and not value:
            raise serializers.ValidationError("有产伤时，必须选择产伤类型")
        
        return value
    
    # 校验产伤皮损部位
    def validate_birth_injury_skin_lesion_location(self, value):
        birth_injury_type = self.initial_data.get('birth_injury_type', BirthInjuryTypeChoice.UNKNOWN)
        
        if birth_injury_type == BirthInjuryTypeChoice.SKIN_LESIONS and not value:
            raise serializers.ValidationError("产伤类型为皮损部位时，必须填写产伤皮损部位")
        
        if birth_injury_type != BirthInjuryTypeChoice.SKIN_LESIONS and value:
            raise serializers.ValidationError("产伤类型非皮损部位时，不允许填写产伤皮损部位")
        
        return value
    
    # 校验营养发育滞后位于多少百分位数
    def validate_nutrition_delayed_percentile(self, value):
        
        nutrition_development = self.initial_data.get('nutrition_development', NutritionsDevelopmentChoice.UNKNOWN)
        
        if nutrition_development == NutritionsDevelopmentChoice.DELAYED and not value:
            raise serializers.ValidationError("营养发育滞后时，必须填写营养发育滞后位于多少百分位数")
        
        if nutrition_development != NutritionsDevelopmentChoice.DELAYED and value:
            raise serializers.ValidationError("营养发育非滞后时，不允许填写营养发育滞后位于多少百分位数")
        
        return value
    
    # 校验全身皮肤异常
    def validate_skin_abnormality(self, value):
        skin_status = self.initial_data.get('skin_status', SkinStatusChoice.UNKNOWN)
        
        if skin_status == SkinStatusChoice.NORMAL and value:
            raise serializers.ValidationError("皮肤正常时，不允许选择异常选项")
        
        if skin_status != SkinStatusChoice.NORMAL and not value:
            raise serializers.ValidationError("皮肤异常时，必须选择异常选项")
        
        return value
    
    # 校验皮损部位
    def validate_skin_lesion_location(self, value):
        skin_abnormality = self.initial_data.get('skin_abnormality', SkinAbnormalityChoice.UNKNOWN)
        
        if skin_abnormality != SkinAbnormalityChoice.LESION and value:
            raise serializers.ValidationError("皮肤无皮损时，不允许填写皮损部位")

        if skin_abnormality == SkinAbnormalityChoice.LESION and not value:
            raise serializers.ValidationError("皮肤有皮损时，必须填写皮损部位")
        
        return value
    
    # 校验红斑部位
    def validate_skin_erythema_location(self, value):
        skin_abnormality = self.initial_data.get('skin_abnormality', SkinAbnormalityChoice.UNKNOWN)
        
        if skin_abnormality != SkinAbnormalityChoice.ERYTHEMA and value:
            raise serializers.ValidationError("皮肤无红斑时，不允许填写红斑部位")
        
        if skin_abnormality == SkinAbnormalityChoice.ERYTHEMA and not value:
            raise serializers.ValidationError("皮肤有红斑时，必须填写红斑部位")
        
        return value
    
    # 校验皮疹部位
    def validate_skin_rash_location(self, value):
        skin_abnormality = self.initial_data.get('skin_abnormality', SkinAbnormalityChoice.UNKNOWN)
        
        if skin_abnormality != SkinAbnormalityChoice.RASH and value:
            raise serializers.ValidationError("皮肤无皮疹时，不允许填写皮疹部位")
        
        if skin_abnormality == SkinAbnormalityChoice.RASH and not value:
            raise serializers.ValidationError("皮肤有皮疹时，必须填写皮疹部位")
        
        return value
    
    
    
    # 校验水肿部位
    def validate_skin_edema_location(self, value):
        skin_abnormality = self.initial_data.get('skin_abnormality', SkinAbnormalityChoice.UNKNOWN)
        
        if skin_abnormality != SkinAbnormalityChoice.EDEMA and value:
            raise serializers.ValidationError("皮肤无水肿时，不允许填写水肿部位")
        
        if skin_abnormality == SkinAbnormalityChoice.EDEMA and not value:
            raise serializers.ValidationError("皮肤有水肿时，必须填写水肿部位")
        
        return value
    
    # 校验口腔黏膜—异常
    def validate_oral_mucosa_abnormality(self, value):
        oral_mucosa = self.initial_data.get('oral_mucosa', OralMucosaChoice.UNKNOWN)
        
        if oral_mucosa != OralMucosaChoice.ABNORMAL and value:
            raise serializers.ValidationError("口腔黏膜正常时，不允许选择异常类型")
        
        if oral_mucosa == OralMucosaChoice.ABNORMAL and not value:
            raise serializers.ValidationError("口腔黏膜异常时，必须选择异常类型") 
        
        return value
        
    # 校验畸形类型
    def validate_anomaly_type(self, value):
        congenital_anomaly = self.initial_data.get('congenital_anomaly', False)
        
        if not congenital_anomaly and value:
            raise serializers.ValidationError("新生儿无畸形时，不允许选择畸形类型")
        
        if congenital_anomaly and not value:
            raise serializers.ValidationError("新生儿有畸形时，必须选择畸形类型")
        
        return value
        
    
    
    # 校验脐部异常
    def validate_umbilical_cord_abnormality(self, value):
        umbilical_cord = self.initial_data.get('umbilical_cord', UmbilicalCordChoice.UNKNOWN)
        
        if umbilical_cord != UmbilicalCordChoice.ABNORMAL and value:
            raise serializers.ValidationError("脐部正常时，不允许选择异常类型")
        
        if umbilical_cord == UmbilicalCordChoice.ABNORMAL and not value:
            raise serializers.ValidationError("脐部异常时，必须选择异常类型")
        
        return value
    
    
    # 校验臀部异常
    def validate_buttocks_abnormality(self, value):
        buttocks = self.initial_data.get('buttocks', ButtocksChoice.UNKNOWN)
        
        if buttocks != ButtocksChoice.ABNORMAL and value:
            raise serializers.ValidationError("臀部正常时，不允许选择异常类型")
        
        if buttocks == ButtocksChoice.ABNORMAL and not value:
            raise serializers.ValidationError("臀部异常时，必须选择异常类型")
        
        return value
    
    # 校验预防接种—其他
    def validate_vaccine_injection_other(self, value):
        vaccine_injection = self.initial_data.get('vaccine_injection', VaccineInjectionChoice.UNKNOWN)
        
        if vaccine_injection != VaccineInjectionChoice.OTHER and value:
            raise serializers.ValidationError("预防接种非其他时，不允许填写其他内容")
        
        if vaccine_injection == VaccineInjectionChoice.OTHER and not value:
            raise serializers.ValidationError("预防接种为其他时，必须填写其他内容")
        
        return value
    
    
# 新生儿入住评估详情序列化器
class NewbornCheckInAssessmentDetailSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 评估时间
    assessment_time = ShanghaiFriendlyDateTimeField(read_only=True)
    # 入所时间
    admission_time = ShanghaiFriendlyDateTimeField(read_only=True)

    class Meta:
        model = NewbornCheckInAssessment
        exclude = [

            'newborn',
            'creator', 
        ]


# 新生儿入住评估更新序列化器
class NewbornCheckInAssessmentUpdateSerializer(NewbornCheckInAssessmentCreateSerializer):
    
    class Meta:
        model = NewbornCheckInAssessment
        exclude = [
            'id',
            'newborn',
            'creator', 
            'created_at', 
            'updated_at',
        ]


# 新生儿护理记录单（1）序列化器

# 获取新生儿护理记录单（1）列表
class NewbornCareOneRecordListSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    
    
    class Meta:
        model = NewbornCareOneRecord
        fields = [
            'record_id',
            'record_date',
            'caregiver_signature',
            'created_at',
            'updated_at',
        ]
        
# 获取新生儿护理记录单（1）详情
class NewbornCareOneRecordDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    
    class Meta:
        model = NewbornCareOneRecord
        exclude = [
            'id',
            'newborn',
            'creator', 
        ]

# 创建新生儿护理记录单（1）
class NewbornCareOneRecordCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewbornCareOneRecord
        exclude = [
            "created_at",
            "updated_at",
        ]

# 更新新生儿护理记录单（1）
class NewbornCareOneRecordUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewbornCareOneRecord
        exclude = [
            'id',
            'record_id',
            'newborn',
            'creator',
            'created_at',
            'updated_at',
        ]



# 新生儿护理记录单（2）序列化器

# 获取新生儿护理记录单（2）列表
class NewbornCareTwoRecordListSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    
    class Meta:
        model = NewbornCareTwoRecord
        fields = [
            'record_id',
            'record_date',
            'caregiver_signature',
            'created_at',
            'updated_at',
        ]
        

#

# 获取新生儿护理记录单（2）详情
class NewbornCareTwoRecordDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    
    class Meta:
        model = NewbornCareTwoRecord
        exclude = [
            'id',
            'newborn',
            'creator', 
        ]

# 创建新生儿护理记录单（2）
class NewbornCareTwoRecordCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewbornCareTwoRecord
        exclude = [
            "created_at",
            "updated_at",
        ]
        

# 更新新生儿护理记录单（2）
class NewbornCareTwoRecordUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewbornCareTwoRecord
        exclude = [
            'id',
            'newborn',
            'creator',
            'created_at',
            'updated_at',
        ]



# 新生儿喂养记录序列化器

# 获取新生儿喂养记录列表
class NewbornFeedingRecordListSerializer(serializers.ModelSerializer):

    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 日期
    record_date = serializers.SerializerMethodField(read_only=True)
    # 喂养时间
    record_time = ShanghaiFriendlyDateTimeField(read_only=True)

    class Meta:
        model = NewbornFeedingRecord
        fields = [
            'record_id',
            'record_date',
            'record_time',
            'caregiver_signature',
            'created_at',
            'updated_at',
        ]
        
    def get_record_date(self, obj):
        return obj.record_time.date()

# 获取新生儿喂养记录详情
class NewbornFeedingRecordDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 喂养时间
    record_time = ShanghaiFriendlyDateTimeField(read_only=True)
    # 日期
    record_date = serializers.SerializerMethodField(read_only=True)
    # 喂养方式
    feeding_method_display = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = NewbornFeedingRecord
        exclude = [
            'id',
            'newborn',
            'creator', 
        ]
        
    def get_record_date(self, obj):
        return obj.record_time.date()

    def get_feeding_method_display(self, obj):
        return obj.get_feeding_method_display()

# 创建新生儿喂养记录
class NewbornFeedingRecordCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = NewbornFeedingRecord
        exclude = [
            "created_at",
            "updated_at",
        ]
        
    def validate_feeding_method(self, value):
        
        method_exclude_fields = {
            FeedingMethodChoice.BREAST_FEEDING: ['artificial_feeding_indicators', 'artificial_feeding_amount', 'mixed_feeding_self_feeding_time', 'mixed_feeding_human_feeding_ml'],
            FeedingMethodChoice.ARTIFICIAL_FEEDING: ['breast_feeding_left_time', 'breast_feeding_right_time', 'mixed_feeding_self_feeding_time', 'mixed_feeding_human_feeding_ml'],
            FeedingMethodChoice.MIXED_FEEDING: ['breast_feeding_left_time', 'breast_feeding_right_time', 'artificial_feeding_indicators', 'artificial_feeding_amount'],
        }
        
        if value in method_exclude_fields:
            for field in method_exclude_fields[value]:
                if self.initial_data.get(field):
                    raise serializers.ValidationError(f"数据校验失败")
        return value


        

# 更新新生儿喂养记录
class NewbornFeedingRecordUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewbornFeedingRecord
        exclude = [
            'id',
            'record_id',
            'newborn',
            'creator',
            'created_at',
            'updated_at',
        ]
        
        
        
        



# 新生儿护理操作记录序列化器

# 获取新生儿护理操作记录列表
class NewbornCareOperationRecordListSerializer(serializers.ModelSerializer):

    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)

    class Meta:
        model = NewbornCareOperationRecord
        fields = [
            'record_id',
            'record_date',
            'operator',
            'created_at',
            'updated_at',
        ]
        

# 获取新生儿护理操作记录详情
class NewbornCareOperationRecordDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    
    class Meta:
        model = NewbornCareOperationRecord
        exclude = [
            'id',
            'newborn',
            'creator', 
        ]
        

# 创建新生儿护理操作记录
class NewbornCareOperationRecordCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = NewbornCareOperationRecord
        exclude = [
            "created_at",
            "updated_at",
        ]
        

# 更新新生儿护理操作记录
class NewbornCareOperationRecordUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewbornCareOperationRecord
        exclude = [
            'id',
            'record_id',
            'newborn',
            'creator',
            'created_at',
            'updated_at',
        ]
        
        
        



# 新生儿满月评估

# 获取新生儿满月评估详情
class NewbornMonthAssessmentDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 评估时间
    assessment_date = ShanghaiFriendlyDateTimeField(read_only=True)
    
    class Meta:
        model = NewbornMonthAssessment
        exclude = [
            'id',
            'newborn',
            'creator', 
        ]
        

# 创建新生儿满月评估
class NewbornMonthAssessmentCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = NewbornMonthAssessment
        exclude = [
            "created_at",
            "updated_at",
        ]
        
    # 校验破损部位
    def validate_damaged_part(self, value):
        skin = self.initial_data.get('skin', MonthSkinStatusChoice.UNKNOWN)
        
        if MonthSkinStatusChoice.DAMAGED in skin and not value:
            raise serializers.ValidationError("皮肤破损时，必须填写破损部位")
        
        if MonthSkinStatusChoice.DAMAGED not in skin and value:
            raise serializers.ValidationError("皮肤无破损时，不允许填写破损部位")
        
        return value
    
    # 校验皮疹部位
    def validate_rash_part(self, value):
        skin = self.initial_data.get('skin', MonthSkinStatusChoice.UNKNOWN)
        
        if MonthSkinStatusChoice.RASH in skin and not value:
            raise serializers.ValidationError("皮肤有皮疹时，必须填写皮疹部位")
        
        if MonthSkinStatusChoice.RASH not in skin and value:
            raise serializers.ValidationError("皮肤无皮疹时，不允许填写皮疹部位")
        
        return value
    
    # 校验奶粉品牌
    def validate_formula_brand(self, value):
        feeding_situation = self.initial_data.get('feeding_situation', MonthFeedingSituationChoice.UNKNOWN)
        
        if feeding_situation != MonthFeedingSituationChoice.ARTIFICIAL_FEEDING and value:
            raise serializers.ValidationError("非人工喂养时，不允许填写奶粉品牌")
        
        if feeding_situation == MonthFeedingSituationChoice.ARTIFICIAL_FEEDING and not value:
            raise serializers.ValidationError("人工喂养时，必须填写奶粉品牌")
        
        return value
    
    # 校验肌张力及活动受限部位
    def validate_muscle_tone_and_activity_limited_part(self, value):
        muscle_tone_and_activity = self.initial_data.get('muscle_tone_and_activity', ExtremityToneAndMovementChoice.UNKNOWN)
        
        if muscle_tone_and_activity != ExtremityToneAndMovementChoice.LIMITED and value:
            raise serializers.ValidationError("四肢张力正常时，不允许填写四肢张力及活动受限部位")
        
        if muscle_tone_and_activity == ExtremityToneAndMovementChoice.LIMITED and not value:
            raise serializers.ValidationError("四肢张力受限时，必须填写四肢张力及活动受限部位")
        
        return value
    
    # 校验现存护理问题描述
    def validate_current_nursing_problems_description(self, value):
        current_nursing_problems = self.initial_data.get('current_nursing_problems', False)
        
        if current_nursing_problems and not value:
            raise serializers.ValidationError("有现存护理问题时，必须填写现存护理问题描述")
        
        if not current_nursing_problems and value:
            raise serializers.ValidationError("无现存护理问题时，不允许填写现存护理问题描述")
        
        return value
    
    

# 更新新生儿满月评估
class NewbornMonthAssessmentUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = NewbornMonthAssessment
        exclude = [
            'id',
            'assessment_id',
            'newborn',
            'creator',
            'created_at',
            'updated_at',
        ]