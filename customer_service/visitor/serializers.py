from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField, parse_datetime_to_shanghai_time
from core.utils import calculate_age
from .models import VisitorRecord, WechatAppVisitAppointment


# 访客登记列表序列化器
class VisitorRecordSerializer(serializers.ModelSerializer):

    # 被访者产妇
    visited_maternity_info = serializers.SerializerMethodField()
    # 来访时间
    visit_time = ShanghaiFriendlyDateTimeField()
    # 离开时间
    departure_time = serializers.SerializerMethodField()
    # 状态
    visit_status_display = serializers.SerializerMethodField()

    class Meta:
        model = VisitorRecord
        fields = ['vid', 'visitor_name', 'visitor_phone', 'visited_maternity_info','visit_purpose','visit_time', 'departure_time','temperature','visit_status','visit_status_display']

    def get_visited_maternity_info(self, obj):
        name = obj.visited_admission.maternity.name if obj.visited_admission and obj.visited_admission.maternity else None
        room = obj.visited_admission.room.room_number if obj.visited_admission and obj.visited_admission.room else None
        return f"{name if name else '-'} ({room if room else '-'})"

    def get_departure_time(self, obj):
        return parse_datetime_to_shanghai_time(obj.actual_departure_time) if obj.actual_departure_time else  f'{parse_datetime_to_shanghai_time(obj.expected_departure_time)}(预计)'

    def get_visit_status_display(self, obj):
        return obj.get_visit_status_display()

    
    
# 访客详情序列化器
class VisitorRecordDetailSerializer(serializers.ModelSerializer):
    

    # 被访者产妇
    visited_maternity_info = serializers.SerializerMethodField()
    # 被访者产妇编号
    visited_aid = serializers.SerializerMethodField()
    # 来访时间
    visit_time = ShanghaiFriendlyDateTimeField()
    # 预计离开时间
    expected_departure_time = ShanghaiFriendlyDateTimeField()
    # 实际离开时间
    actual_departure_time = ShanghaiFriendlyDateTimeField()
    # 离开时间
    departure_time = serializers.SerializerMethodField()
    # 状态
    visit_status_display = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = VisitorRecord
        exclude = ['maternity_center','id','visited_admission']
        
    def get_visited_maternity_info(self, obj):
        
        if not obj.visited_admission or not obj.visited_admission.maternity:
            return {}
        
        age = calculate_age(obj.visited_admission.maternity.birth_date)
        
        return {
            'age': age,
            'name': obj.visited_admission.maternity.name,
            'room': obj.visited_admission.room.room_number if obj.visited_admission and obj.visited_admission.room else "暂无记录",
            'phone': obj.visited_admission.maternity.phone,
            'gender': obj.visited_admission.maternity.get_gender_display(),
            'birth_date': obj.visited_admission.maternity.birth_date.strftime("%Y-%m-%d") if obj.visited_admission.maternity.birth_date else "暂无记录",
        } if obj.visited_admission and obj.visited_admission.maternity else {}
    
    def get_visited_aid(self, obj):
        return obj.visited_admission.aid if obj.visited_admission else ""
    
    def get_departure_time(self, obj):
        return parse_datetime_to_shanghai_time(obj.actual_departure_time) if obj.actual_departure_time else  f'{parse_datetime_to_shanghai_time(obj.expected_departure_time)}(预计)'

    def get_visit_status_display(self, obj):
        return obj.get_visit_status_display()
    

# 访客登记创建序列化器
class VisitorRecordCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = VisitorRecord
        fields = [
            'visitor_name',
            'visitor_phone', 
            'visitor_identity_number',
            'visited_admission',
            'visitor_count',
            'visit_purpose',
            'temperature',
            'fever_symptoms',
            'contact_with_case',
            'visit_time',
            'expected_departure_time',
            'appointment_remark',
            'visit_status',
            'maternity_center'
        ]
        

# 更新访客登记序列化器
class VisitorRecordUpdateSerializer(serializers.ModelSerializer):

    class Meta:
        model = VisitorRecord
        fields = [
            'visitor_name',
            'visitor_phone', 
            'visitor_identity_number',
            'visited_admission',
            'visitor_count',
            'visit_purpose',
            'temperature',
            'fever_symptoms',
            'contact_with_case',
            'visit_time',
            'expected_departure_time',
            'appointment_remark',
            'visit_status'
        ]




# 小程序预约参观列表序列化器
class WechatAppVisitListSerializer(serializers.ModelSerializer):
    
    # 预约状态
    visit_time = ShanghaiFriendlyDateTimeField()
    # account_bind_phone
    account_bind_phone = serializers.SerializerMethodField()
    # 预约状态
    appointment_status_display = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()

    class Meta:
        model = WechatAppVisitAppointment
        fields = ['vid', 'visit_time','visitor_count','visitor_name', 'visitor_phone', 'maternity_present','appointment_status','account_bind_phone','appointment_status_display','created_at']

    def get_account_bind_phone(self, obj):
        return obj.maternity.phone if obj.maternity else "N/A"

    def get_appointment_status_display(self, obj):
        return obj.get_appointment_status_display()

