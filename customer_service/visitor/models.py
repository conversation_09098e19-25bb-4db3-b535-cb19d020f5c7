from django.db import models

from core.generate_hashid import generate_visitor_record_number, generate_wechat_app_visit_appointment_number
from core.model import BaseModel
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from maternity_center.models import MaternityCenter
from user.models import Staff, Maternity
from .enum import VisitorRecordStatusEnum, WechatAppointmentStatusEnum


# Create your models here.
class VisitorRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心")
    # 访客姓名
    visitor_name = models.CharField(max_length=100, verbose_name="访客姓名")
    # 访客手机
    visitor_phone = models.CharField(max_length=20, verbose_name="访客手机")
    # 身份证号，可选
    visitor_identity_number = models.CharField(max_length=20, verbose_name="访客身份证号", blank=True, default="")
    # 被访者产妇入住记录
    visited_admission = models.ForeignKey(MaternityAdmission, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="被访者产妇入住记录",related_name="visitor_records")
    # 来访人数
    visitor_count = models.IntegerField(verbose_name="来访人数")
    # 来访事项
    visit_purpose = models.TextField(verbose_name="来访事项", blank=True, default="")
    # 体温
    temperature = models.FloatField(verbose_name="体温",blank=True, default=37.0)
    # 过去14天内是否有发热、咳嗽等症状?
    fever_symptoms = models.BooleanField(verbose_name="过去14天内是否有发热、咳嗽等症状?", default=False)
    # 过去14天内是否接触过确诊或疑似病例?
    contact_with_case = models.BooleanField(verbose_name="过去14天内是否接触过确诊或疑似病例?", default=False)
    # 来访时间
    visit_time = models.DateTimeField(verbose_name="来访时间")
    # 预计离开时间
    expected_departure_time = models.DateTimeField(verbose_name="预计离开时间")
    # 实际离开时间
    actual_departure_time = models.DateTimeField(verbose_name="实际离开时间", null=True, blank=True)
    # 预约备注
    appointment_remark = models.TextField(verbose_name="预约备注", blank=True, default="")
    # 访问结果备注
    visit_result_remark = models.TextField(verbose_name="访问结果备注", blank=True, default="")
    # 访问状态
    visit_status = models.CharField(max_length=10, verbose_name="访问状态", choices=VisitorRecordStatusEnum.choices, default=VisitorRecordStatusEnum.PENDING)
    # 标记离开
    leave_flag = models.BooleanField(verbose_name="标记离开", default=False)
    # 访客登记单号
    vid = models.CharField(max_length=100, verbose_name="访客登记单号", blank=True, default=generate_visitor_record_number)


    class Meta:
        verbose_name = "访客登记"
        verbose_name_plural = "访客登记"





# 预约参观
class WechatAppVisitAppointment(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name="wechat_app_visit_appointments")
    # 用户
    maternity = models.ForeignKey(Maternity, on_delete=models.SET_NULL, null=True,verbose_name="用户",related_name="wechat_app_visit_appointments") 
    # 参观时间
    visit_time = models.DateTimeField(verbose_name="参观时间")
    # 参观人数
    visitor_count = models.IntegerField(verbose_name="参观人数")
    # 预约人姓名
    visitor_name = models.CharField(max_length=100, verbose_name="预约参观人姓名")
    # 联系电话
    visitor_phone = models.CharField(max_length=20, verbose_name="预约参观人手机号码")
    # 产妇本人是否到场
    maternity_present = models.BooleanField(verbose_name="产妇本人是否到场")
    # 预约状态
    appointment_status = models.CharField(max_length=10, verbose_name="预约状态", choices=WechatAppointmentStatusEnum.choices, default=WechatAppointmentStatusEnum.PENDING)
    # 访客编号
    vid = models.CharField(max_length=100, verbose_name="访客编号", blank=True, default=generate_wechat_app_visit_appointment_number)
    
    
    class Meta:
        verbose_name = "小程序预约参观"
        verbose_name_plural = "小程序预约参观"
        
    def __str__(self):
        return f"{self.visitor_name} 预约参观 ({self.visit_time.strftime('%Y-%m-%d %H:%M')})"
    
    def mark_as_confirmed(self):
        self.appointment_status = WechatAppointmentStatusEnum.CONFIRMED
        self.save()
        
    def mark_as_rejected(self):
        self.appointment_status = WechatAppointmentStatusEnum.REJECTED
        self.save()
    
    def mark_as_completed(self):
        self.appointment_status = WechatAppointmentStatusEnum.COMPLETED
        self.save()
    
    def get_visitor_time(self):
        return self.visit_time.strftime('%Y-%m-%d %H:%M:%S')
    
    # 检查产妇是否有同一天的预约,传入字符串时间格式为2025-07-11 10:00:00
    @classmethod
    def check_has_same_day_visit(cls,user,visit_time):
        return cls.objects.filter(maternity=user,visit_time__date=visit_time.split(' ')[0],appointment_status__in=[WechatAppointmentStatusEnum.PENDING,WechatAppointmentStatusEnum.CONFIRMED]).exists()
    
    
    @classmethod
    def get_wechat_visit_queryset_by_vid(cls,user):
        try:
            return cls.objects.filter(maternity=user).order_by("visit_time")
        except cls.DoesNotExist:
            return None
        
    @classmethod
    def get_wechat_visit_by_vid(cls,vid,user):
        try:
            return cls.objects.get(vid=vid,maternity=user)
        except cls.DoesNotExist:
            return None
    
    
    
    