from django.db import models

class VisitorRecordStatusEnum(models.TextChoices):
    # 待访问
    PENDING = 'PENDING', '待访问'
    # 正在访问
    VISITING = 'VISITING', '正在访问'
    # 已离开
    LEAVED = 'LEAVED', '已离开'


# 预约参观状态
class WechatAppointmentStatusEnum(models.TextChoices):
    # 待处理
    PENDING = 'PENDING', '待处理'
    # 已确认
    CONFIRMED = 'CONFIRMED', '已确认'
    # 已取消
    CANCELLED = 'CANCELLED', '已取消'
    # 已完成
    COMPLETED = 'COMPLETED', '已完成'
    # 已拒绝
    REJECTED = 'REJECTED', '已拒绝'
