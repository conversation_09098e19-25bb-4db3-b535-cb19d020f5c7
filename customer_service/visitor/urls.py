from django.urls import path

from .views import (
    VisitorRecordListView,
    VisitorRecordDetailView,
    VisitorRecordCreateView,
    VisitorRecordUpdateView,
    VisitorRecordLeaveView,
    VisitorRecordDeleteView,
    VisitorRecordVisitingView,
    WechatAppVisitAuditView,
    WechatAppVisitCompleteView,
    WechatAppVisitListView,
)

urlpatterns = [
    # 访客登记列表
    path('visitor/list/', VisitorRecordListView.as_view(), name='visitor-record-list'),
    # 访客详情
    path('visitor/detail/<str:vid>/', VisitorRecordDetailView.as_view(), name='visitor-record-detail'),
    # 创建访客登记
    path('visitor/create/', VisitorRecordCreateView.as_view(), name='visitor-record-create'),
    # 更新访客登记
    path('visitor/update/<str:vid>/', VisitorRecordUpdateView.as_view(), name='visitor-record-update'),
    # 标记访客访问中
    path('visitor/visiting/<str:vid>/', VisitorRecordVisitingView.as_view(), name='visitor-record-visiting'),
    # 标记访客离开
    path('visitor/leave/<str:vid>/', VisitorRecordLeaveView.as_view(), name='visitor-record-leave'),
    # 删除访客登记
    path('visitor/delete/<str:vid>/', VisitorRecordDeleteView.as_view(), name='visitor-record-delete'),
    
    
    # 小程序预约参观
    path('wechatapp/visit/list/', WechatAppVisitListView.as_view(), name='wechat-visit-appointment-list'),
    # 小程序预约参观详情审核
    path('wechatapp/visit/audit/<str:vid>/', WechatAppVisitAuditView.as_view(), name='wechat-visit-audit'),#
    # 小程序预约参观标记完成
    path('wechatapp/visit/complete/<str:vid>/', WechatAppVisitCompleteView.as_view(), name='wechat-visit-complete'),


]