from django.contrib import admin

from .models import *


# Register your models here.



@admin.register(VisitorRecord)
class VisitorRecordAdmin(admin.ModelAdmin):
    list_display = ['vid', 'visitor_name', 'visitor_phone', 'visit_time', 'visit_status']
    list_filter = ['visit_status']
    search_fields = ['visitor_name', 'visitor_phone']

# 预约参观
@admin.register(WechatAppVisitAppointment)
class WechatAppVisitAppointmentAdmin(admin.ModelAdmin):
    list_display = ['vid', 'visit_time', 'visitor_name', 'visitor_phone', 'appointment_status']
    list_filter = ['appointment_status']
    search_fields = ['visitor_name', 'visitor_phone']
