from django.utils import timezone
from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.activity.enum import ActivityStatusEnum
from file.models import ActivityCoverFile
from .models import Activity


# 活动列表序列化器
class ActivityListSerializer(serializers.ModelSerializer):
    
    # 使用上海时间处理时间字段
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 开始时间
    start_time = ShanghaiFriendlyDateTimeField(read_only=True)
    # 结束时间
    end_time = ShanghaiFriendlyDateTimeField(read_only=True)
    # 状态
    status = serializers.SerializerMethodField(read_only=True)
    # 状态显示
    status_display = serializers.SerializerMethodField(read_only=True)
    # 封面
    cover = serializers.SerializerMethodField(read_only=True)
    class Meta:
        model = Activity
        fields = ['rid','name','start_time','end_time','location','speaker','cover','status','status_display','created_at','updated_at']
        
    def get_status(self, obj):
        now = timezone.now()
        
        if obj.start_time > now:
            return ActivityStatusEnum.UPCOMING
        elif obj.start_time <= now < obj.end_time:
            return ActivityStatusEnum.IN_PROGRESS
        else:
            return ActivityStatusEnum.COMPLETED
            
    def get_status_display(self, obj):
        status = self.get_status(obj)
        return ActivityStatusEnum(status).label

    def get_cover(self, obj):
        cover_file = ActivityCoverFile.get_activity_cover_file_by_rid(obj.cover,obj.maternity_center)
        if cover_file:
            return cover_file.file.url
        return None


# 活动详情序列化器
class ActivityDetailSerializer(serializers.ModelSerializer):
    # 使用上海时间处理时间字段
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    start_time = ShanghaiFriendlyDateTimeField(read_only=True)
    end_time = ShanghaiFriendlyDateTimeField(read_only=True)
    creator_name = serializers.CharField(source='creator.name', read_only=True)
    cover_url = serializers.SerializerMethodField(read_only=True)
    class Meta:
        model = Activity
        exclude = ['maternity_center','id','creator']
    
    def get_cover_url(self, obj):
        cover_file = ActivityCoverFile.get_activity_cover_file_by_rid(obj.cover,obj.maternity_center)
        if cover_file:
            return cover_file.file.url
        return None

# 活动创建序列化器
class ActivityCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Activity
        fields = ['maternity_center','name', 'activity_type', 'start_time', 'end_time', 'location','speaker','cover','introduction','details','attention','creator']

# 更新活动序列化器
class ActivityUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Activity
        fields = ['name', 'activity_type', 'start_time', 'end_time', 'location','speaker','cover','introduction','details','attention']