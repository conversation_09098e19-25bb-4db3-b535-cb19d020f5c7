from django.urls import path

from .views import (
    ActivityListView,
    ActivityCreateView,
    ActivityDetailView,
    ActivityUpdateView,
    ActivityDeleteView,
    ActivityTypeView,
)

urlpatterns = [
    # 活动相关
    path('activity/list/', ActivityListView.as_view(), name='activity-list'),
    # 活动类型
    path('activity/type/', ActivityTypeView.as_view(), name='activity-type'),
    # 活动创建
    path('activity/create/', ActivityCreateView.as_view(), name='activity-create'),
    # 活动详情
    path('activity/detail/<str:rid>/', ActivityDetailView.as_view(), name='activity-detail'),
    # 活动更新
    path('activity/update/<str:rid>/', ActivityUpdateView.as_view(), name='activity-update'),
    # 活动删除
    path('activity/delete/<str:rid>/', ActivityDeleteView.as_view(), name='activity-delete'),

]