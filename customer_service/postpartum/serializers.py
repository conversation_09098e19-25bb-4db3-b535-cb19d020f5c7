from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.postpartum.models import PostpartumProject,PostpartumRecord

# 康复项目列表序列化器
class PostpartumProjectListSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 状态标签
    status_label = serializers.SerializerMethodField()
    
    class Meta:
        model = PostpartumProject
        fields = ['rid','name','description','status','status_label','created_at','updated_at']

    def get_status_label(self,obj):
        return obj.get_status_display()

# 康复项目创建序列化器
class PostpartumProjectCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = PostpartumProject
        fields = ['maternity_center','name','description','status']

# 康复项目更新序列化器
class PostpartumProjectUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = PostpartumProject
        fields = ['name','description','status']
        
        
        

# 康复记录列表序列化器
class PostpartumRecordListSerializer(serializers.ModelSerializer):
    # 项目名称
    project_name = serializers.SerializerMethodField()
    # 康复时间
    recovery_time = ShanghaiFriendlyDateTimeField()
    # 康复产妇
    maternity = serializers.SerializerMethodField()
    
    class Meta:
        model = PostpartumRecord
        fields = ['rid','recovery_time','maternity','project_name','recovery_evaluation',]
    
    def get_project_name(self,obj):
        return obj.project.name
    
    def get_maternity(self,obj):
        return f'{obj.maternity_admission.maternity.name}-({obj.maternity_admission.room.room_number})'

# 康复记录详情序列化器
class PostpartumRecordDetailSerializer(serializers.ModelSerializer):
    
    project = PostpartumProjectListSerializer()
    
    maternity_admission = serializers.SerializerMethodField()
    
    maternity = serializers.SerializerMethodField()

    created_at = ShanghaiFriendlyDateTimeField()  
      
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = PostpartumRecord
        fields = ['rid','maternity_admission','maternity','recovery_time','recovery_evaluation','recovery_remark','project','created_at','updated_at']


    def get_maternity_admission(self,obj):
        return obj.maternity_admission.aid
    
    def get_maternity(self,obj):
        return f'{obj.maternity_admission.maternity.name}-({obj.maternity_admission.room.room_number})'

        
# 康复记录创建序列化器
class PostpartumRecordCreateSerializer(serializers.ModelSerializer):
    
    # 项目名称
    project = serializers.CharField()
    # 入院单
    maternity_admission = serializers.CharField() 
        
    class Meta:
        model = PostpartumRecord
        fields = ['maternity_center','maternity_admission','project','recovery_time','recovery_evaluation','recovery_remark']
        
    def validate_project(self,value):
        
        pro = PostpartumProject.get_project_by_rid(value,self.context['maternity_center'],status='ENABLED')
        
        if not pro:
            raise serializers.ValidationError("项目不存在")
        
        return pro
    
    def validate_maternity_admission(self,value):
        
        ma_ins = MaternityAdmission.get_maternity_admission_by_aid(value,self.context['maternity_center'])
        
        if not ma_ins:
            raise serializers.ValidationError("产妇入院记录不存在")
        
        return ma_ins
 

# 康复记录更新序列化器
class PostpartumRecordUpdateSerializer(serializers.ModelSerializer):
    
    project = serializers.CharField()
    
    class Meta:
        model = PostpartumRecord
        fields = ['project','recovery_time','recovery_evaluation','recovery_remark']
        
    def validate_project(self, value):
        if hasattr(self.instance, 'project') and self.instance.project.rid == value:
            return self.instance.project
        
        pro = PostpartumProject.get_project_by_rid(value, self.context['maternity_center'])
        if not pro:
            raise serializers.ValidationError("项目不存在")
        
        if pro.status != 'ENABLED':
            raise serializers.ValidationError("只能选择启用状态的项目")
        
        return pro
    
    
    

# 在住产妇选择列表序列化器
class MaternitySelectListSerializer(serializers.ModelSerializer):
    
    maternity = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityAdmission
        fields = ['aid','maternity']
    
    def get_maternity(self,obj):
        
        name = obj.maternity.name or '-'
        room_number = obj.room.room_number or '-'
        
        return f'{name}-({room_number})'