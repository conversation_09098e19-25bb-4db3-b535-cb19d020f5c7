from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import CheckInStatusEnum
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.postpartum.models import PostpartumProject, PostpartumRecord
from customer_service.postpartum.serializers import MaternitySelectListSerializer, PostpartumProjectCreateSerializer, PostpartumProjectListSerializer, PostpartumProjectUpdateSerializer, PostpartumRecordCreateSerializer, PostpartumRecordDetailSerializer, PostpartumRecordListSerializer, PostpartumRecordUpdateSerializer
from permissions.enum import PermissionEnum


# 康复项目列表
class PostpartumProjectListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_VIEW
    serializer_class = PostpartumProjectListSerializer
    response_msg = "获取康复项目列表成功"
    error_response_msg = "获取康复项目列表失败"
    search_fields = ['name','description']
    
    def get_queryset(self):
        
        base_queryset = PostpartumProject.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
        
        
        status = self.request.query_params.get('status',None)
                
        if status:
            base_queryset = base_queryset.filter(status=status)

        return base_queryset
    
    

# 康复项目创建
class PostpartumProjectCreateView(APIView): 
    
    authentication_classes = [CareCenterAuthentication] 
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_EDIT
    
    def post(self,request):
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = PostpartumProjectCreateSerializer(data=data)
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='康复项目创建成功',data=PostpartumProjectListSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='康复项目创建失败')
        

# 康复项目更新
class PostpartumProjectUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_EDIT
    
    def put(self,request,rid):
        project = PostpartumProject.get_project_by_rid(rid,request.user.maternity_center)
        
        if not project:
            return make_response(code=-1,msg='康复项目不存在')
        
        data = request.data.copy()
                
        serializer = PostpartumProjectUpdateSerializer(project,data=data)
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='康复项目更新成功',data=PostpartumProjectListSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='康复项目更新失败')


# 康复记录列表
class PostpartumRecordListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_VIEW
    serializer_class = PostpartumRecordListSerializer
    response_msg = "获取康复记录列表成功"
    error_response_msg = "获取康复记录列表失败"
    search_fields = ['maternity_admission__maternity__name','project__name','maternity_admission__room__room_number']
    
    def get_queryset(self):
        
        base_queryset = PostpartumRecord.objects.select_related('maternity_admission__maternity','maternity_admission__room').filter(maternity_center=self.request.user.maternity_center).order_by('-recovery_time')

        return base_queryset


# 康复记录详情
class PostpartumRecordDetailView(APIView):
    authentication_classes = [CareCenterAuthentication] 
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_VIEW
    
    def get(self,request,rid):
        
        record = PostpartumRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not record:
            return make_response(code=-1,msg='康复记录不存在')
        
        return make_response(code=0,msg='获取康复记录详情成功',data=PostpartumRecordDetailSerializer(record).data)
        


# 康复记录创建
class PostpartumRecordCreateView(APIView): 
    
    authentication_classes = [CareCenterAuthentication] 
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_EDIT
    
    def post(self,request):
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = PostpartumRecordCreateSerializer(data=data,context={'maternity_center':request.user.maternity_center})
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='康复记录创建成功',data=PostpartumRecordDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='康复记录创建失败，数据校验失败。')

# 康复记录更新
class PostpartumRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_EDIT
    
    def put(self,request,rid):
        
        record = PostpartumRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not record:
            return make_response(code=-1,msg='康复记录不存在')

        data = request.data.copy()
        
        serializer = PostpartumRecordUpdateSerializer(record,data=data,context={'maternity_center':request.user.maternity_center})
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='康复记录更新成功',data=PostpartumRecordDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='康复记录更新失败，数据校验失败。')


# 康复记录删除
class PostpartumRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_EDIT
    
    def delete(self,request,rid):
        ins = PostpartumRecord.get_by_rid(rid,request.user.maternity_center)
        
        if not ins:
            return make_response(code=-1,msg="康复记录不存在")
        
        ins.delete()
        
        return make_response(code=0,msg='康复记录删除成功')

# 在住产妇选择列表
class MaternitySelectListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_EDIT
    
    def get(self,request):
        
        queryset = MaternityAdmission.objects.filter(maternity_center=request.user.maternity_center,check_in_status=CheckInStatusEnum.CHECKED_IN).order_by('-actual_check_in_date')
        
        return make_response(code=0,msg='获取在住产妇选择列表成功',data=MaternitySelectListSerializer(queryset,many=True).data)
        
        
        
        



