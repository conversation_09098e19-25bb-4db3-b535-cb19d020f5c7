from django.urls import path

from .views import (
    HealthEducationListView,
    HealthEducationCreateView,
    HealthEducationDetailView,
    HealthEducationRecordCreateView,
    HealthEducationRecordDeleteView,
    HealthEducationRecordListView,
    HealthEducationRecordUpdateView,
    HealthEducationUpdateView,
    HealthEducationDeleteView,
    HealthEducationRecordDetailView,
    InHouseMaternitySelectListView
)

urlpatterns = [
    # 健康宣教内容列表
    path('health-education/content/list/', HealthEducationListView.as_view(), name='health-education-list'),
    # 健康宣教内容详情
    path('health-education/content/detail/<str:rid>/', HealthEducationDetailView.as_view(), name='health-education-detail'),
    # 健康宣教内容创建
    path('health-education/content/create/', HealthEducationCreateView.as_view(), name='health-education-create'),
    # 健康宣教内容更新
    path('health-education/content/update/<str:rid>/', HealthEducationUpdateView.as_view(), name='health-education-update'),
    # 健康宣教内容删除
    path('health-education/content/delete/<str:rid>/', HealthEducationDeleteView.as_view(), name='health-education-delete'),
    
    
    # 健康宣教活动记录列表
    path('health-education/record/list/', HealthEducationRecordListView.as_view(), name='health-education-record-list'),
    # 健康宣教活动记录详情
    path('health-education/record/detail/<str:eid>/', HealthEducationRecordDetailView.as_view(), name='health-education-record-detail'),
    # 健康宣教活动记录创建
    path('health-education/record/create/', HealthEducationRecordCreateView.as_view(), name='health-education-record-create'),
    # 健康宣教活动记录更新
    path('health-education/record/update/<str:eid>/', HealthEducationRecordUpdateView.as_view(), name='health-education-record-update'),
    # # 健康宣教活动记录删除
    path('health-education/record/delete/<str:eid>/', HealthEducationRecordDeleteView.as_view(), name='health-education-record-delete'),
    # 在住产妇选择列表
    path('health-education/maternity/select/list/', InHouseMaternitySelectListView.as_view(), name='in-house-maternity-select-list'),
    
]