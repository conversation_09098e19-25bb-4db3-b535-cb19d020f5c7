from datetime import datetime
import hashlib
import os
from core.generate_hashid import generate_health_education_code, generate_resource_uuid
from core.model import BaseModel
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from maternity_center.models import MaternityCenter
from user.models import Staff
from .enum import *
from django.core.validators import MinValueValidator, MaxValueValidator


def generate_secure_filename(original_filename):
    ext = os.path.splitext(original_filename)[1]
    hash_input = f"{original_filename}_{datetime.now().isoformat()}_{os.urandom(8).hex()}"
    file_hash = hashlib.md5(hash_input.encode()).hexdigest()[:17]
    secure_filename = f"{file_hash}{ext}"
    return secure_filename

def health_education_attachment_upload_path(instance, filename):
    secure_name = generate_secure_filename(filename)
    return f"hel/attachment/{instance.maternity_center.file_identifier}/{secure_name}"


class HealthEducationContent(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心")
    # 标题
    title = models.CharField(max_length=255, verbose_name="主题/标题")
    # 分类
    category = models.CharField(max_length=100, verbose_name="分类")
    # 关键词
    keywords = models.CharField(max_length=255, verbose_name="关键词",blank=True,default='')
    # 格式
    format = models.CharField(max_length=50, choices=FormatEnum.choices, verbose_name="格式")
    # 内容摘要
    summary = models.TextField( blank=True, verbose_name="内容摘要",default='')
    # 详细内容
    content = models.TextField(blank=True, verbose_name="详细内容",default='')
    # 附件
    attachment = models.FileField(upload_to=health_education_attachment_upload_path, verbose_name="附件",blank=True,null=True)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="创建人", blank=True,null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid",default=generate_resource_uuid)



    class Meta:
        verbose_name = "健康宣教内容"
        verbose_name_plural = "健康宣教内容"

    def __str__(self):
        return self.title
    @classmethod
    def get_all_category_types(cls, maternity_center):
            queryset = cls.objects.all()
            if not maternity_center:
                return []
            return list(set(cls.objects.filter(maternity_center=maternity_center).values_list('category', flat=True)))

    @classmethod
    def get_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
        

# 宣教活动记录
class HealthEducationRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心")
    # 宣教主题
    content = models.ForeignKey(HealthEducationContent, on_delete=models.SET_NULL, verbose_name="宣教主题",blank=True,null=True)
    # 宣教对象入院记录
    admission = models.ForeignKey(MaternityAdmission, on_delete=models.SET_NULL, verbose_name="宣教对象入院记录",blank=True,null=True)
    # 宣教方式
    way = models.CharField(max_length=50, choices=HealthEducationWayEnum.choices, verbose_name="宣教方式")
    # 宣教时间
    edu_time = models.DateTimeField(verbose_name="宣教时间")
    # 宣教地点
    edu_place = models.CharField(max_length=255, verbose_name="宣教地点")
    # 执行人
    executor = models.CharField(max_length=30, verbose_name="执行人")
    # 内容分类
    content_category = models.CharField(max_length=50, verbose_name="内容分类",blank=True,default='')
    # 内容摘要
    content_summary = models.TextField(blank=True, verbose_name="内容摘要",default='')
    # 关键知识点
    key_points = models.TextField(blank=True, verbose_name="关键知识点",default='') 
    # 使用材料或工具
    materials_tools = models.TextField(blank=True, verbose_name="使用材料或工具",default='')
    # 反馈
    feedback = models.TextField(blank=True, verbose_name="反馈",default='')
    # 理解程度评分，1-5
    understanding_score = models.PositiveIntegerField(verbose_name="理解程度评分",validators=[MinValueValidator(1), MaxValueValidator(5)])
    # 参与度评估
    participation_evaluation = models.CharField(max_length=50, choices=HealthEducationParticipationEvaluationEnum.choices, verbose_name="参与度评估")
    # 后续跟进计划  
    follow_up_plan = models.TextField(blank=True, verbose_name="后续跟进计划",default='')
    # 备注
    remark = models.TextField(blank=True, verbose_name="备注",default='')
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="创建人",blank=True,null=True)
    # 宣教编号
    eid = models.CharField(max_length=100, verbose_name="宣教编号",default=generate_health_education_code)
    

    class Meta:
        verbose_name = "宣教活动记录"
        verbose_name_plural = "宣教活动记录"

    def __str__(self):
        return f"{self.maternity_center.name} - {self.content.title if self.content else ''}"
    
    @classmethod
    def get_by_eid(cls, eid,maternity_center):
        try:
            return cls.objects.get(eid=eid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
    
    
    
    
    
    
    