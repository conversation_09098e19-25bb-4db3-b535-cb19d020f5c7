from django.db import models


# 格式
class FormatEnum(models.TextChoices):
    # 文字
    TEXT = 'TEXT', '文字'
    # 文档
    DOCUMENT = 'DOCUMENT', '文档'
    # 视频
    VIDEO = 'VIDEO', '视频'
    # 图片
    IMAGE = 'IMAGE', '图片'
    # 音频
    AUDIO = 'AUDIO', '音频'
    # PPT
    PPT = 'PPT', 'PPT'
    # 其他
    OTHER = 'OTHER', '其他'
    
    
# 宣教方式
class HealthEducationWayEnum(models.TextChoices):
    # 一对一指导
    ONE_ON_ONE_GUIDANCE = 'ONE_ON_ONE_GUIDANCE', '一对一指导'
    # 集体讲座
    GROUP_LECTURE = 'GROUP_LECTURE', '集体讲座'
    # 资料发放
    MATERIAL_DISTRIBUTION = 'MATERIAL_DISTRIBUTION', '资料发放'
    # 视频观看
    VIDEO_WATCHING = 'VIDEO_WATCHING', '视频观看'
    # 演示指导
    DEMONSTRATION_GUIDANCE = 'DEMONSTRATION_GUIDANCE', '演示指导'
    
# 参与度评估
class HealthEducationParticipationEvaluationEnum(models.TextChoices):
    # 主动
    ACTIVE = 'ACTIVE', '积极主动'
    # 良好
    GOOD = 'GOOD', '配合良好'
    # 一般
    AVERAGE = 'AVERAGE', '一般配合'
    # 被动接受
    PASSIVE = 'PASSIVE', '被动接受'