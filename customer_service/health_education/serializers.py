from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from user.serializers import MaternityDesensitizedSerializer
from .enum import FormatEnum
from .models import HealthEducationContent, HealthEducationRecord

# 健康宣教内容列表
class HealthEducationContentListSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.CharField(source='creator.name', read_only=True)

    class Meta:
        model = HealthEducationContent
        fields = ['rid','title','category','keywords','format','created_at','creator_name']
        
# 健康宣教内容详情
class HealehEducationContentDetailSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.Char<PERSON><PERSON>(source='creator.name', read_only=True)
    
    class Meta:
        model = HealthEducationContent
        exclude = ['maternity_center','creator','id']

# 健康宣教内容更新
class HealthEducationContentUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = HealthEducationContent
        fields = [
            'title',
            'category',
            'keywords',
            'format',
            'summary',
            'content',
            'attachment',
        ]

# 健康宣教内容创建
class HealthEducationContentCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = HealthEducationContent
        fields = [
            'maternity_center',
            'title',
            'category',
            'keywords',
            'format',
            'summary',
            'content',
            'attachment',
            'creator',
        ]



# 健康宣教活动记录列表
class HealthEducationRecordListSerializer(serializers.ModelSerializer):
    # 宣教主题
    content_title = serializers.SerializerMethodField()
    # 被宣教人
    maternity_name = serializers.SerializerMethodField()
    # 宣教方式
    way_display = serializers.SerializerMethodField()
    # 宣教时间
    edu_time = ShanghaiFriendlyDateTimeField(read_only=True)

    class Meta:
        model = HealthEducationRecord
        fields = ['eid','content_title','maternity_name','way_display','edu_time','executor','edu_place']

    def get_content_title(self, obj):
        return obj.content.title if obj.content else '无主题'

    def get_maternity_name(self, obj):
        name = obj.admission.maternity.name if obj.admission else '-'
        room_number = obj.admission.room.room_number if obj.admission else '-'
        return f"{name} - {room_number}"

    def get_way_display(self, obj):
        return obj.get_way_display()


# 健康宣教活动记录详情
class HealehEducationRecordDetailSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 创建人
    creator_name = serializers.CharField(source='creator.name', read_only=True)
    # 宣教时间
    edu_time = ShanghaiFriendlyDateTimeField(read_only=True)
    # 方式
    way_display = serializers.SerializerMethodField()
    # 参与度评估
    participation_evaluation_display = serializers.SerializerMethodField()
    # content
    content = serializers.SerializerMethodField()
    # content
    content_info = serializers.SerializerMethodField()
    # admission
    admission = serializers.SerializerMethodField()
    # admission
    admission_info = serializers.SerializerMethodField()
    class Meta:
        model = HealthEducationRecord
        exclude = ['maternity_center','creator','id']

    def get_way_display(self, obj):
        return obj.get_way_display()

    def get_participation_evaluation_display(self, obj):
        return obj.get_participation_evaluation_display()
    
    def get_content(self, obj):
        return obj.content.rid if obj.content else None
    
    def get_content_info(self, obj):
        return HealehEducationContentDetailSerializer(obj.content).data
    
    def get_admission(self, obj):
        return obj.admission.aid if obj.admission else None

    def get_admission_info(self, obj):
        return MaternityDesensitizedSerializer(obj.admission.maternity).data if obj.admission else None


# 健康宣教活动记录更新
class HealthEducationRecordUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = HealthEducationRecord
        fields = [
            'content',
            'admission',
            'way',
            'edu_time',
            'edu_place',
            'executor',
            'content_category',
            'content_summary',
            'key_points',
            'materials_tools',
            'feedback',
            'understanding_score',
            'participation_evaluation',
            'follow_up_plan',
            'remark',
        ]


# 健康宣教内容创建
class HealthEducationRecordCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = HealthEducationRecord
        fields = [
            'maternity_center',
            'content',
            'admission',
            'way',
            'edu_time',
            'edu_place',
            'executor',
            'content_category',
            'content_summary',
            'key_points',
            'materials_tools',
            'feedback',
            'understanding_score',
            'participation_evaluation',
            'follow_up_plan',
            'remark',
            'creator',
        ]

    def validate_admission(self, value):
        if not value:
            return None
        return value

    def validate(self, data):
        """
        验证 admission 字段：只有当 way 等于 ONE_ON_ONE_GUIDANCE（一对一指导）时，
        admission 才能有值，否则 admission 应该为空
        """
        way = data.get('way')
        admission = data.get('admission')

        from .enum import HealthEducationWayEnum

        if way != HealthEducationWayEnum.ONE_ON_ONE_GUIDANCE and admission is not None:
            raise serializers.ValidationError({
                'admission': '只有在宣教方式为"一对一指导"时才能选择宣教对象'
            })

        return data



