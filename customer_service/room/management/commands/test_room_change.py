from django.core.management import BaseCommand

from core.enum import CheckInStatusEnum
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.room.models import Room


class Command(BaseCommand):
    help = 'Room Change Test'

    def handle(self, *args, **options):
        admission = MaternityAdmission.objects.get(id=3)
        room = Room.objects.get(id=1)
        existing_admissions = MaternityAdmission.objects.filter(
            room=room,
            check_in_status__in=[CheckInStatusEnum.RESERVED, CheckInStatusEnum.CHECKED_IN]
        )
        result,error_msg = room.check_room_available(admission.expected_check_out_date,existing_admissions)
        print(result,error_msg)

