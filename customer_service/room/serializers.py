from datetime import timedelta

from django.utils import timezone
from rest_framework import serializers

from core.config import FACILITY_LIST
from core.enum import CheckInStatusEnum
from core.parse_time import ShanghaiFriendlyDateTimeField
from core.utils import calculate_age
from .enum import RoomStatusEnum
from .models import Room, RoomStatusHistory


# 房间状态历史
class RoomStatusHistorySerializer(serializers.ModelSerializer):
    created_at = ShanghaiFriendlyDateTimeField()
    expected_recovery_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = RoomStatusHistory
        exclude = ['id','room']


class RoomSerializer(serializers.ModelSerializer):
    created_at = ShanghaiFriendlyDateTimeField(required=False)
    updated_at = ShanghaiFriendlyDateTimeField(required=False)
    room_status_history = RoomStatusHistorySerializer(many=True, read_only=True)

    class Meta:
        model = Room
        exclude = ['reason_remark','expected_recovery_time','id','maternity_center']
    


class RoomCreateSerializer(serializers.ModelSerializer):


    class Meta:
        model = Room
        exclude = ['reason_remark','expected_recovery_time','id','rid']


# 房间详情
class RoomDetailSerializer(serializers.ModelSerializer):
    
    # 房间历史
    room_status_history = RoomStatusHistorySerializer(many=True, read_only=True)
    
    # 格式化时间字段
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField() 
    expected_recovery_time = ShanghaiFriendlyDateTimeField()
      
    class Meta:
        model = Room
        exclude = ['maternity_center','id']
        
    def get_room_status_history(self, obj):
        history = obj.room_status_history.order_by('-created_at')[:10]
        return RoomStatusHistorySerializer(history, many=True).data
    
# 房间基本信息更新序列化器
class RoomUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Room
        fields = [
            'room_number', 'room_type', 'area', 
            'orientation', 'floor', 'facility_list', 'description'
        ]
        
    # 验证facility_list是否为列表
    def validate_facility_list(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("设施清单必须为列表")
        for facility in value:
            if facility not in FACILITY_LIST:
                raise serializers.ValidationError(f"{facility}不在设施清单中")
        return value


# 房间总览序列化器（用于way='normal'的情况）
class RoomOverviewNormalSerializer(serializers.ModelSerializer):
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    current_status = serializers.SerializerMethodField()
    customer_info = serializers.SerializerMethodField()

    class Meta:
        model = Room
        exclude = ['reason_remark', 'expected_recovery_time']
        read_only_fields = ('created_at', 'updated_at')

    def get_current_status(self, obj):
        # 获取当前状态
        current_date = timezone.now().date()

        checked_in_admission = obj.maternity_admission.filter(
            check_in_status=CheckInStatusEnum.CHECKED_IN
        ).first()

        if checked_in_admission:
            return CheckInStatusEnum.CHECKED_IN

        reserved_admission = obj.maternity_admission.filter(
            check_in_status=CheckInStatusEnum.RESERVED
        ).first()

        if reserved_admission:
            expected_check_in_date = reserved_admission.expected_check_in_date
            if expected_check_in_date and expected_check_in_date >= current_date:
                return CheckInStatusEnum.RESERVED

        return obj.room_status

    def get_customer_info(self, obj):
        # 获取客户信息
        current_date = timezone.now().date()

        # 优先获取已入住的产妇信息
        admission = obj.maternity_admission.filter(
            check_in_status=CheckInStatusEnum.CHECKED_IN
        ).first()

        # 如果没有已入住的，获取有效预约的产妇信息（预约日期未过期）
        if not admission:
            reserved_admissions = obj.maternity_admission.filter(
                check_in_status=CheckInStatusEnum.RESERVED
            )
            # 只获取预约日期大于或等于当前日期的预约
            for reserved_admission in reserved_admissions:
                if (reserved_admission.expected_check_in_date and
                    reserved_admission.expected_check_in_date >= current_date):
                    admission = reserved_admission
                    break

        if admission and admission.maternity:
            return {
                "id": admission.maternity.id,
                "name": admission.maternity.name,   
                "phone": admission.maternity.phone,
                "age": calculate_age(admission.maternity.birth_date) if admission.maternity.birth_date else "-",
                "expected_check_in_date": admission.expected_check_in_date,
                "actual_check_in_date": admission.actual_check_in_date,
                "expected_check_out_date": admission.expected_check_out_date,
                "actual_check_out_date": admission.actual_check_out_date,
                "check_in_status": admission.get_check_in_status_display(),
            }

        return None


# 房间时间线序列化器（用于way='time'的情况）
class RoomOverviewTimeSerializer(serializers.ModelSerializer):
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    daily_status = serializers.SerializerMethodField()

    class Meta:
        model = Room
        exclude = ['reason_remark', 'expected_recovery_time']
        read_only_fields = ('created_at', 'updated_at')

    def __init__(self, *args, **kwargs):
        self.days = kwargs.pop('days', 7)
        super().__init__(*args, **kwargs)

    def get_daily_status(self, obj):
        # 获取未来n天的房间状态
        current_date = timezone.now().date()
        daily_statuses = []

        # 如果房间状态是不可用状态，未来n天都是这个状态
        if obj.room_status in [
            RoomStatusEnum.UNAVAILABLE_MAINTENANCE,
            RoomStatusEnum.UNAVAILABLE_CLEANING,
            RoomStatusEnum.UNAVAILABLE_OTHER
        ]:
            for i in range(self.days):
                check_date = current_date + timedelta(days=i)
                daily_statuses.append({
                    'date': check_date,
                    'status': obj.room_status,
                    'status_display': obj.get_room_status_display(),
                    'customer_info': None
                })
            return daily_statuses

        # 如果房间状态是可用，需要检查每一天的预约和入住情况
        for i in range(self.days):
            check_date = current_date + timedelta(days=i)
            day_status = self._get_day_status(obj, check_date)
            daily_statuses.append(day_status)

        return daily_statuses

    def _get_day_status(self, room, check_date):
        # 获取特定日期的房间状态
        checked_in_admission = room.maternity_admission.filter(
            check_in_status=CheckInStatusEnum.CHECKED_IN,
            actual_check_in_date__lte=check_date
        ).exclude(
            actual_check_out_date__lt=check_date
        ).first()

        if checked_in_admission:
            return {
                'date': check_date,
                'status': CheckInStatusEnum.CHECKED_IN,
                'status_display': CheckInStatusEnum.CHECKED_IN.label,
                'customer_info': self._get_customer_info(checked_in_admission)
            }

        # 检查是否有预约（使用预计入住和退房日期）
        reserved_admission = room.maternity_admission.filter(
            check_in_status=CheckInStatusEnum.RESERVED,
            expected_check_in_date__lte=check_date,
            expected_check_out_date__gt=check_date
        ).first()

        if reserved_admission:
            return {
                'date': check_date,
                'status': CheckInStatusEnum.RESERVED,
                'status_display': CheckInStatusEnum.RESERVED.label,
                'customer_info': self._get_customer_info(reserved_admission)
            }

        # 房间可用
        return {
            'date': check_date,
            'status': RoomStatusEnum.AVAILABLE,
            'status_display': RoomStatusEnum.AVAILABLE.label,
            'customer_info': None
        }

    def _get_customer_info(self, admission):
        # 获取客户信息
        if admission and admission.maternity:
            return {
                "id": admission.maternity.id,
                "name": admission.maternity.name,
                "phone": admission.maternity.phone,
                "age": calculate_age(admission.maternity.birth_date) if admission.maternity.birth_date else "-",
                "expected_check_in_date": admission.expected_check_in_date,
                "actual_check_in_date": admission.actual_check_in_date,
                "expected_check_out_date": admission.expected_check_out_date,
                "actual_check_out_date": admission.actual_check_out_date,
                "check_in_status": admission.get_check_in_status_display(),
            }
        return None