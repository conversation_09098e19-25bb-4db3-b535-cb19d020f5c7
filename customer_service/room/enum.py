from django.db import models


# 房间状态枚举
class RoomStatusEnum(models.TextChoices):
    # 可用
    AVAILABLE = "AVAILABLE", "可用"
    # 不可用（维修中）
    UNAVAILABLE_MAINTENANCE = "UNAVAILABLE_MAINTENANCE", "不可用（维修中）"
    # 不可用（待清洁）
    UNAVAILABLE_CLEANING = "UNAVAILABLE_CLEANING", "不可用（待清洁）"
    # 不可用（换房审批中）
    UNAVAILABLE_SWITCH_ROOM = "UNAVAILABLE_SWITCH_ROOM", "不可用（换房审批中）"
    # 不可用（其他原因）
    UNAVAILABLE_OTHER = "UNAVAILABLE_OTHER", "不可用（其他原因）"