from django.db import models
from django.utils import timezone

from core.enum import CheckInStatusEnum
from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from core.parse_time import parse_datetime_string
from customer_service.room.enum import RoomStatusEnum
from hospital.models import Hospital
from maternity_center.models import MaternityCenter
from user.models import Maternity


# 房间
class Room(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 房间号
    room_number = models.CharField(max_length=100, verbose_name="房间号")
    # 房间类型
    room_type = models.CharField(max_length=100, verbose_name="房间类型")
    # 面积
    area = models.FloatField(verbose_name="面积")
    # 朝向
    orientation = models.CharField(max_length=100, verbose_name="朝向")
    # 楼层
    floor = models.IntegerField(verbose_name="楼层")
    # 基础设施清单
    facility_list = models.JSONField(verbose_name="基础设施清单", default=list)
    # 描述
    description = models.TextField(verbose_name="描述", blank=True, default="")
    # 房间状态
    room_status = models.CharField(max_length=100, verbose_name="房间状态", choices=RoomStatusEnum.choices, default=RoomStatusEnum.AVAILABLE,blank=True)
    # 原因备注
    reason_remark = models.TextField(verbose_name="原因备注", blank=True, default="")
    # 预计恢复时间
    expected_recovery_time = models.DateTimeField(verbose_name="预计恢复时间", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)

    class Meta:
        verbose_name = "房间信息管理"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f'{self.room_number if self.room_number else '-'}'
    
    @staticmethod
    def get_room_by_rid(rid,maternity_center):
        try:
            return Room.objects.get(rid=rid,maternity_center=maternity_center)
        except Room.DoesNotExist:
            return None
    
    def update_status(self, new_status, reason="", expected_recovery_time=None):
        
        if new_status != RoomStatusEnum.AVAILABLE:
            expected_recovery_time = parse_datetime_string(expected_recovery_time)
            if not expected_recovery_time:
                return None
        
        # 记录状态变更历史记录
        RoomStatusHistory.objects.create(
            room=self,
            old_status=self.room_status,
            new_status=new_status,
            reason_remark=reason or '',
            expected_recovery_time=expected_recovery_time or None
        )
        
        # 更新状态
        self.room_status = new_status
        if reason is not None:
            self.reason_remark = reason
        if expected_recovery_time is not None:
            self.expected_recovery_time = expected_recovery_time
        self.save()
        return self
    
    def check_room_available(self,check_out_date,existing_admissions):
        
        new_start_date = timezone.now().date()
            
        if self.room_status != RoomStatusEnum.AVAILABLE:
            return False,f"当前{self.room_number}房间状态为{RoomStatusEnum(self.room_status).label}"

        # 检查日期重叠
        # 检查日期范围是否与现有记录重叠
        # existing_admissions = MaternityAdmission.objects.filter(
        #     room=self,
        #     check_in_status__in=[CheckInStatusEnum.RESERVED, CheckInStatusEnum.CHECKED_IN]
        # )

        for existing in existing_admissions:
            if existing.check_in_status == CheckInStatusEnum.CHECKED_IN:
                if existing.actual_check_in_date and existing.expected_check_out_date:
                    existing_start_date = existing.actual_check_in_date
                    existing_end_date = existing.expected_check_out_date
                else:
                    continue 
            else:
                if existing.expected_check_in_date and existing.expected_check_out_date:
                    existing_start_date = existing.expected_check_in_date
                    existing_end_date = existing.expected_check_out_date
                else:
                    continue 

            if new_start_date < existing_end_date and existing_start_date < check_out_date:
                existing_status_label = CheckInStatusEnum(existing.check_in_status).label
                existing_customer = existing.maternity.name if existing.maternity else "未知客户"
                error_msg = f"当前{self.room_number}房间在{new_start_date}到{check_out_date}期间与{existing_customer}的{existing_status_label}记录({existing_start_date}到{existing_end_date})存在时间冲突"
                return False, error_msg

        return True, None
        

# 房间状态历史
class RoomStatusHistory(BaseModel):
    # 关联房间
    room = models.ForeignKey(Room, on_delete=models.CASCADE, verbose_name="房间",related_name="room_status_history")
    # 旧状态
    old_status = models.CharField(max_length=100, verbose_name="旧状态", choices=RoomStatusEnum.choices)
    # 新状态
    new_status = models.CharField(max_length=100, verbose_name="新状态", choices=RoomStatusEnum.choices)
    # 原因备注
    reason_remark = models.TextField(verbose_name="原因备注", blank=True, null=True)
    # 预计恢复时间
    expected_recovery_time = models.DateTimeField(verbose_name="预计恢复时间", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)

    class Meta:
        verbose_name = "房间状态历史"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f'{self.room} - {self.old_status} -> {self.new_status}'
    
    
    
# 房间入住表
class RoomCheckIn(BaseModel):
    # 关联房间
    room = models.ForeignKey(Room, on_delete=models.CASCADE, verbose_name="房间",related_name="checkin_records")
    # 关联产妇
    maternity = models.ForeignKey(Maternity, on_delete=models.CASCADE, verbose_name="产妇",related_name="stay_records")
    # 产妇入住状态
    check_in_status = models.CharField(max_length=20, verbose_name="产妇入住状态", choices=CheckInStatusEnum.choices, default=CheckInStatusEnum.RESERVED)
    # 预计入住时间
    check_in_time = models.DateTimeField(verbose_name="预计入住时间")
    # 实际入住时间
    actual_check_in_time = models.DateTimeField(verbose_name="实际入住时间", blank=True, null=True)
    # 预计退房时间
    check_out_time = models.DateTimeField(verbose_name="预计退房时间")
    # 实际退房时间
    actual_check_out_time = models.DateTimeField(verbose_name="实际退房时间", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    
