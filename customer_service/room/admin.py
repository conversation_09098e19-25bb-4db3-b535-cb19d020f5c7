from django.contrib import admin

# Register your models here.
from .models import Room, RoomStatusHistory


# 注册模型
@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    list_display = ['id','room_number', 'room_type', 'area', 'orientation', 'facility_list', 'description', 'room_status', 'reason_remark', 'expected_recovery_time', 'maternity_center']

@admin.register(RoomStatusHistory)
class RoomStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ['room', 'old_status', 'new_status', 'reason_remark', 'expected_recovery_time']
