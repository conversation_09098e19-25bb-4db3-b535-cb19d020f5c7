from django.urls import path

from .views import RoomListView, RoomCreateView, RoomUpdateView, RoomDeleteView, RoomDetailView, RoomChangeStatusView, \
    RoomOverviewView, RoomTypeListView, RoomFloorListView

# 婴儿相关接口的URL配置
urlpatterns = [
    
    # 查看房态总览
    path('room/overview/<str:way>/', RoomOverviewView.as_view(), name='room_overview'),  # 查看房态总览
    
    # 房间列表
    path('room/list/', RoomListView.as_view(), name='room_list'),  # 获取入院记录列表
    # 房间创建
    path('room/create/', RoomCreateView.as_view(), name='room_create'),  # 创建入院记录
    # 房间详情
    path('room/detail/<str:rid>/', RoomDetailView.as_view(), name='room_detail'),  # 获取入院记录详情
    # 房间更新
    path('room/update/<str:rid>/', RoomUpdateView.as_view(), name='room_update'),  # 更新入院记录
    # 房间删除
    path('room/delete/<str:rid>/', RoomDeleteView.as_view(), name='room_delete'),  # 删除入院记录
    # 更改房间状态
    path('room/change-status/<str:rid>/', RoomChangeStatusView.as_view(), name='room_change_status'),  # 更改房间状态    
    
    
    # 获取房间类型列表接口
    path('room/type/list/', RoomTypeListView.as_view(), name='room_type_list'),  # 获取房间类型列表
    # 获取房间所有楼层接口
    path('room/floor/list/', RoomFloorListView.as_view(), name='room_floor_list'),  # 获取房间所有楼层
]