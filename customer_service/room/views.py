from collections import defaultdict
from datetime import datetime

from django.db.models import Q
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.config import FACILITY_LIST
from core.enum import ApprovalStatusEnum, CheckInStatusEnum
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.room.enum import RoomStatusEnum
from customer_service.room.models import Room
from customer_service.room.serializers import RoomCreateSerializer, RoomDetailSerializer, RoomSerializer, RoomUpdateSerializer, \
    RoomOverviewNormalSerializer, RoomOverviewTimeSerializer
from permissions.enum import PermissionEnum


# 查看房态总览
class RoomOverviewView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_STATUS_VIEW
    
    def get(self, request, way='normal'):
        
        sk = request.GET.get('sk', None)
        aps = request.GET.get('aps', None)
        room_type = request.GET.get('room_type', None)
        floor = request.GET.get('floor', None)
        check_in_date_start = self.request.query_params.get('check_in_date_start', None)
        check_in_date_end = self.request.query_params.get('check_in_date_end', None) 
        
        rooms = Room.objects.filter(maternity_center=request.user.maternity_center).prefetch_related(
            'maternity_admission__maternity'
        ).order_by('floor', 'room_number')
        
        if aps:
            if aps == "CHECKED_IN":
                rooms = rooms.filter(maternity_admission__check_in_status=CheckInStatusEnum.CHECKED_IN)
            elif aps == "RESERVED":
                rooms = rooms.filter(maternity_admission__check_in_status=CheckInStatusEnum.RESERVED)
            elif aps in RoomStatusEnum.values:
                rooms = rooms.filter(room_status=aps)
            else:
                return make_response(code=-1, msg="房间状态不正确")
            
        if room_type:
            rooms = rooms.filter(room_type__icontains=room_type)
            
        if floor:
            try:
                floor_int = int(floor)
                rooms = rooms.filter(floor=floor_int)
            except ValueError:
                return make_response(code=-1, msg="楼层参数格式错误")
        
        if sk:
            print(sk)
            rooms = rooms.filter(
                Q(maternity_admission__maternity__name__icontains=sk,
                  maternity_admission__check_in_status__in=[CheckInStatusEnum.RESERVED, CheckInStatusEnum.CHECKED_IN]) |
                Q(room_number__icontains=sk)
            )
            
        if check_in_date_start and check_in_date_end:
            try:
                start_date = datetime.strptime(check_in_date_start, "%Y-%m-%d").date()
                end_date = datetime.strptime(check_in_date_end, "%Y-%m-%d").date()
                rooms = rooms.filter(
                    Q(maternity_admission__actual_check_in_date__gte=start_date,
                      maternity_admission__actual_check_in_date__lte=end_date) |
                    Q(maternity_admission__expected_check_in_date__gte=start_date,
                      maternity_admission__expected_check_in_date__lte=end_date)
                )
            except ValueError:
                return make_response(code=-1, msg="入住日期格式错误，请使用YYYY-MM-DD格式")
        
        if way == 'normal':
            grouped_data = self._group_rooms_by_floor(rooms, 'normal')
            return make_response(code=0, msg="获取房态总览成功", data=grouped_data)
        
        elif way == 'time':
            try:
                days = int(request.GET.get('days', 30))
                if days <= 0 or days > 365: 
                    return make_response(code=-1, msg="天数参数必须在1-365之间")
            except (ValueError, TypeError):
                return make_response(code=-1, msg="天数参数格式错误")

            grouped_data = self._group_rooms_by_floor(rooms, 'time', days=days)
            
            return make_response(code=0, msg="获取房态总览成功", data=grouped_data)
        else:
            return make_response(code=-1, msg="无效的参数")
        
    # 按楼层分组房间数据
    def _group_rooms_by_floor(self, rooms, mode, days=None):

        floor_groups = defaultdict(list)

        # 按楼层分组
        for room in rooms:
            floor_groups[room.floor].append(room)

        # 序列化每个楼层的房间数据
        result = []
        for floor in sorted(floor_groups.keys()):
            floor_rooms = floor_groups[floor]

            if mode == 'normal':
                serializer = RoomOverviewNormalSerializer(floor_rooms, many=True)
            elif mode == 'time':
                serializer = RoomOverviewTimeSerializer(floor_rooms, many=True, days=days)

            result.append({
                'floor': floor,
                'floor_name': f"{floor}楼",
                'room_count': len(floor_rooms),
                'rooms': serializer.data
            })

        return result

    
    

class RoomListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_VIEW
    serializer_class = RoomSerializer
    response_msg = "获取房间列表成功"
    error_response_msg = "获取房间列表失败"
    search_fields = ["room_number"]
    
    def get_queryset(self):

        aps = self.request.query_params.get('aps', None)  # 房间状态
        floor = self.request.query_params.get('floor', None)  # 楼层
        room_type = self.request.query_params.get('room_type', None)  # 房间类型
        base_queryset = Room.objects.filter(maternity_center=self.request.user.maternity_center).order_by('floor','room_number')
                    
        if aps:
            if aps == "CHECKED_IN":
                # 筛选已入住：查找有已入住记录的房间
                base_queryset = base_queryset.filter(maternity_admission__check_in_status=CheckInStatusEnum.CHECKED_IN)
            elif aps == "RESERVED":
                # 筛选已预约：查找有预约记录的房间
                base_queryset = base_queryset.filter(maternity_admission__check_in_status=CheckInStatusEnum.RESERVED)
            elif aps in RoomStatusEnum.values:
                # 其他房间状态（可用、不可用等）：直接按房间状态筛选
                base_queryset = base_queryset.filter(room_status=aps)
            else:
                self.error_response_msg = "房间状态不正确"
                return None

        if floor:
            try:
                floor_int = int(floor)
                base_queryset = base_queryset.filter(floor=floor_int)
            except ValueError:
                self.error_response_msg = "楼层参数格式错误"
                return None

        if room_type:
            base_queryset = base_queryset.filter(room_type=room_type)
        
        return base_queryset




# 新建房间
class RoomCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_EDIT
    
    def post(self, request):
        
        room_status = request.data.get('room_status')
        if room_status != "AVAILABLE":
            return make_response(code=-1, msg="新建房间状态必须为可用")
        
        if Room.objects.filter(room_number=request.data['room_number'],maternity_center=request.user.maternity_center).exists():
            return make_response(code=-1, msg=f"房间号 {request.data['room_number']} 已存在")
        
        if not isinstance(request.data['facility_list'], list):
            return make_response(code=-1, msg="设施清单必须为列表")
        for facility in request.data['facility_list']:
            if facility not in FACILITY_LIST:
                return make_response(code=-1, msg=f"{facility}不在设施清单中")
        
        request.data['maternity_center'] = request.user.maternity_center.id
        serializer = RoomCreateSerializer(data=request.data,context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="房间创建成功", data=RoomSerializer(serializer.instance).data)
        print(serializer.errors)
        return make_response(code=-1, msg=serializer.errors)
    
# 房间详情
class RoomDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_VIEW
    
    def get(self, request,rid):
        try:
            room = Room.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except Room.DoesNotExist:
            return make_response(code=-1, msg="房间不存在")
        serializer = RoomDetailSerializer(room)
        return make_response(code=0, msg="房间详情", data=serializer.data)

# 更新房间
class RoomUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_EDIT
    
    def put(self, request, rid):
        try:
            
            data = request.data.copy()
            rn = data.get('room_number',None)
            room = Room.objects.get(rid=rid, maternity_center=request.user.maternity_center)
        except Room.DoesNotExist:
            return make_response(code=-1, msg="房间不存在")
        
        if rn and rn != room.room_number:
            if Room.objects.filter(room_number=rn,maternity_center=request.user.maternity_center).exists():
                return make_response(code=-1, msg="房间号已存在")
        
        if not isinstance(data['facility_list'], list):
            return make_response(code=-1, msg="设施清单必须为列表")
        for facility in data['facility_list']:
            if facility not in FACILITY_LIST:
                return make_response(code=-1, msg=f"{facility}不在设施清单中")
        
        
        # 使用专门的基本信息序列化器，只更新允许的字段
        serializer = RoomUpdateSerializer(room, data=data, partial=True,context={'request': request})
        if serializer.is_valid():
            serializer.save()
            # 使用详情序列化器返回完整信息
            detail_serializer = RoomDetailSerializer(room)
            return make_response(code=0, msg="房间信息更新成功", data=detail_serializer.data)
        
        return make_response(code=-1, msg=serializer.errors)

# 删除房间
class RoomDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_EDIT
    
    def delete(self, request,rid):
        try:
            room = Room.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            room.delete()
            return make_response(code=0, msg="房间删除成功")
        except Room.DoesNotExist:
            return make_response(code=-1, msg="房间不存在")
        

# 更改房间状态
class RoomChangeStatusView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_EDIT
    
    def put(self, request,rid):
        
        status = request.data.get('room_status')
        reason_remark = request.data.get('reason_remark')
        expected_recovery_time = request.data.get('expected_recovery_time')
        if status not in RoomStatusEnum.values:
            return make_response(code=-1, msg="无效的状态值")
        
        try:
            room = Room.objects.get(rid=rid,maternity_center=request.user.maternity_center)
                        
            if room.maternity_admission.filter(check_in_status=CheckInStatusEnum.CHECKED_IN).exists():
                return make_response(code=-1, msg="房间有产妇入住，不能更改状态")
            
            if room.room_change_applications.filter(approval_status=ApprovalStatusEnum.PENDING).exists():
                return make_response(code=-1, msg="房间有待审核的换房申请，不能更改状态")
            
            # if  room.room_status != RoomStatusEnum.AVAILABLE:
            #     return make_response(code=-1, msg=f"房间状态为不可用，无法更改状态，当前状态为：{RoomStatusEnum(room.room_status).label}")
            
            result  = room.update_status(status,reason_remark,expected_recovery_time)
            
            if not result:
                return make_response(code=-1, msg="预计恢复时间格式错误")
            
            detail_serializer = RoomDetailSerializer(room)
            return make_response(code=0, msg="房间状态更改成功", data=detail_serializer.data)
        except Room.DoesNotExist:
            return make_response(code=-1, msg="房间不存在")
        
        

# 房间类型列表
class RoomTypeListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_VIEW
    
    def get(self, request):
        room_types = Room.objects.filter(maternity_center=request.user.maternity_center).values_list('room_type', flat=True).distinct()
        return make_response(code=0, msg="获取房间类型列表成功", data=list(room_types))
    
# 房间楼层列表
class RoomFloorListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_VIEW
    
    def get(self, request):
        room_floors = Room.objects.filter(maternity_center=request.user.maternity_center).values_list('floor', flat=True).distinct().order_by('floor')
        return make_response(code=0, msg="获取房间楼层列表成功", data=list(room_floors))