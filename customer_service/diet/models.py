from django.db import models

from core.generate_hashid import generate_maternity_daily_diet_record_code, generate_resource_uuid
from core.model import BaseModel, PositiveFloatField
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from hospital.models import Hospital
from maternity_center.models import MaternityCenter
from user.models import Maternity, Staff


class DishManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_active=True)



# 菜品
class Dish(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", null=True, blank=True)
    # 菜品名称
    name = models.CharField(max_length=50, verbose_name="菜名")
    # 菜品描述
    description = models.TextField(verbose_name="菜品描述", blank=True,default="")
    # 卡路里
    calorie = PositiveFloatField(verbose_name="卡路里", null=True, blank=True)
    # 蛋白质
    protein = PositiveFloatField(verbose_name="蛋白质(g)", null=True, blank=True)
    # 碳水化合物
    carbohydrate = PositiveFloatField(verbose_name="碳水化合物(g)", null=True, blank=True)
    # 脂肪
    fat = PositiveFloatField(verbose_name="脂肪(g)", null=True, blank=True)
    # 是否上架
    is_active = models.BooleanField(default=True, verbose_name="是否上架",blank=True)
    # 资源id
    rid = models.CharField(verbose_name="资源id", max_length=100, unique=True,blank=True,default=generate_resource_uuid)

    def __str__(self):
        return self.name
    class Meta:
        verbose_name = "菜品"
        verbose_name_plural = "菜品"
        
    objects = DishManager()
    all_objects = models.Manager()
    
    # 查看是否有名字重名菜品
    @classmethod
    def check_name_duplicate(cls, name, maternity_center, exclude_id=None):
        if exclude_id:
            return cls.objects.filter(name=name, maternity_center=maternity_center).exclude(id=exclude_id).exists()
        return cls.objects.filter(name=name, maternity_center=maternity_center).exists()





# 产妇膳食记录表
class MaternityDietRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",null=True, blank=True)
    # 产妇入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="产妇入院记录",related_name="diet_records")
    # 日期
    date = models.DateField(verbose_name="日期")
    # 一天所有餐食的安排 (JSON格式)
    daily_meals = models.JSONField(verbose_name="一天的餐食安排", default=dict, blank=True)
    # 整体备注
    daily_remark = models.TextField(verbose_name="当日膳食备注", null=True, blank=True)
    # 记录id
    rid = models.CharField(verbose_name="记录id", max_length=100, unique=True,blank=True,default=generate_maternity_daily_diet_record_code)

    class Meta:
        verbose_name = "产妇膳食记录表"
        verbose_name_plural = "产妇膳食记录表"

    def __str__(self):
        return f'{self.maternity_admission.maternity.name} - {self.date}'

    @classmethod
    def check_date_duplicate(cls, maternity_center,maternity_admission, date,):
        return cls.objects.filter(maternity_center=maternity_center,maternity_admission=maternity_admission,date=date).exists()
