from customer_service.diet.enum import MealTypeEnum
from customer_service.diet.models import Dish

    
    
def validate_daily_meals(daily_meals,maternity_center):
    try:
        if not isinstance(daily_meals, dict):
            return "数据校验失败"
        
        provided_meal_types = set(daily_meals.keys())
        for meal_type in provided_meal_types:
            if meal_type not in MealTypeEnum.values:
                return "数据校验失败"
        
        all_dish_rids = []
        for meal_type, meal_data in daily_meals.items():
            if not isinstance(meal_data, dict):
                return "数据校验失败"
            if 'dishes' not in meal_data:
                return "数据校验失败"
            if not isinstance(meal_data['dishes'], list):
                return "数据校验失败"
            
            dish_rids = [dish_rid for dish_rid in meal_data['dishes']]
            all_dish_rids.extend(dish_rids)
        
        if all_dish_rids:
            unique_dish_rids = list(set(all_dish_rids))
            
            existing_dish_rids = set(
                Dish.objects.filter(
                    maternity_center=maternity_center,
                    rid__in=unique_dish_rids
                ).values_list('rid', flat=True)
            )
            
            missing_dish_rids = set(unique_dish_rids) - existing_dish_rids
            if missing_dish_rids:
                print("missing_dish_rids",missing_dish_rids)
                return "数据校验失败"
        
        return None
    except Exception as e:
        print("数据校验失败",e)
        return "数据校验失败，请稍后再试！"