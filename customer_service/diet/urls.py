from django.urls import path

from .views import *

urlpatterns = [
    # 菜品列表
    path('dishes/list/', DishListAPIView.as_view(), name='dish-list'),
    # # 菜品详情
    path('dishes/detail/<str:rid>/', DishDetailAPIView.as_view(), name='dish-detail'),
    # # 菜品创建
    path('dishes/create/', DishCreateView.as_view(), name='dish-create'),
    # 菜品更新
    path('dishes/update/<str:rid>/', DishUpdateView.as_view(), name='dish-update'),
    # # 菜品删除
    path('dishes/delete/<str:rid>/', DishDeleteView.as_view(), name='dish-delete'),
    
    # 膳食记录列表
    path('diet-records/list/', DietRecordListAPIView.as_view(), name='maternity-diet-record-list'),
    # 获取制定产妇膳食列表
    path('maternity-diet-records/list/<str:aid>/', MaternityDietRecordListAPIView.as_view(), name='maternity-diet-record-list-by-maternity-admission'),
    # 产妇膳食记录详情
    path('maternity-diet-records/detail/<str:rid>/', MaternityDietRecordDetailAPIView.as_view(), name='maternity-diet-record-detail'),
    # 产妇膳食记录创建
    path('maternity-diet-records/create/<str:aid>/', MaternityDietRecordCreateView.as_view(), name='maternity-diet-record-create'),
    # 产妇膳食记录更新
    path('maternity-diet-records/update/<str:rid>/', MaternityDietRecordUpdateView.as_view(), name='maternity-diet-record-update'),
    # 产妇膳食记录删除
    path('maternity-diet-records/delete/<str:rid>/', MaternityDietRecordDeleteView.as_view(), name='maternity-diet-record-delete'),
] 