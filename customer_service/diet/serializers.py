from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from .models import (
    Dish, MaternityDietRecord
)


# 菜品列表序列化器
class DishListSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = Dish
        exclude = ['maternity_center','is_active','id']
        


# 菜品详情序列化器
class DishDetailSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = Dish
        exclude = ['maternity_center','id']
        

# 菜品列表简化序列化器
class DishListSimpleSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Dish
        fields = ['rid','name','description','calorie','protein','carbohydrate','fat']



# 菜品创建序列化器
class DishCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Dish
        fields = ['maternity_center', 'name', 'description', 'calorie', 'protein', 'carbohydrate', 'fat']
        

# 菜品更新序列化器
class DishUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Dish
        fields = ['name', 'description', 'calorie', 'protein', 'carbohydrate', 'fat']


# 膳食记录列表序列化器
class DietRecordListSerializer(serializers.ModelSerializer):
    
    # 产妇名字
    maternity_name = serializers.SerializerMethodField()
    # 入院记录 id
    admission_id = serializers.SerializerMethodField()
    # 产妇入住日期
    check_in_date = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityDietRecord
        fields = ['rid','maternity_name','admission_id','check_in_date','date']
       
    def get_maternity_name(self, obj):
        return obj.maternity_admission.maternity.name
    
    def get_admission_id(self, obj):
        return obj.maternity_admission.id
    
    def get_check_in_date(self, obj):
        return obj.maternity_admission.actual_check_in_date if obj.maternity_admission.actual_check_in_date else '-'



# 产妇膳食记录列表序列化器
class MaternityDietRecordListSerializer(serializers.ModelSerializer):
    
    # 膳食详情
    daily_meals_detail = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityDietRecord
        fields = ['rid','date','daily_meals_detail','daily_remark']
    
    
    def get_daily_meals_detail(self, obj):
        if not obj.daily_meals:
            return {}
        
        all_dish_rids = []
        for meal_data in obj.daily_meals.values():
            if isinstance(meal_data, dict) and 'dishes' in meal_data:
                all_dish_rids.extend(meal_data['dishes'])
        
        dishes_dict = {}
        if all_dish_rids:
            dishes = Dish.objects.filter(rid__in=all_dish_rids)
            dishes_dict = {dish.rid: DishListSimpleSerializer(dish).data for dish in dishes}
        
        detailed_meals = {}
        for meal_type, meal_data in obj.daily_meals.items():
            if isinstance(meal_data, dict):
                detailed_meals[meal_type] = {
                    'dishes': [dishes_dict.get(dish_rid, {'rid': dish_rid, 'name': 'N/A'}) 
                              for dish_rid in meal_data.get('dishes', [])],
                    'remark': meal_data.get('remark', '')
                }
        
        return detailed_meals
        
    

# 产妇膳食记录详情序列化器
class MaternityDietRecordDetailSerializer(serializers.ModelSerializer):
    daily_meals_detail = serializers.SerializerMethodField()
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    
    class Meta:
        model = MaternityDietRecord
        exclude = ['maternity_center','maternity_admission','daily_meals','id']
    
    def get_daily_meals_detail(self, obj):
        if not obj.daily_meals:
            return {}
        
        all_dish_rids = []
        for meal_data in obj.daily_meals.values():
            if isinstance(meal_data, dict) and 'dishes' in meal_data:
                all_dish_rids.extend(meal_data['dishes'])
        
        dishes_dict = {}
        if all_dish_rids:
            dishes = Dish.objects.filter(rid__in=all_dish_rids)
            dishes_dict = {dish.rid: DishListSimpleSerializer(dish).data for dish in dishes}
        
        detailed_meals = {}
        for meal_type, meal_data in obj.daily_meals.items():
            if isinstance(meal_data, dict):
                detailed_meals[meal_type] = {
                    'dishes': [dishes_dict.get(dish_rid, {'rid': dish_rid, 'name': 'N/A'}) 
                              for dish_rid in meal_data.get('dishes', [])],
                    'remark': meal_data.get('remark', '')
                }
        
        return detailed_meals
        
        
        
    
# 产妇膳食创建序列化器
class MaternityDietRecordCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaternityDietRecord
        fields = ['maternity_center', 'maternity_admission', 'date', 'daily_meals', 'daily_remark']
        
        
