from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import CheckInStatusEnum
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.diet.serializers import (
    DietRecordListSerializer, DishCreateSerializer, DishDetailSerializer, DishListSerializer, DishUpdateSerializer,
    MaternityDietRecordDetailSerializer, MaternityDietRecordListSerializer,
)
from permissions.enum import PermissionEnum
from user.serializers import MaternityDesensitizedSerializer
from .models import Dish, MaternityDietRecord
from .utils import validate_daily_meals


# 菜品列表
class DishListAPIView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    serializer_class = DishListSerializer
    response_msg = "获取菜品列表成功"
    error_response_msg = "获取菜品列表失败"
    search_fields = ['name', 'description', 'calorie','protein','carbohydrate','fat']
    
    
    def get_queryset(self):

        return Dish.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
        
# 菜品详情
class DishDetailAPIView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW

    def get(self, request, rid  ):
        try:
            dish = Dish.objects.get(maternity_center=request.user.maternity_center, rid=rid)
        except Dish.DoesNotExist:
            return make_response(code=1,msg="菜品不存在")
        
        serializer = DishDetailSerializer(dish)
        return make_response(code=0,msg="获取菜品详情成功",data=serializer.data)
    
# 菜品创建
class DishCreateView(APIView):
    
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    
    def post(self, request):
        
        name = request.data.get('name')
        
        if not name:
            return make_response(code=1,msg="菜品名称不能为空")
        
        if Dish.check_name_duplicate(name, request.user.maternity_center):
            return make_response(code=1,msg=f"菜品名称（{name}）已存在")
        
        data = request.data.copy()
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = DishCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg="创建菜品成功",data=DishDetailSerializer(serializer.instance).data)
        return make_response(code=1,msg="数据校验失败")

# 菜品更新
class DishUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    
    def put(self, request, rid):
        
        name = request.data.get('name')
        if not name:
            return make_response(code=1,msg="菜品名称不能为空")
        
        
        try:
            dish = Dish.objects.get(maternity_center=request.user.maternity_center, rid=rid)
        except Dish.DoesNotExist:
            return make_response(code=1,msg="菜品不存在")
        
        if Dish.check_name_duplicate(name=name,maternity_center=request.user.maternity_center,exclude_id=dish.id):
            return make_response(code=1,msg=f"菜品名称（{name}）已存在")
        
        serializer = DishUpdateSerializer(dish, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg="更新菜品成功",data=DishDetailSerializer(serializer.instance).data)
        return make_response(code=1,msg="数据校验失败")
    
# 菜品删除
class DishDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    
    def delete(self, request, rid):
        try:
            dish = Dish.objects.get(maternity_center=request.user.maternity_center, rid=rid)
        except Dish.DoesNotExist:
            return make_response(code=1,msg="菜品不存在")
        
        dish.delete()
        return make_response(code=0,msg=f"删除菜品（{dish.name}）成功")
    
    


# 膳食记录列表
class DietRecordListAPIView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    serializer_class = DietRecordListSerializer
    response_msg = "获取膳食记录列表成功"
    error_response_msg = "获取膳食记录列表失败"
    
    def get_queryset(self):
        return MaternityDietRecord.objects.filter(maternity_center=self.request.user.maternity_center,).order_by('-created_at')
    

# 获取指定产妇膳食列表
class MaternityDietRecordListAPIView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    
    def get(self, request, aid):
        
        try:
            maternity_admission = MaternityAdmission.objects.get(maternity_center=request.user.maternity_center,aid=aid)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=1,msg="产妇入院记录不存在")
        
        try:
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
        except ValueError:
            page = 1
            page_size = 20
        
        
        queryset = MaternityDietRecord.objects.filter(maternity_center=request.user.maternity_center,maternity_admission=maternity_admission).order_by('-date')

        total_count = queryset.count()
        total_page = (total_count + page_size - 1) // page_size
        start = (page - 1) * page_size
        end = start + page_size
        
        # 获取分页数据
        paginated_data = queryset[start:end]
        
        serializer = MaternityDietRecordListSerializer(paginated_data,many=True)
        
        maternity_info = MaternityDesensitizedSerializer(maternity_admission.maternity).data if maternity_admission and maternity_admission.maternity else None

        
        result = {
            'content': {
                'maternity_info': maternity_info,
                'diet_records': serializer.data
            },
            'page': page,
            'page_size': page_size,
            'total_count': total_count,
            'total_page': total_page
        }
        
        return make_response(code=0,msg="获取产妇膳食记录列表成功",data=result)



# 产妇膳食记录详情
class MaternityDietRecordDetailAPIView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    
    def get(self, request, rid):
        try:
            record = MaternityDietRecord.objects.get(maternity_center=request.user.maternity_center, rid=rid)
        except MaternityDietRecord.DoesNotExist:
            return make_response(code=1,msg="产妇膳食记录不存在")
        
        serializer = MaternityDietRecordDetailSerializer(record)
        return make_response(code=0,msg="获取产妇膳食记录详情成功",data=serializer.data)
    

# 产妇膳食记录创建
class MaternityDietRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    
    def post(self, request, aid):
        
        date = request.data.get('date')
        daily_meals = request.data.get('daily_meals')
        daily_remark = request.data.get('daily_remark')
        
        if not date:
            return make_response(code=1,msg="日期不能为空")
        
        try:
            maternity_admission = MaternityAdmission.objects.get(maternity_center=request.user.maternity_center,aid=aid)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=1,msg="产妇入院记录不存在")
        
        if maternity_admission.check_in_status != CheckInStatusEnum.CHECKED_IN:
            return make_response(code=1,msg="产妇未入住，无法创建膳食记录")
        
        
        if MaternityDietRecord.check_date_duplicate(request.user.maternity_center,maternity_admission,date):
            return make_response(code=1,msg=f"{maternity_admission.maternity.name}（{date}）膳食记录已存在")
        
        check_result = validate_daily_meals(daily_meals,request.user.maternity_center)
        if check_result:
            return make_response(code=1,msg=check_result)
        
        record = MaternityDietRecord.objects.create(
            maternity_center=request.user.maternity_center,
            maternity_admission=maternity_admission,
            date=date,
            daily_meals=daily_meals,
            daily_remark=daily_remark
        )
        return make_response(code=0,msg="创建产妇膳食记录成功",data=MaternityDietRecordDetailSerializer(record).data)
    

# 产妇膳食记录更新
class MaternityDietRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    
    
    def put(self, request, rid):
        
        daily_meals = request.data.get('daily_meals')
        daily_remark = request.data.get('daily_remark','')
        
        if not daily_meals:
            return make_response(code=1,msg="菜谱不能为空")
        
        try:
            record = MaternityDietRecord.objects.get(maternity_center=request.user.maternity_center, rid=rid)
        except MaternityDietRecord.DoesNotExist:
            return make_response(code=1,msg="产妇膳食记录不存在")
        
        check_result = validate_daily_meals(daily_meals,request.user.maternity_center)
        if check_result:
            return make_response(code=1,msg=check_result)
        
        record.daily_meals = daily_meals
        record.daily_remark = daily_remark
        record.save()
        
        return make_response(code=0,msg="更新产妇膳食记录成功",data=MaternityDietRecordDetailSerializer(record).data)
    
    
# 产妇膳食记录删除
class MaternityDietRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DIET_VIEW
    
    def delete(self, request, rid):
        try:
            record = MaternityDietRecord.objects.get(maternity_center=request.user.maternity_center, rid=rid)
        except MaternityDietRecord.DoesNotExist:
            return make_response(code=1,msg="产妇膳食记录不存在")
        
        record.delete()
        return make_response(code=0,msg="删除产妇膳食记录成功")
    
    
    