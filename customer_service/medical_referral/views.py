from datetime import datetime

from rest_framework.views import APIView

from core.authorization import Care<PERSON><PERSON><PERSON><PERSON><PERSON>ication, StaffWithSpecificPermissionOnly
from core.enum import ApprovalStatusEnum
from core.logs import AuditLogCreator
from core.model import get_field_changes
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.baby import Newborn
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.medical_referral.enum import EmergencyLevelEnum, ReferralStatusEnum
from customer_service.medical_referral.models import MaternityReferralApplication, NewbornReferralApplication
from customer_service.medical_referral.serializers import MedicalReferralCreateSerializer, \
    MedicalReferralDetailSerializer, MedicalReferralSerializer, MedicalReferralUpdateSerializer, \
    NewbornMedicalReferralCreateSerializer, NewbornMedicalReferralDetailSerializer, NewbornMedicalReferralSerializer, \
    NewbornMedicalReferralUpdateSerializer
from file.models import Medical<PERSON><PERSON>erralFile, MedicalReferralReturnFile
from permissions.enum import PermissionEnum


def get_maternity_referral_str(maternity_referral):
    return f"[{maternity_referral.maternity_admission.maternity.name}({maternity_referral.maternity_admission.room.room_number if maternity_referral.maternity_admission.room else '-'})] "


def get_newborn_referral_str(newborn_referral):
    return f"[{newborn_referral.baby.name} - {newborn_referral.baby.maternity_admission.maternity.name}({newborn_referral.baby.maternity_admission.room.room_number if newborn_referral.baby.maternity_admission.room else '-'})]"


# 产妇院内转诊列表
class MaternityMedicalReferralListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_VIEW
    serializer_class = MedicalReferralSerializer
    response_msg = "获取院内转诊列表成功"
    error_response_msg = ""
    search_fields = ["maternity_admission__maternity__name","maternity_admission__room__room_number"]
    
    def get_queryset(self):
        
        aps = self.request.query_params.get('aps', None)
        apt = self.request.query_params.get('apt', None)
        rd = self.request.query_params.get('rd', None)
        
        base_queryset = MaternityReferralApplication.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-application_time')
        
        if aps:
            if aps not in ReferralStatusEnum.values:
                self.error_response_msg = "转诊状态不正确"
                return None
            base_queryset = base_queryset.filter(referral_status=aps)
        
        if apt:
            try:
                apt_datetime = datetime.strptime(apt, "%Y-%m-%d %H:%M:%S")
                base_queryset = base_queryset.filter(application_time__gte=apt_datetime)
            except ValueError:
                self.error_response_msg = "查询日期格式错误"
                return None
        if rd:
            base_queryset = base_queryset.filter(referral_department__icontains=rd)
        
        AuditLogCreator.create_query_audit_log(self.request,"院内转诊","查看了<产妇院内转诊>申请单列表")

        return base_queryset
    
    
# 产妇院内转诊详情
class MaternityMedicalReferralDetailView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_VIEW
    
    def get(self, request, rid):
        try:
            medical_referral = MaternityReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            
            serializer = MedicalReferralDetailSerializer(medical_referral)
            
            AuditLogCreator.create_query_audit_log(self.request,"院内转诊",f"查看了 {get_maternity_referral_str(medical_referral)} <产妇院内转诊> 申请单")
            
            return make_response(code=0, msg="获取院内转诊详情成功", data=serializer.data)
        except MaternityReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
    
# 产妇院内转诊创建
class MaternityMedicalReferralCreateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def post(self, request,aid):
        
        emergency_level = request.data.get("emergency_level", None)
        
        if emergency_level not in EmergencyLevelEnum.values:
            return make_response(code=-1, msg="数据校验失败，紧急程度不正确")
        
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=aid,maternity_center=request.user.maternity_center)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="产妇入院记录不存在")
        
        if MaternityReferralApplication.has_pending_referral(maternity_admission):
            return make_response(code=-1, msg="产妇有未处理的转诊申请，暂无法创建新的转诊申请")
        
        request.data["maternity_center"] = request.user.maternity_center.id
        request.data["maternity_admission"] = maternity_admission.id
        
        serializer = MedicalReferralCreateSerializer(data=request.data,context={'request': request})
        if serializer.is_valid():
            serializer.save()
            resData = MedicalReferralDetailSerializer(serializer.instance).data
            
            AuditLogCreator.create_create_audit_log(self.request,"院内转诊",f"创建了 {get_maternity_referral_str(serializer.instance)} <产妇院内转诊> 申请单")
            
            return make_response(code=0, msg="创建院内转诊申请成功", data=resData)
        else:
            return make_response(code=-1, msg=serializer.errors)
        
        
# 产妇院内转诊更新
class MaternityMedicalReferralUpdateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        try:
            medical_referral = MaternityReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except MaternityReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="转诊申请单已审核，无法修改")
        
        serializer = MedicalReferralUpdateSerializer(medical_referral, data=request.data,context={'request': request})
        if serializer.is_valid():
            
            cfs = get_field_changes(medical_referral,serializer.validated_data)
            
            serializer.save()
            
            resData = MedicalReferralDetailSerializer(serializer.instance).data
            
            AuditLogCreator.create_update_audit_log(self.request,"院内转诊",f"更新了 {get_maternity_referral_str(serializer.instance)} <产妇院内转诊> 申请单",cfs)
            
            return make_response(code=0, msg="更新院内转诊申请成功", data=resData)
        else:
            return make_response(code=-1, msg=serializer.errors)
        

# 产妇院内转诊删除
class MaternityMedicalReferralDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def delete(self, request, rid):
        
        try:
            medical_referral = MaternityReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except MaternityReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="转诊申请单已审核，无法删除，审核结果为：%s" % ApprovalStatusEnum(medical_referral.approval_status).label)
        
        medical_referral.delete()
        
        AuditLogCreator.create_delete_audit_log(self.request,"院内转诊",f"删除了 {get_maternity_referral_str(medical_referral)} <产妇院内转诊> 申请单")
        
        return make_response(code=0, msg="删除院内转诊申请成功")


# 产妇院内转诊审核
class MaternityMedicalReferralAuditView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        require_fields = ["result","opinion"]
        for field in require_fields:
            if field not in request.data:
                return make_response(code=-1, msg=f"数据校验失败，提供的数据不完整")
        
        result = request.data.get("result", False)
        opinion = request.data.get("opinion", "")
        
        if result not in [True, False]:
            return make_response(code=-1, msg="数据校验失败，审核结果不正确")
        
        try:
            medical_referral = MaternityReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except MaternityReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="转诊申请单已审核，无法再次审核，审核结果为：%s" % ApprovalStatusEnum(medical_referral.approval_status).label)
        
        if result:
            res = medical_referral.approve_referral(request.user,opinion)
        else:
            res = medical_referral.reject_referral(request.user,opinion)

        resData = MedicalReferralDetailSerializer(res).data
        
        AuditLogCreator.create_request_audit_log(self.request,"院内转诊",f"审核了 {get_maternity_referral_str(medical_referral)} <产妇院内转诊> 申请单,审核结果为：{ApprovalStatusEnum(res.approval_status).label}")
        
        return make_response(code=0, msg="院内转诊申请审核成功", data=resData)


# 产妇院内转诊标记转诊中
class MaternityMedicalReferralMarkReferralIngView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        try:
            medical_referral = MaternityReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except MaternityReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.referral_status != ReferralStatusEnum.PENDING_REFERRAL:
            return make_response(code=-1, msg="转诊状态不正确，当前状态为：%s" % ReferralStatusEnum(medical_referral.referral_status).label)
        
        medical_referral.referral_status = ReferralStatusEnum.REFERRALING
        medical_referral.save()
        
        AuditLogCreator.create_status_change_audit_log(self.request,"院内转诊",f"标记了 {get_maternity_referral_str(medical_referral)} <产妇院内转诊> 申请单为转诊中")
        
        return make_response(code=0, msg="标记院内转诊为转诊中成功")
    
    
# 产妇院内转诊标记已转诊
class MaternityMedicalReferralMarkReferralCompletedView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        try:
            medical_referral = MaternityReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except MaternityReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.referral_status != ReferralStatusEnum.REFERRALING:
            return make_response(code=-1, msg="转诊状态不正确，当前状态为：%s" % ReferralStatusEnum(medical_referral.referral_status).label)
        
        medical_referral.referral_status = ReferralStatusEnum.REFERRALLED
        medical_referral.save()
        
        AuditLogCreator.create_status_change_audit_log(self.request,"院内转诊",f"标记了 {get_maternity_referral_str(medical_referral)} <产妇院内转诊> 申请单为已转诊")
        
        return make_response(code=0, msg="标记院内转诊为已转诊成功")


# 产妇院内转诊标记已返回
class MaternityMedicalReferralMarkReferralReturnedView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        rrd = request.data.get("rrd", None)
        
        if not isinstance(rrd, list):
            return make_response(code=-1, msg="数据校验失败，转诊返回出院小结错误")
        
        if not MedicalReferralReturnFile.check_rids(rrd,request.user.maternity_center):
            return make_response(code=-1, msg="数据校验失败，转诊返回出院小结错误")
        
        try:
            medical_referral = MaternityReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except MaternityReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.referral_status != ReferralStatusEnum.REFERRALLED:
            return make_response(code=-1, msg="转诊状态不正确，当前状态为：%s" % ReferralStatusEnum(medical_referral.referral_status).label)
        
        medical_referral.referral_return_discharge = rrd
        medical_referral.referral_status = ReferralStatusEnum.RETURNED
        medical_referral.save()
        
        AuditLogCreator.create_status_change_audit_log(self.request,"院内转诊",f"标记了 {get_maternity_referral_str(medical_referral)} <产妇院内转诊> 申请单为已返回")
        
        return make_response(code=0, msg="标记院内转诊为已返回成功")
        


# 新生儿院内转诊列表
class NewbornMedicalReferralListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_VIEW
    serializer_class = NewbornMedicalReferralSerializer
    response_msg = "获取院内转诊列表成功"
    error_response_msg = ""
    search_fields = ["baby__name","baby__maternity_admission__maternity__name","baby__maternity_admission__room__room_number"]
    
    def get_queryset(self):
        
        aps = self.request.query_params.get('aps', None)
        apt = self.request.query_params.get('apt', None)
        rd = self.request.query_params.get('rd', None)
        
        base_queryset = NewbornReferralApplication.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-application_time')
        
        if aps:
            if aps not in ReferralStatusEnum.values:
                self.error_response_msg = "转诊状态不正确"
                return None
            base_queryset = base_queryset.filter(referral_status=aps)
        
        if apt:
            try:
                apt_datetime = datetime.strptime(apt, "%Y-%m-%d %H:%M:%S")
                base_queryset = base_queryset.filter(application_time__gte=apt_datetime)
            except ValueError:
                self.error_response_msg = "查询日期格式错误"
                return None
        if rd:
            base_queryset = base_queryset.filter(referral_department__icontains=rd)
            
        AuditLogCreator.create_query_audit_log(self.request,"院内转诊","查看了<新生儿院内转诊>申请单列表")
        
        return base_queryset

    
# 新生儿院内转诊详情
class NewbornMedicalReferralDetailView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_VIEW
    
    def get(self, request, rid):
        try:
            medical_referral = NewbornReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            serializer = NewbornMedicalReferralDetailSerializer(medical_referral)
            
            AuditLogCreator.create_query_audit_log(self.request,"院内转诊",f"查看了 {get_newborn_referral_str(medical_referral)} <新生儿院内转诊> 申请单详情")
            
            return make_response(code=0, msg="获取院内转诊详情成功", data=serializer.data)
        except MaternityReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
    
# 新生儿院内转诊创建
class NewbornMedicalReferralCreateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def post(self, request,nid):
        
        emergency_level = request.data.get("emergency_level", None)
        
        if emergency_level not in EmergencyLevelEnum.values:
            return make_response(code=-1, msg="数据校验失败，紧急程度不正确")
        
        try:
            baby = Newborn.objects.select_related('maternity_admission').get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿不存在")
        
        if NewbornReferralApplication.has_pending_referral(baby):
            return make_response(code=-1, msg="新生儿有未处理的转诊申请，暂无法创建新的转诊申请")
        
        request.data["maternity_center"] = request.user.maternity_center.id
        request.data["baby"] = baby.id
        
        serializer = NewbornMedicalReferralCreateSerializer(data=request.data,context={'request': request})
        if serializer.is_valid():
            serializer.save()
            resData = NewbornMedicalReferralDetailSerializer(serializer.instance).data
            
            AuditLogCreator.create_create_audit_log(self.request,"院内转诊",f"创建了 {get_newborn_referral_str(serializer.instance)} <新生儿院内转诊> 申请单")
            
            return make_response(code=0, msg="创建院内转诊申请成功", data=resData)
        else:
            return make_response(code=-1, msg=serializer.errors)
        
# 新生儿院内转诊更新
class NewbornMedicalReferralUpdateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        try:
            medical_referral = NewbornReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except NewbornReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="转诊申请单已审核，无法修改")
        
        serializer = NewbornMedicalReferralUpdateSerializer(medical_referral, data=request.data,context={'request': request})
        if serializer.is_valid():
            
            cfs = get_field_changes(medical_referral,serializer.validated_data)
            
            serializer.save()
            resData = NewbornMedicalReferralDetailSerializer(serializer.instance).data
            
            AuditLogCreator.create_update_audit_log(self.request,"院内转诊",f"更新了 {get_newborn_referral_str(serializer.instance)} <新生儿院内转诊> 申请单",cfs)
            
            return make_response(code=0, msg="更新院内转诊申请成功", data=resData)
        else:
            return make_response(code=-1, msg=serializer.errors)
        
# 新生儿院内转诊删除
class NewbornMedicalReferralDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def delete(self, request, rid):
        
        try:
            medical_referral = NewbornReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except NewbornReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="转诊申请单已审核，无法删除，审核结果为：%s" % ApprovalStatusEnum(medical_referral.approval_status).label)
        
        medical_referral.delete()
        
        AuditLogCreator.create_delete_audit_log(self.request,"院内转诊",f"删除了 {get_newborn_referral_str(medical_referral)} <新生儿院内转诊> 申请单")
        
        return make_response(code=0, msg="删除院内转诊申请成功")

# 新生儿院内转诊审核
class NewbornMedicalReferralAuditView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        require_fields = ["result","opinion"]
        for field in require_fields:
            if field not in request.data:
                return make_response(code=-1, msg=f"数据校验失败，提供的数据不完整")
        
        result = request.data.get("result", False)
        opinion = request.data.get("opinion", "")
        
        if result not in [True, False]:
            return make_response(code=-1, msg="数据校验失败，审核结果不正确")
        
        try:
            medical_referral = NewbornReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except NewbornReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="转诊申请单已审核，无法再次审核，审核结果为：%s" % ApprovalStatusEnum(medical_referral.approval_status).label)
        
        if result:
            res = medical_referral.approve_referral(request.user,opinion)
        else:
            res = medical_referral.reject_referral(request.user,opinion)

        resData = NewbornMedicalReferralDetailSerializer(res).data
        
        AuditLogCreator.create_request_audit_log(self.request,"院内转诊",f"审核了 {get_newborn_referral_str(medical_referral)} <新生儿院内转诊> 申请单,审核结果为：{ApprovalStatusEnum(res.approval_status).label}")
        
        return make_response(code=0, msg="院内转诊申请审核成功", data=resData)



# 新生儿院内转诊标记转诊中
class NewbornMedicalReferralMarkReferralIngView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        try:
            medical_referral = NewbornReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except NewbornReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.referral_status != ReferralStatusEnum.PENDING_REFERRAL:
            return make_response(code=-1, msg="转诊状态不正确，当前状态为：%s" % ReferralStatusEnum(medical_referral.referral_status).label)
        
        medical_referral.referral_status = ReferralStatusEnum.REFERRALING
        medical_referral.save()
        
        AuditLogCreator.create_status_change_audit_log(self.request,"院内转诊",f"标记了 {get_newborn_referral_str(medical_referral)} <新生儿院内转诊> 申请单为转诊中")
        
        return make_response(code=0, msg="标记院内转诊为转诊中成功")
    
    

# 新生儿院内转诊标记已转诊
class NewbornMedicalReferralMarkReferralCompletedView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        try:
            medical_referral = NewbornReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except NewbornReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.referral_status != ReferralStatusEnum.REFERRALING:
            return make_response(code=-1, msg="转诊状态不正确，当前状态为：%s" % ReferralStatusEnum(medical_referral.referral_status).label)
        
        medical_referral.referral_status = ReferralStatusEnum.REFERRALLED
        medical_referral.save()
        
        AuditLogCreator.create_status_change_audit_log(self.request,"院内转诊",f"标记了 {get_newborn_referral_str(medical_referral)} <新生儿院内转诊> 申请单为已转诊")
        
        return make_response(code=0, msg="标记院内转诊为已转诊成功")
    
    

# 新生儿院内转诊标记已返回
class NewbornMedicalReferralMarkReferralReturnedView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MEDICAL_REFERRAL_EDIT
    
    def put(self, request, rid):
        
        rrd = request.data.get("rrd", None)
        
        if not isinstance(rrd, list):
            return make_response(code=-1, msg="数据校验失败，转诊返回出院小结错误")
        
        if not MedicalReferralReturnFile.check_rids(rrd,request.user.maternity_center):
            return make_response(code=-1, msg="数据校验失败，转诊返回出院小结错误")
        
        try:
            medical_referral = NewbornReferralApplication.objects.get(rid=rid,maternity_center=request.user.maternity_center)
        except NewbornReferralApplication.DoesNotExist:
            return make_response(code=-1, msg="院内转诊申请单不存在")
        
        if medical_referral.referral_status != ReferralStatusEnum.REFERRALLED:
            return make_response(code=-1, msg="转诊状态不正确，当前状态为：%s" % ReferralStatusEnum(medical_referral.referral_status).label)
        
        medical_referral.referral_return_discharge = rrd
        medical_referral.referral_status = ReferralStatusEnum.RETURNED
        medical_referral.save()
        
        AuditLogCreator.create_status_change_audit_log(self.request,"院内转诊",f"标记了 {get_newborn_referral_str(medical_referral)} <新生儿院内转诊> 申请单为已返回")
        
        return make_response(code=0, msg="标记院内转诊为已返回成功")