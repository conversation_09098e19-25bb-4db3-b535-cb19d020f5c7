from django.urls import path

from .views import MaternityMedicalReferralListView, MaternityMedicalReferralDetailView, \
    MaternityMedicalReferralCreateView, \
    MaternityMedicalReferralUpdateView, MaternityMedicalReferralDeleteView, MaternityMedicalReferralAuditView, \
    NewbornMedicalReferralListView, NewbornMedicalReferralDetailView, NewbornMedicalReferralCreateView, \
    NewbornMedicalReferralUpdateView, NewbornMedicalReferralDeleteView, NewbornMedicalReferralAuditView, \
    MaternityMedicalReferralMarkReferralIngView, \
    NewbornMedicalReferralMarkReferralIngView, MaternityMedicalReferralMarkReferralCompletedView, \
    NewbornMedicalReferralMarkReferralCompletedView, \
    MaternityMedicalReferralMarkReferralReturnedView, NewbornMedicalReferralMarkReferralReturnedView

urlpatterns = [
    # 产妇院内转诊列表
    path('maternity-medical-referral/list/', MaternityMedicalReferralListView.as_view(), name='maternity-medical-referral-list'),
    # 产妇院内转诊详情
    path('maternity-medical-referral/detail/<str:rid>/', MaternityMedicalReferralDetailView.as_view(), name='maternity-medical-referral-detail'),
    # 产妇院内转诊创建
    path('maternity-medical-referral/create/<str:aid>/', MaternityMedicalReferralCreateView.as_view(), name='maternity-medical-referral-create'),
    # 产妇院内转诊更新
    path('maternity-medical-referral/update/<str:rid>/', MaternityMedicalReferralUpdateView.as_view(), name='maternity-medical-referral-update'),
    # 产妇院内转诊删除
    path('maternity-medical-referral/delete/<str:rid>/', MaternityMedicalReferralDeleteView.as_view(), name='maternity-medical-referral-delete'),
    # 产妇院内转诊审核
    path('maternity-medical-referral/audit/<str:rid>/', MaternityMedicalReferralAuditView.as_view(), name='maternity-medical-referral-audit'),
    # 产妇院内转诊标记转诊中
    path('maternity-medical-referral/mark-referral-ing/<str:rid>/', MaternityMedicalReferralMarkReferralIngView.as_view(), name='maternity-medical-referral-mark-referral-ing'),
    # 产妇院内转诊标记已转诊
    path('maternity-medical-referral/mark-referral-completed/<str:rid>/', MaternityMedicalReferralMarkReferralCompletedView.as_view(), name='maternity-medical-referral-mark-referral-completed'),
    # 产妇院内转诊标记已返回
    path('maternity-medical-referral/mark-referral-returned/<str:rid>/', MaternityMedicalReferralMarkReferralReturnedView.as_view(), name='maternity-medical-referral-mark-referral-returned'),



    # 新生儿院内转诊列表
    path('newborn-medical-referral/list/', NewbornMedicalReferralListView.as_view(), name='newborn-medical-referral-list'),
    # 新生儿院内转诊详情
    path('newborn-medical-referral/detail/<str:rid>/', NewbornMedicalReferralDetailView.as_view(), name='newborn-medical-referral-detail'),
    # 新生儿院内转诊创建
    path('newborn-medical-referral/create/<str:nid>/', NewbornMedicalReferralCreateView.as_view(), name='newborn-medical-referral-create'),
    # 新生儿院内转诊更新
    path('newborn-medical-referral/update/<str:rid>/', NewbornMedicalReferralUpdateView.as_view(), name='newborn-medical-referral-update'),
    # 新生儿院内转诊删除
    path('newborn-medical-referral/delete/<str:rid>/', NewbornMedicalReferralDeleteView.as_view(), name='newborn-medical-referral-delete'),
    # 新生儿院内转诊审核
    path('newborn-medical-referral/audit/<str:rid>/', NewbornMedicalReferralAuditView.as_view(), name='newborn-medical-referral-audit'),
    # 新生儿院内转诊标记转诊中
    path('newborn-medical-referral/mark-referral-ing/<str:rid>/', NewbornMedicalReferralMarkReferralIngView.as_view(), name='newborn-medical-referral-mark-referral-ing'),
    # 新生儿院内转诊标记已转诊
    path('newborn-medical-referral/mark-referral-completed/<str:rid>/', NewbornMedicalReferralMarkReferralCompletedView.as_view(), name='newborn-medical-referral-mark-referral-completed'),
    # 新生儿院内转诊标记已返回
    path('newborn-medical-referral/mark-referral-returned/<str:rid>/', NewbornMedicalReferralMarkReferralReturnedView.as_view(), name='newborn-medical-referral-mark-referral-returned'),
]