
from django.contrib import admin

from customer_service.medical_referral.models import MaternityReferralApplication, NewbornReferralApplication


@admin.register(MaternityReferralApplication)
class MaternityReferralApplicationAdmin(admin.ModelAdmin):
    list_display = ["id","get_maternity_name", "get_room_number", "referral_department", "main_symptoms_and_reasons",
                  "emergency_level",  "application_time", "approval_status"]
    
    def get_maternity_name(self, obj):
        if obj.maternity_admission and obj.maternity_admission.maternity:
            return obj.maternity_admission.maternity.name
        return "-"
    get_maternity_name.short_description = "产妇姓名"
    
    def get_room_number(self, obj):
        if obj.maternity_admission and obj.maternity_admission.room:
            return obj.maternity_admission.room.room_number
        return "-"
    get_room_number.short_description = "房间号"
    
    
@admin.register(NewbornReferralApplication)
class NewbornReferralApplicationAdmin(admin.ModelAdmin):
    list_display = ["id","baby_name", "get_room_number", "referral_department", "main_symptoms_and_reasons",
                  "emergency_level",  "application_time", "approval_status"]
    
    def baby_name(self, obj):
        if obj.baby:
            return obj.baby.name
        return "-"
    baby_name.short_description = "新生儿姓名"
    
    def get_room_number(self, obj):
        if obj.baby and obj.baby.maternity_admission and obj.baby.maternity_admission.room:
            return obj.baby.maternity_admission.room.room_number
        return "-"
    get_room_number.short_description = "房间号"
    