from django.db import models


# 紧急程度
class EmergencyLevelEnum(models.TextChoices):
    # 一般
    GENERAL = 'GENERAL','一般'
    # 紧急
    EMERGENCY = 'EMERGENCY','紧急'
    # 非常紧急
    VERY_EMERGENCY = 'VERY_EMERGENCY','非常紧急'
    
    
    
# 转诊状态
class ReferralStatusEnum(models.TextChoices):
    # 待审核
    PENDING_AUDIT = 'PENDING_AUDIT','待审核'
    # 待转诊
    PENDING_REFERRAL = 'PENDING_REFERRAL','待转诊'
    # 已拒绝
    REJECTED = 'REJECTED','已拒绝'
    # 转诊中
    REFERRALING = 'REFERRALING','转诊中'
    # 已转诊
    REFERRALLED = 'REFERRALLED','已转诊'
    # 已返回，转诊结束
    RETURNED = 'RETURNED','已返回'
    


    
    
    
    
    
    