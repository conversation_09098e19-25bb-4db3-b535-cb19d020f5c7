from django.urls import path

from .views import RoomChangeApplicationListView, RoomChangeApplicationDetailView, RoomChangeApplicationCreateView, \
    RoomChangeApplicationUpdateView, RoomChangeApplicationDeleteView, RoomChangeApplicationAuditView

urlpatterns = [
    # 换房申请列表
    path('room-change-application/list/', RoomChangeApplicationListView.as_view(), name='room-change-application-list'),
    # 换房申请详情
    path('room-change-application/detail/<str:rid>/', RoomChangeApplicationDetailView.as_view(), name='room-change-application-detail'),
    # 换房申请创建
    path('room-change-application/create/<str:aid>/', RoomChangeApplicationCreateView.as_view(), name='room-change-application-create'),
    # 换房申请更新
    path('room-change-application/update/<str:rid>/', RoomChangeApplicationUpdateView.as_view(), name='room-change-application-update'),
    # 换房申请删除
    path('room-change-application/delete/<str:rid>/', RoomChangeApplicationDeleteView.as_view(), name='room-change-application-delete'),
    # 换房申请审核
    path('room-change-application/audit/<str:rid>/', RoomChangeApplicationAuditView.as_view(), name='room-change-application-audit'),
]