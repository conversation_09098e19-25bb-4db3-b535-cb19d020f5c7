from decimal import Decimal, ROUND_HALF_UP, InvalidOperation

from rest_framework import serializers

from core.enum import ApprovalStatusEnum
from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.room.serializers import RoomDetailSerializer
from customer_service.room_change.models import RoomChangeApplication


# 换房申请列表序列化器
class RoomChangeApplicationSerializer(serializers.ModelSerializer):

    
    maternity_name = serializers.SerializerMethodField()
    source_room = serializers.SerializerMethodField()
    intented_room = serializers.SerializerMethodField()
    approval_status_label = serializers.SerializerMethodField()
    application_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = RoomChangeApplication
        fields = ["rid","maternity_name","source_room","intented_room","reason","price_difference_description","price_difference","application_time","approval_status","approval_status_label"]

    
    def get_approval_status_label(self, obj):
        return ApprovalStatusEnum(obj.approval_status).label
    
    def get_maternity_name(self, obj):
        return obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else "-"
    
    def get_source_room(self, obj):
        return f'{obj.maternity_admission.room.room_type} / {obj.maternity_admission.room.room_number}' if obj.maternity_admission.room else "-"
    
    def get_intented_room(self, obj):
        return f'{obj.intented_room.room_type} / {obj.intented_room.room_number}' if obj.intented_room else "-"
    

# 换房申请详情序列化器
class RoomChangeApplicationDetailSerializer(serializers.ModelSerializer):

    maternity_name = serializers.SerializerMethodField()
    source_room = serializers.SerializerMethodField()
    intented_room = serializers.SerializerMethodField()
    intented_room_detail = serializers.SerializerMethodField()
    approval_status_label = serializers.SerializerMethodField()
    application_time = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = RoomChangeApplication
        fields = ["rid","maternity_name",'maternity_admission',"source_room","intented_room","intented_room_detail","reason","price_difference_description","price_difference","application_time","approval_status","approval_status_label"]

    
    def get_approval_status_label(self, obj):
        return ApprovalStatusEnum(obj.approval_status).label
    
    def get_maternity_name(self, obj):
        return obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else "-"
    
    def get_source_room(self, obj):
        return f'{obj.maternity_admission.room.room_type} / {obj.maternity_admission.room.room_number}' if obj.maternity_admission.room else "-"
    
    def get_intented_room(self, obj):
        return f'{obj.intented_room.room_type} / {obj.intented_room.room_number}' if obj.intented_room else "-"
    
    def get_intented_room_detail(self, obj):
        return RoomDetailSerializer(obj.intented_room).data if obj.intented_room else None
    
    
    
# 换房申请创建序列化器
class RoomChangeApplicationCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = RoomChangeApplication
        fields = ["maternity_center","maternity_admission","intented_room","reason","price_difference_description","price_difference"]

        
# 换房申请更新序列化器
class RoomChangeApplicationUpdateSerializer(serializers.ModelSerializer):

    price_difference = serializers.DecimalField(max_digits=10, decimal_places=None, required=False, allow_null=True)


    class Meta:
        model = RoomChangeApplication
        fields = ["intented_room","reason","price_difference_description","price_difference"]

    def validate_price_difference(self, value):

        if value is None:
            return value

        try:
            return Decimal(str(value)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        except (ValueError, TypeError, InvalidOperation):
            raise serializers.ValidationError("费用差额格式不正确，请输入有效数字")

