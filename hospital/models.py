from django.db import models

# 医院信息模型
class Hospital(models.Model):
    # 医院名称
    name = models.CharField(max_length=200, verbose_name="医院名称")
    # 医院地址
    address = models.CharField(max_length=500, verbose_name="地址")
    # 医院联系电话
    phone_number = models.CharField(max_length=20, verbose_name="联系电话")
    # 医院描述
    description = models.TextField(blank=True, null=True, verbose_name="医院描述")
    # 创建时间
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 更新时间
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    # 是否启用
    is_active = models.BooleanField(default=True, verbose_name="是否启用")

    class Meta:
        verbose_name = "医院"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


# 科室信息模型
class HospitalDepartment(models.Model):
    # 所属医院
    hospital = models.ForeignKey(Hospital, on_delete=models.CASCADE, related_name="departments", verbose_name="所属医院")
    # 科室名称
    name = models.CharField(max_length=100, verbose_name="科室名称")
    # 科室描述
    description = models.TextField(blank=True, null=True, verbose_name="科室描述")
    # 创建时间
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 更新时间
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    # 是否启用
    is_active = models.BooleanField(default=True, verbose_name="是否启用")

    class Meta:
        verbose_name = "科室"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.hospital.name}-{self.name}"