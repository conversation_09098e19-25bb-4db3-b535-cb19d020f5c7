@echo off
REM 开发环境启动脚本

REM 导入环境变量 (如果 env.sh 包含需要设置的环境变量，需要手动在这里设置)
REM 例如：set MY_VARIABLE=my_value

REM 激活虚拟环境
call .venv\Scripts\activate.bat

REM 检查虚拟环境是否成功激活 (在bat脚本中检查激活状态比较复杂，这里我们假设activate.bat会输出错误如果失败)

REM 创建迁移文件
python manage.py makemigrations

REM 执行迁移
python manage.py migrate

REM 启动开发服务器
python manage.py runserver 127.0.0.1:7777

REM 保持窗口打开，以便查看服务器输出或错误 (可选，如果希望窗口在服务器停止后自动关闭可以删除 pause)
pause