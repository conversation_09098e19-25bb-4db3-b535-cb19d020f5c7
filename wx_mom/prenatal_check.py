"""
产检提醒功能模块
基于北京市人民政府发布的权威产检指南生成产检提醒列表
参考：北京市产前检查指南（2024年版）
"""

from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Union


def generate_prenatal_check_reminders(expected_delivery_date: Optional[Union[str, date]]) -> Dict[str, List[Dict]]:
    """
    根据预计分娩日期生成产检提醒列表

    Args:
        expected_delivery_date: 预计分娩日期，可以是字符串 'YYYY-MM-DD' 或 datetime.date 对象，可以为 None

    Returns:
        Dict包含两个列表：
        - completed: 已完成的产检项目列表
        - pending: 待完成的产检项目列表

    Examples:
        >>> generate_prenatal_check_reminders('2024-03-15')
        {
            'completed': [
                {
                    'week': 12,
                    'week_range': '11-14周',
                    'name': '第二次产检（NT检查）',
                    'description': '胎儿NT检查，初步筛查染色体异常、先天性心脏病等',
                    'key_items': ['NT检查（胎儿颈项透明层厚度）', '常规产科检查'],
                    'notes': 'NT检查最佳时期为11+0周至13+6周，必须在14周前完成',
                    'check_date': '2023-12-22',
                    'is_important': True,
                    'tags': ['孕早期', 'NT检查', '染色体筛查'],
                    'suggested_time': '09:00-11:00'
                }
            ],
            'pending': [
                {
                    'week': 22,
                    'week_range': '20-24周',
                    'name': '第四次产检（大排畸）',
                    'description': '系统性超声检查，全面筛查胎儿结构异常',
                    'key_items': ['大排畸超声检查', '胎儿测量', '血常规'],
                    'notes': '需提前预约；不需空腹、憋尿；可适当饮食增加胎动',
                    'check_date': '2024-01-26',
                    'is_important': True,
                    'tags': ['孕中期', '大排畸', '结构筛查'],
                    'suggested_time': '09:30-11:30'
                }
            ]
        }
    """
    # 如果预计分娩日期为空，返回空列表
    if not expected_delivery_date:
        return {
            'completed': [],
            'pending': []
        }

    try:
        # 解析预计分娩日期，支持字符串和date对象两种类型
        if isinstance(expected_delivery_date, str):
            delivery_date = datetime.strptime(expected_delivery_date, '%Y-%m-%d').date()
        elif isinstance(expected_delivery_date, date):
            delivery_date = expected_delivery_date
        else:
            raise ValueError("expected_delivery_date must be str or date object")

        today = datetime.now().date()

        # 计算孕期开始日期（预产期往前推280天，即40周）
        pregnancy_start_date = delivery_date - timedelta(days=280)

        # 获取产检时间表
        prenatal_schedule = _get_prenatal_check_schedule()

        completed = []
        pending = []

        for check_item in prenatal_schedule:
            # 计算具体的产检日期
            check_date = pregnancy_start_date + timedelta(weeks=check_item['week'])

            # 构建产检项目信息
            check_info = {
                'week': check_item['week'],
                'week_range': check_item['week_range'],
                'name': check_item['name'],
                'description': check_item['description'],
                'key_items': check_item['key_items'],
                'notes': check_item['notes'],
                'check_date': check_date.strftime('%Y-%m-%d'),
                'is_important': check_item['is_important'],
                'tags': check_item['tags'],
                'suggested_time': check_item['suggested_time']
            }

            # 根据当前日期判断是已完成还是待完成
            if check_date <= today:
                completed.append(check_info)
            else:
                pending.append(check_info)

        return {
            'completed': completed,
            'pending': pending
        }

    except ValueError:
        # 日期格式错误，返回空列表
        return {
            'completed': [],
            'pending': []
        }


def _get_prenatal_check_schedule() -> List[Dict]:
    """
    获取北京市权威产检时间表
    基于北京市人民政府发布的产前检查指南
    推荐产检孕周：6-13+6周、14-19+6周、20-24周、25-28周、29-32周、33-36周、37-41周

    Returns:
        产检项目列表，每个项目包含：
        - week: 孕周
        - week_range: 孕周范围
        - name: 检查名称
        - description: 检查描述
        - key_items: 关键检查项目
        - notes: 注意事项
        - is_important: 是否为重要检查
        - tags: 检查标签列表
        - suggested_time: 建议检查时间（如：08:00-10:00）
    """
    return [
        # 第一次产检：孕6-8周
        {
            'week': 7,
            'week_range': '6-8周',
            'name': '第一次产检',
            'description': '建立母子健康手册，确定孕周，推算预产期，除外宫外孕',
            'key_items': [
                '超声检查（确定孕周、推算预产期、除外宫外孕）',
                '血常规、尿常规',
                '血型（ABO血型和Rh血型）',
                '空腹血糖',
                '肝、肾功能',
                '乙型肝炎病毒、梅毒螺旋体、HIV筛查',
                '心电图检查',
                '妊娠期风险分级评估'
            ],
            'notes': '抽血检测前保持空腹；尿常规选择中段尿；重点检查母婴传播疾病',
            'is_important': True,
            'tags': ['孕早期', '建档', '基础检查', '传染病筛查'],
            'suggested_time': '08:00-10:00'
        },

        # 第二次产检：孕11-14周
        {
            'week': 12,
            'week_range': '11-14周',
            'name': '第二次产检（NT检查）',
            'description': '胎儿NT检查，初步筛查染色体异常、先天性心脏病等',
            'key_items': [
                'NT检查（胎儿颈项透明层厚度）',
                '常规产科检查',
                '血压、体重测量'
            ],
            'notes': 'NT检查最佳时期为11+0周至13+6周，必须在14周前完成，建议提前预约',
            'is_important': True,
            'tags': ['孕早期', 'NT检查', '染色体筛查', '结构筛查'],
            'suggested_time': '09:00-11:00'
        },

        # 第三次产检：孕15-20周
        {
            'week': 17,
            'week_range': '15-20周',
            'name': '第三次产检（唐氏筛查）',
            'description': '根据孕妇情况进行唐氏筛查、无创DNA或羊水穿刺',
            'key_items': [
                '孕中期唐氏筛查/无创产前DNA筛查/产前诊断（羊水穿刺）',
                '血常规、尿常规',
                '常规产科检查'
            ],
            'notes': '唐氏筛查需空腹；无创DNA不需空腹；根据年龄、病史等选择检查方式',
            'is_important': True,
            'tags': ['孕中期', '唐氏筛查', '无创DNA', '产前诊断'],
            'suggested_time': '08:30-10:30'
        },

        # 第四次产检：孕20-24周
        {
            'week': 22,
            'week_range': '20-24周',
            'name': '第四次产检（大排畸）',
            'description': '系统性超声检查，全面筛查胎儿结构异常',
            'key_items': [
                '大排畸超声检查',
                '胎儿双顶径、头围、腹围、股骨长测量',
                '胎儿外观发育检查',
                '血常规、尿常规',
                '宫高、腹围测量',
                '胎心听诊'
            ],
            'notes': '需提前预约；不需空腹、憋尿；可适当饮食增加胎动',
            'is_important': True,
            'tags': ['孕中期', '大排畸', '结构筛查', '超声检查'],
            'suggested_time': '09:30-11:30'
        },

        # 第五次产检：孕24-28周
        {
            'week': 26,
            'week_range': '24-28周',
            'name': '第五次产检（糖耐量筛查）',
            'description': '妊娠期糖尿病筛查，评估血糖代谢情况',
            'key_items': [
                '糖耐量筛查（75g OGTT）',
                '血常规、尿常规',
                '常规产科检查'
            ],
            'notes': '检查前3天正常饮食；前一天晚上10点后禁食；空腹到医院；糖水5分钟内喝完',
            'is_important': True,
            'tags': ['孕中期', '糖耐量', '妊娠糖尿病', '代谢筛查'],
            'suggested_time': '08:00-11:00'
        },

        # 第六次产检：孕28-30周
        {
            'week': 29,
            'week_range': '28-30周',
            'name': '第六次产检（小排畸）',
            'description': '胎儿生长发育评估，开始每两周产检',
            'key_items': [
                'B超检查（小排畸）',
                '胎儿生长发育情况评估',
                '血常规、尿常规',
                '常规产科检查'
            ],
            'notes': '从28周开始每两周产检一次；开始每天自数胎动；发现异常及时就医',
            'is_important': False,
            'tags': ['孕晚期', '小排畸', '生长发育', '胎动监测'],
            'suggested_time': '09:00-11:00'
        },

        # 第七次产检：孕30-32周
        {
            'week': 31,
            'week_range': '30-32周',
            'name': '第七次产检（传染病复查）',
            'description': '复查传染病指标，母婴阻断准备',
            'key_items': [
                '复测乙型肝炎病毒、梅毒螺旋体、HIV筛查',
                '血常规、尿常规',
                '常规产科检查'
            ],
            'notes': '妊娠期如存在感染风险要告知医生；做好母婴阻断和分娩防护准备',
            'is_important': True,
            'tags': ['孕晚期', '传染病复查', '母婴阻断', '分娩准备'],
            'suggested_time': '08:30-10:30'
        },

        # 第八次产检：孕32-34周
        {
            'week': 33,
            'week_range': '32-34周',
            'name': '第八次产检（胎心监护开始）',
            'description': '全面检查，开始胎心监护评估',
            'key_items': [
                '血常规、尿常规',
                '生化全项',
                '产科B超',
                '胎心监护（根据情况）'
            ],
            'notes': '胎心监护用监护带绑肚子，每次20分钟，半躺卧位，穿宽松衣裤，排空尿液，进食后进行',
            'is_important': False,
            'tags': ['孕晚期', '胎心监护', '生化检查', '宫内状况'],
            'suggested_time': '10:00-11:30'
        },

        # 第九次产检：孕35-36周
        {
            'week': 35,
            'week_range': '35-36周',
            'name': '第九次产检',
            'description': '常规检查，准备进入每周产检阶段',
            'key_items': [
                '血常规、尿常规',
                '产科B超',
                '胎心监护',
                '常规产科检查'
            ],
            'notes': '从37周开始每周产检一次',
            'is_important': False,
            'tags': ['孕晚期', '常规检查', '产前准备'],
            'suggested_time': '10:00-11:30'
        },

        # 每周产检阶段：37-41周
        {
            'week': 37,
            'week_range': '37周',
            'name': '足月产检（第十次）',
            'description': '进入足月期，开始每周产检',
            'key_items': [
                '胎心监护',
                '常规产科检查',
                '分娩方式评估'
            ],
            'notes': '每周都要产检；37-38周进行分娩方式鉴定',
            'is_important': True,
            'tags': ['足月期', '每周检查', '分娩评估'],
            'suggested_time': '10:00-11:30'
        },

        {
            'week': 38,
            'week_range': '38周',
            'name': '第十一次产检',
            'description': '胎儿宫内情况全面评估',
            'key_items': [
                '胎心监护',
                '彩超检测（胎儿宫内情况、胎盘成熟度、羊水指数）',
                '分泌物B族链球菌检测（必要时）',
                '分娩方式最终确定'
            ],
            'notes': '重点评估胎盘功能和羊水情况',
            'is_important': True,
            'tags': ['足月期', '胎盘评估', '羊水检测', '分娩准备'],
            'suggested_time': '09:30-11:30'
        },

        {
            'week': 39,
            'week_range': '39周',
            'name': '第十二次产检',
            'description': '临产前检查',
            'key_items': [
                '胎心监护',
                '常规产科检查',
                '临产征象评估'
            ],
            'notes': '密切关注临产征象',
            'is_important': True,
            'tags': ['足月期', '临产准备', '征象监测'],
            'suggested_time': '10:00-11:30'
        },

        {
            'week': 40,
            'week_range': '40周',
            'name': '预产期检查',
            'description': '预产期当周全面评估',
            'key_items': [
                '胎心监护',
                '全面产科检查',
                '胎儿宫内状况评估',
                '过期妊娠风险评估'
            ],
            'notes': '预产期当周，如无临产征象需评估是否引产',
            'is_important': True,
            'tags': ['预产期', '引产评估', '宫内状况'],
            'suggested_time': '09:00-11:00'
        },

        {
            'week': 41,
            'week_range': '41周',
            'name': '过期妊娠监测',
            'description': '超过预产期的密切监测',
            'key_items': [
                '每日胎心监护',
                '羊水量评估',
                '胎盘功能评估',
                '引产指征评估'
            ],
            'notes': '超过41周属于过期妊娠，围产儿病率和死亡率增高，需及时寻求医生帮助',
            'is_important': True,
            'tags': ['过期妊娠', '密切监测', '引产指征', '高危妊娠'],
            'suggested_time': '09:00-11:00'
        }
    ]


def get_current_pregnancy_week(expected_delivery_date: Optional[Union[str, date]]) -> Optional[int]:
    """
    根据预计分娩日期计算当前孕周

    Args:
        expected_delivery_date: 预计分娩日期，可以是字符串 'YYYY-MM-DD' 或 datetime.date 对象

    Returns:
        当前孕周数，如果日期无效则返回 None
    """
    if not expected_delivery_date:
        return None

    try:
        # 解析预计分娩日期，支持字符串和date对象两种类型
        if isinstance(expected_delivery_date, str):
            delivery_date = datetime.strptime(expected_delivery_date, '%Y-%m-%d').date()
        elif isinstance(expected_delivery_date, date):
            delivery_date = expected_delivery_date
        else:
            return None

        today = datetime.now().date()

        # 计算孕期开始日期（预产期往前推280天）
        pregnancy_start_date = delivery_date - timedelta(days=280)

        # 计算当前孕周
        days_pregnant = (today - pregnancy_start_date).days
        current_week = days_pregnant // 7

        # 孕周范围应该在0-42周之间
        if 0 <= current_week <= 42:
            return current_week
        else:
            return None

    except (ValueError, TypeError):
        return None


def get_next_prenatal_check(expected_delivery_date: Optional[Union[str, date]]) -> Optional[Dict]:
    """
    获取下一次产检信息

    Args:
        expected_delivery_date: 预计分娩日期，可以是字符串 'YYYY-MM-DD' 或 datetime.date 对象

    Returns:
        下一次产检信息，如果没有则返回 None
    """
    if not expected_delivery_date:
        return None

    reminders = generate_prenatal_check_reminders(expected_delivery_date)
    pending_checks = reminders.get('pending', [])

    if pending_checks:
        # 返回最近的一次产检
        return pending_checks[0]
    else:
        return None


def get_prenatal_check_by_week(week: int) -> Optional[Dict]:
    """
    根据孕周获取对应的产检信息

    Args:
        week: 孕周数

    Returns:
        对应孕周的产检信息，如果没有则返回 None
    """
    schedule = _get_prenatal_check_schedule()

    for check_item in schedule:
        if check_item['week'] == week:
            return check_item

    return None


def get_prenatal_check_summary() -> Dict[str, any]:
    """
    获取产检指南总览信息

    Returns:
        产检指南总览，包含检查次数、重要时间点等
    """
    schedule = _get_prenatal_check_schedule()

    total_checks = len(schedule)
    important_checks = [item for item in schedule if item['is_important']]

    key_timepoints = [
        {
            'week_range': '6-8周',
            'milestone': '建档立案',
            'description': '确认怀孕，建立母子健康手册'
        },
        {
            'week_range': '11-14周',
            'milestone': 'NT筛查',
            'description': '早期染色体异常筛查的关键时期'
        },
        {
            'week_range': '20-24周',
            'milestone': '大排畸',
            'description': '胎儿结构异常筛查的最佳时期'
        },
        {
            'week_range': '24-28周',
            'milestone': '糖耐量筛查',
            'description': '妊娠期糖尿病筛查'
        },
        {
            'week_range': '37-41周',
            'milestone': '每周产检',
            'description': '足月期密切监测，准备分娩'
        }
    ]

    return {
        'total_checks': total_checks,
        'important_checks_count': len(important_checks),
        'recommended_frequency': '共7-11次产检',
        'key_timepoints': key_timepoints,
        'special_notes': [
            '有高危因素的孕妈妈需要酌情增加产检次数和项目',
            '超过41周属于过期妊娠，需要及时就医',
            '从28周开始要每天自数胎动',
            '37周开始每周产检一次'
        ]
    }