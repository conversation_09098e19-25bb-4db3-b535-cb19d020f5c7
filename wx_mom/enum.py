from django.db import models

# 消息类型
class MessageTypeEnum(models.TextChoices):
    # 系统消息
    SYSTEM_MESSAGE = 'SYSTEM_MESSAGE', '系统消息'
    # 产检、复查等医疗提醒
    MEDICAL_REMINDER = 'MEDICAL_REMINDER', '医疗提醒' 
    # 检查结果、转诊通知等
    MEDICAL_NOTICE = 'MEDICAL_NOTICE', '医疗通知'    
    # 新活动发布、活动变更
    ACTIVITY_NOTICE = 'ACTIVITY_NOTICE', '活动通知' 
    # 活动即将开始提醒  
    ACTIVITY_REMINDER = 'ACTIVITY_REMINDER', '活动提醒' 
    # 预约确认
    APPOINTMENT_CONFIRMATION = 'APPOINTMENT_CONFIRMATION', '预约确认'
    # 预约提醒
    APPOINTMENT_REMINDER = 'APPOINTMENT_REMINDER', '预约提醒'
    # 预约变更
    APPOINTMENT_CHANGE = 'APPOINTMENT_CHANGE', '预约变更'
    # 护理完成、饮食安排等
    SERVICE_NOTICE = 'SERVICE_NOTICE', '服务通知'     
    # 意见反馈处理结果
    FEEDBACK_RESULT = 'FEEDBACK_RESULT', '反馈结果'   
    # 紧急/警告
    URGENT_NOTICE = 'URGENT_NOTICE', '紧急通知'       
    # 异常警告、安全提醒
    WARNING_MESSAGE = 'WARNING_MESSAGE', '警告消息'   
    # 费用变更、账单生成
    BILLING_NOTICE = 'BILLING_NOTICE', '账单通知'     
    # 通用
    GENERAL_NOTICE = 'GENERAL_NOTICE', '一般通知'     

    