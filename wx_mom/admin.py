from django.contrib import admin
from django.utils.html import format_html

from .models import WxMaternityMessage, WxUnpaidMaternityMessage, PrenatalCheckReminder, UnpaidMaternityPrenatalInfo, PublicHealthKnowledge, HealthKnowledge
from .enum import MessageTypeEnum


# 消息中心管理
@admin.register(WxMaternityMessage)
class WxMaternityMessageAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'maternity_name', 'maternity_phone', 'message_type_display',
        'message_content_preview', 'is_read_display', 'creator_name',
        'created_at'
    )

    list_filter = (
        'message_type', 'is_read', 'maternity_center', 'created_at'
    )

    search_fields = (
        'maternity_admission__maternity__name',
        'maternity_admission__maternity__phone',
        'message_content', 'creator__name'
    )

    readonly_fields = ('id', 'rid', 'created_at', 'updated_at')

    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'maternity_center', 'maternity_admission', 'message_type')
        }),
        ('消息内容', {
            'fields': ('message_content', 'is_read','is_loaded'),
            'description': '消息的具体内容和阅读状态'
        }),
        ('创建信息', {
            'fields': ('creator', 'rid'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    # 自定义显示方法
    def maternity_name(self, obj):
        if obj.maternity_admission and obj.maternity_admission.maternity:
            return obj.maternity_admission.maternity.name
        return '-'
    maternity_name.short_description = '产妇姓名'
    maternity_name.admin_order_field = 'maternity_admission__maternity__name'

    def maternity_phone(self, obj):
        if obj.maternity_admission and obj.maternity_admission.maternity:
            return obj.maternity_admission.maternity.phone
        return '-'
    maternity_phone.short_description = '联系电话'
    maternity_phone.admin_order_field = 'maternity_admission__maternity__phone'

    def message_type_display(self, obj):
        """消息类型带颜色显示"""
        type_colors = {
            MessageTypeEnum.SYSTEM_MESSAGE: '#6c757d',           # 灰色
            MessageTypeEnum.MEDICAL_REMINDER: '#ffc107',        # 黄色
            MessageTypeEnum.MEDICAL_NOTICE: '#17a2b8',          # 蓝色
            MessageTypeEnum.ACTIVITY_NOTICE: '#28a745',         # 绿色
            MessageTypeEnum.ACTIVITY_REMINDER: '#fd7e14',       # 橙色
            MessageTypeEnum.APPOINTMENT_CONFIRMATION: '#20c997', # 青绿色
            MessageTypeEnum.APPOINTMENT_REMINDER: '#ffc107',    # 黄色
            MessageTypeEnum.APPOINTMENT_CHANGE: '#6f42c1',      # 紫色
            MessageTypeEnum.SERVICE_NOTICE: '#17a2b8',          # 蓝色
            MessageTypeEnum.FEEDBACK_RESULT: '#6c757d',         # 灰色
            MessageTypeEnum.URGENT_NOTICE: '#dc3545',           # 红色
            MessageTypeEnum.WARNING_MESSAGE: '#fd7e14',         # 橙色
            MessageTypeEnum.BILLING_NOTICE: '#e83e8c',          # 粉色
            MessageTypeEnum.GENERAL_NOTICE: '#6c757d',          # 灰色
        }
        color = type_colors.get(obj.message_type, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            MessageTypeEnum(obj.message_type).label
        )
    message_type_display.short_description = '消息类型'
    message_type_display.admin_order_field = 'message_type'

    def message_content_preview(self, obj):
        """消息内容预览（限制长度）"""
        if len(obj.message_content) > 50:
            return f"{obj.message_content[:50]}..."
        return obj.message_content
    message_content_preview.short_description = '消息内容'

    def is_read_display(self, obj):
        """已读状态显示"""
        if obj.is_read:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">✓ 已读</span>'
            )
        return format_html(
            '<span style="color: #dc3545; font-weight: bold;">✗ 未读</span>'
        )
    is_read_display.short_description = '阅读状态'
    is_read_display.admin_order_field = 'is_read'

    def creator_name(self, obj):
        if obj.creator:
            return obj.creator.name
        return '系统'
    creator_name.short_description = '创建人'
    creator_name.admin_order_field = 'creator__name'

    # 优化查询性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'maternity_center',
            'maternity_admission__maternity',
            'creator'
        )

    # 自定义操作
    actions = ['mark_as_read', 'mark_as_unread', 'delete_selected_messages']

    def mark_as_read(self, request, queryset):
        """批量标记为已读"""
        updated = queryset.update(is_read=True)
        self.message_user(request, f"已将 {updated} 条消息标记为已读")
    mark_as_read.short_description = "标记选中消息为已读"

    def mark_as_unread(self, request, queryset):
        """批量标记为未读"""
        updated = queryset.update(is_read=False)
        self.message_user(request, f"已将 {updated} 条消息标记为未读")
    mark_as_unread.short_description = "标记选中消息为未读"

    def delete_selected_messages(self, request, queryset):
        """批量删除消息"""
        count = queryset.count()
        queryset.delete()
        self.message_user(request, f"已删除 {count} 条消息")
    delete_selected_messages.short_description = "删除选中的消息"

    # 分页设置
    list_per_page = 25
    ordering = ('-created_at',)

    # 添加自定义过滤器
    def get_list_filter(self, request):
        filters = list(self.list_filter)
        # 可以根据用户权限动态调整过滤器
        # 例如：if not request.user.is_superuser: filters.remove('maternity_center')
        return filters


# 产检提醒管理
@admin.register(PrenatalCheckReminder)
class PrenatalCheckReminderAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'maternity_name', 'week', 'week_range', 'name',
        'check_date', 'is_important_display', 'is_completed_display', 'created_at'
    )

    list_filter = (
        'is_important', 'is_completed', 'week', 'maternity_center',
        'check_date', 'created_at'
    )

    search_fields = (
        'maternity__name', 'maternity__phone', 'name', 'description'
    )

    readonly_fields = ('id', 'rid', 'created_at', 'updated_at')

    date_hierarchy = 'check_date'

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'maternity_center', 'maternity', 'week', 'week_range')
        }),
        ('检查详情', {
            'fields': ('name', 'description', 'key_items', 'notes', 'suggested_time'),
            'description': '产检的具体信息和注意事项'
        }),
        ('状态信息', {
            'fields': ('check_date', 'is_important', 'is_completed', 'tags')
        }),
        ('系统信息', {
            'fields': ('rid', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def maternity_name(self, obj):
        return obj.maternity.name if obj.maternity else '-'
    maternity_name.short_description = '产妇姓名'
    maternity_name.admin_order_field = 'maternity__name'

    def is_important_display(self, obj):
        if obj.is_important:
            return format_html(
                '<span style="color: #dc3545; font-weight: bold;">⭐ 重要</span>'
            )
        return format_html('<span style="color: #6c757d;">普通</span>')
    is_important_display.short_description = '重要程度'
    is_important_display.admin_order_field = 'is_important'

    def is_completed_display(self, obj):
        if obj.is_completed:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">✓ 已完成</span>'
            )
        return format_html(
            '<span style="color: #ffc107; font-weight: bold;">⏳ 待完成</span>'
        )
    is_completed_display.short_description = '完成状态'
    is_completed_display.admin_order_field = 'is_completed'

    # 优化查询性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'maternity_center', 'maternity'
        )

    # 自定义操作
    actions = ['mark_as_completed', 'mark_as_pending']

    def mark_as_completed(self, request, queryset):
        """批量标记为已完成"""
        updated = queryset.update(is_completed=True)
        self.message_user(request, f"已将 {updated} 条产检提醒标记为已完成")
    mark_as_completed.short_description = "标记选中提醒为已完成"

    def mark_as_pending(self, request, queryset):
        """批量标记为待完成"""
        updated = queryset.update(is_completed=False)
        self.message_user(request, f"已将 {updated} 条产检提醒标记为待完成")
    mark_as_pending.short_description = "标记选中提醒为待完成"

    list_per_page = 25
    ordering = ('check_date', 'week')


# 未付费产妇产检信息管理
@admin.register(UnpaidMaternityPrenatalInfo)
class UnpaidMaternityPrenatalInfoAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'maternity_name', 'maternity_phone', 'expected_delivery_date',
        'created_at'
    )

    list_filter = ('expected_delivery_date', 'created_at')

    search_fields = ('maternity__name', 'maternity__phone')

    readonly_fields = ('id', 'rid', 'created_at', 'updated_at')

    date_hierarchy = 'expected_delivery_date'

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'maternity', 'expected_delivery_date')
        }),
        ('系统信息', {
            'fields': ('rid', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def maternity_name(self, obj):
        return obj.maternity.name if obj.maternity else '-'
    maternity_name.short_description = '产妇姓名'
    maternity_name.admin_order_field = 'maternity__name'

    def maternity_phone(self, obj):
        return obj.maternity.phone if obj.maternity else '-'
    maternity_phone.short_description = '联系电话'
    maternity_phone.admin_order_field = 'maternity__phone'

    # 优化查询性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity')

    list_per_page = 25
    ordering = ('-created_at',)


# 未付费会员消息中心管理
@admin.register(WxUnpaidMaternityMessage)
class WxUnpaidMaternityMessageAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'maternity_name', 'maternity_phone', 'message_type_display',
        'message_content_preview', 'is_read_display', 'creator_name',
        'created_at'
    )

    list_filter = (
        'message_type', 'is_read', 'maternity_center', 'created_at'
    )

    search_fields = (
        'maternity__name', 'maternity__phone',
        'message_content', 'creator__name'
    )

    readonly_fields = ('id', 'rid', 'created_at', 'updated_at')

    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'maternity_center', 'maternity', 'message_type')
        }),
        ('消息内容', {
            'fields': ('message_content', 'is_read','is_loaded'),
            'description': '消息的具体内容和阅读状态'
        }),
        ('创建信息', {
            'fields': ('creator', 'rid'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    # 自定义显示方法
    def maternity_name(self, obj):
        if obj.maternity:
            return obj.maternity.name
        return '-'
    maternity_name.short_description = '产妇姓名'
    maternity_name.admin_order_field = 'maternity__name'

    def maternity_phone(self, obj):
        if obj.maternity:
            return obj.maternity.phone
        return '-'
    maternity_phone.short_description = '联系电话'
    maternity_phone.admin_order_field = 'maternity__phone'

    def message_type_display(self, obj):
        """消息类型带颜色显示"""
        type_colors = {
            MessageTypeEnum.SYSTEM_MESSAGE: '#6c757d',           # 灰色
            MessageTypeEnum.MEDICAL_REMINDER: '#ffc107',        # 黄色
            MessageTypeEnum.MEDICAL_NOTICE: '#17a2b8',          # 蓝色
            MessageTypeEnum.ACTIVITY_NOTICE: '#28a745',         # 绿色
            MessageTypeEnum.ACTIVITY_REMINDER: '#fd7e14',       # 橙色
            MessageTypeEnum.APPOINTMENT_CONFIRMATION: '#20c997', # 青绿色
            MessageTypeEnum.APPOINTMENT_REMINDER: '#ffc107',    # 黄色
            MessageTypeEnum.APPOINTMENT_CHANGE: '#6f42c1',      # 紫色
            MessageTypeEnum.SERVICE_NOTICE: '#17a2b8',          # 蓝色
            MessageTypeEnum.FEEDBACK_RESULT: '#6c757d',         # 灰色
            MessageTypeEnum.URGENT_NOTICE: '#dc3545',           # 红色
            MessageTypeEnum.WARNING_MESSAGE: '#fd7e14',         # 橙色
            MessageTypeEnum.BILLING_NOTICE: '#e83e8c',          # 粉色
            MessageTypeEnum.GENERAL_NOTICE: '#6c757d',          # 灰色
        }
        color = type_colors.get(obj.message_type, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            MessageTypeEnum(obj.message_type).label
        )
    message_type_display.short_description = '消息类型'
    message_type_display.admin_order_field = 'message_type'

    def message_content_preview(self, obj):
        """消息内容预览（限制长度）"""
        if len(obj.message_content) > 50:
            return f"{obj.message_content[:50]}..."
        return obj.message_content
    message_content_preview.short_description = '消息内容'

    def is_read_display(self, obj):
        """已读状态显示"""
        if obj.is_read:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">✓ 已读</span>'
            )
        return format_html(
            '<span style="color: #dc3545; font-weight: bold;">✗ 未读</span>'
        )
    is_read_display.short_description = '阅读状态'
    is_read_display.admin_order_field = 'is_read'

    def creator_name(self, obj):
        if obj.creator:
            return obj.creator.name
        return '系统'
    creator_name.short_description = '创建人'
    creator_name.admin_order_field = 'creator__name'

    # 优化查询性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'maternity_center', 'maternity', 'creator'
        )

    # 自定义操作
    actions = ['mark_as_read', 'mark_as_unread', 'delete_selected_messages']

    def mark_as_read(self, request, queryset):
        """批量标记为已读"""
        updated = queryset.update(is_read=True)
        self.message_user(request, f"已将 {updated} 条消息标记为已读")
    mark_as_read.short_description = "标记选中消息为已读"

    def mark_as_unread(self, request, queryset):
        """批量标记为未读"""
        updated = queryset.update(is_read=False)
        self.message_user(request, f"已将 {updated} 条消息标记为未读")
    mark_as_unread.short_description = "标记选中消息为未读"

    def delete_selected_messages(self, request, queryset):
        """批量删除消息"""
        count = queryset.count()
        queryset.delete()
        self.message_user(request, f"已删除 {count} 条消息")
    delete_selected_messages.short_description = "删除选中的消息"

    # 分页设置
    list_per_page = 25
    ordering = ('-created_at',)

    # 添加自定义过滤器
    def get_list_filter(self, request):
        filters = list(self.list_filter)
        # 可以根据用户权限动态调整过滤器
        # 例如：if not request.user.is_superuser: filters.remove('maternity_center')
        return filters


# 公共健康知识管理
@admin.register(PublicHealthKnowledge)
class PublicHealthKnowledgeAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'title', 'summary_preview', 'created_at', 'updated_at'
    )

    list_filter = ('created_at', 'updated_at')

    search_fields = ('title', 'summary', 'content')

    readonly_fields = ('id', 'rid', 'created_at', 'updated_at')

    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'title', 'summary')
        }),
        ('内容', {
            'fields': ('content',),
            'description': '健康知识的详细内容'
        }),
        ('系统信息', {
            'fields': ('rid', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def summary_preview(self, obj):
        if len(obj.summary) > 100:
            return f"{obj.summary[:100]}..."
        return obj.summary
    summary_preview.short_description = '摘要预览'




# 月子中心健康知识管理
@admin.register(HealthKnowledge)
class HealthKnowledgeAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'title', 'maternity_center_name', 'summary_preview',
        'created_at', 'updated_at'
    )

    list_filter = ('maternity_center', 'created_at', 'updated_at')

    search_fields = ('title', 'summary', 'content', 'maternity_center__name')

    readonly_fields = ('id', 'rid', 'created_at', 'updated_at')

    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'maternity_center', 'title', 'summary')
        }),
        ('内容', {
            'fields': ('content',),
            'description': '健康知识的详细内容'
        }),
        ('系统信息', {
            'fields': ('rid', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def maternity_center_name(self, obj):
        return obj.maternity_center.name if obj.maternity_center else '-'
    maternity_center_name.short_description = '月子中心'
    maternity_center_name.admin_order_field = 'maternity_center__name'

    def summary_preview(self, obj):
        if len(obj.summary) > 100:
            return f"{obj.summary[:100]}..."
        return obj.summary
    summary_preview.short_description = '摘要预览'

    # 优化查询性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity_center')





    ordering = ('-created_at',)

