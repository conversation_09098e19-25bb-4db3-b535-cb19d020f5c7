<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <title>文件不存在</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            line-height: 1.6;
        }

        .error-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }

        .error-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        }

        .error-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .error-icon::before {
            content: '📄';
            font-size: 48px;
            opacity: 0.3;
        }

        .error-icon::after {
            content: '❌';
            font-size: 32px;
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
        }

        .error-message {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .error-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #ff9a9e;
        }

        .error-details h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .error-details ul {
            list-style: none;
            text-align: left;
        }

        .error-details li {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .error-details li::before {
            content: '•';
            color: #ff9a9e;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 154, 158, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .footer-info {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #adb5bd;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .error-container {
                padding: 40px 20px;
                margin: 20px;
            }

            .error-title {
                font-size: 24px;
            }

            .error-icon {
                width: 100px;
                height: 100px;
            }

            .error-icon::before {
                font-size: 40px;
            }

            .error-icon::after {
                font-size: 24px;
                width: 32px;
                height: 32px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* 无障碍访问 */
        @media (prefers-reduced-motion: reduce) {
            .btn {
                transition: none;
            }
        }

        /* 高对比度模式 */
        @media (prefers-contrast: high) {
            .error-container {
                border: 2px solid #000;
            }
            
            .btn-primary {
                background: #000;
                border: 2px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon" role="img" aria-label="文件不存在图标"></div>
        
        <h1 class="error-title">抱歉，文件不存在</h1>
        
        <p class="error-message">
            您访问的资源可能已被删除、移动或暂时不可用。<br>
        </p>

        <div class="error-details">
            <h4>可能的原因</h4>
            <ul>
                <li>文件已被管理员删除或移动</li>
                <li>文件链接已过期或无效</li>
                <li>存储服务暂时不可用</li>
                <li>您没有访问该文件的权限</li>
            </ul>
        </div>


        <div class="footer-info">
            <p>请检查链接是否正确，如果问题持续存在，请联系技术支持</p>
        </div>
    </div>

    <script>
        // 记录错误信息用于调试
        console.warn('File not found error page loaded at:', new Date().toISOString());
        
        // 如果是通过直接访问进入的页面，提供额外的导航选项
        if (document.referrer === '') {
            const backButton = document.querySelector('.btn-secondary');
            backButton.style.display = 'none';
        }

        // 添加键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                history.back();
            }
        });

        // 自动聚焦到主要操作按钮，提升无障碍体验
        window.addEventListener('load', function() {
            const primaryButton = document.querySelector('.btn-primary');
            if (primaryButton) {
                primaryButton.focus();
            }
        });

    </script>
</body>
</html>
