from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from permissions.enum import PermissionEnum
from permissions.models import StaffRole


# 员工角色权限序列化器
class StaffRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffRole
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at")

        
        
# 角色列表序列化器
class RoleListSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffRole
        exclude = ['maternity_center','id']
        
# 角色详情序列化器
class RoleDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
        
    class Meta:
        model = StaffRole   
        exclude = ['maternity_center','id']

# 角色更新序列化器
class RoleUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffRole
        fields = ['name', 'description', 'permissions']
        
    # 验证权限
    def validate_permissions(self, value):
        if not value:  
            raise serializers.ValidationError("权限不能为空")
            
        valid_permissions = {permission.value for permission in PermissionEnum}
        invalid_permissions = set(value) - valid_permissions
        
        if invalid_permissions:
            raise serializers.ValidationError(f"无效的权限: {', '.join(invalid_permissions)}")
            
        return value


