from django.db import models

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from hospital.models import Hospital
from maternity_center.models import MaternityCenter
from permissions.enum import PermissionEnum


# 角色模型
class StaffRole(BaseModel):
    # 角色名称
    name = models.CharField(max_length=100, verbose_name="角色名称")
    # 角色描述
    description = models.TextField(blank=True, null=True, verbose_name="角色描述")
    # 权限 - 使用JSONField存储权限代码列表
    permissions = models.JSONField(default=list, verbose_name="权限")
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name="user_roles")
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "角色"
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return self.name
    
    def has_permission(self, permission):
        if PermissionEnum.SUPER_ADMIN in self.permissions:
            return True
        print(f"DEBUG: StaffRole.has_permission - Permission: {permission}, Permissions: {self.permissions}")
        return permission in self.permissions
    
    @classmethod
    def get_role_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
    
    