from django.db import models

# 权限枚举
class PermissionEnum(models.TextChoices):
    
    # 超级管理员
    SUPER_ADMIN = 'super.admin', '超级管理员'
    
    # dashboard 查看
    DASHBOARD_VIEW = 'main.dashboard.view', '查看总览'
    
    # 文件上传
    FILE_UPLOAD = 'cus.file.upload', '文件上传'
    
    # 房态总览查看
    ROOM_STATUS_VIEW = 'cus.housekeeping.room.status.view', '查看房态总览'
    # 房态总览编辑
    ROOM_STATUS_EDIT = 'cus.housekeeping.room.status.edit', '编辑房态总览'
    
    # 入住管理查看
    CHECK_IN_VIEW = 'cus.housekeeping.check_in.view', '查看入住管理'
    # 入住管理编辑
    CHECK_IN_EDIT = 'cus.housekeeping.check_in.edit', '编辑入住管理'
    
    # 退房管理查看
    CHECK_OUT_VIEW = 'cus.housekeeping.check_out.view', '查看退房管理'
    # 退房管理编辑
    CHECK_OUT_EDIT = 'cus.housekeeping.check_out.edit', '编辑退房管理'
    
    # 房间管理查看
    ROOM_VIEW = 'cus.housekeeping.room.view', '查看房间管理'
    # 房间管理编辑
    ROOM_EDIT = 'cus.housekeeping.room.edit', '编辑房间管理'
    
    # 换房管理查看
    ROOM_CHANGE_VIEW = 'cus.housekeeping.room_change.view', '查看换房管理'
    # 换房管理编辑
    ROOM_CHANGE_EDIT = 'cus.housekeeping.room_change.edit', '编辑换房管理'
    
    # 外出管理查看
    OUT_VIEW = 'cus.housekeeping.out.view', '查看外出管理'
    # 外出管理编辑
    OUT_EDIT = 'cus.housekeeping.out.edit', '编辑外出管理'

    # 院内转诊 查看
    MEDICAL_REFERRAL_VIEW = 'cus.medical_referral.view', '查看院内转诊'
    # 院内转诊 编辑
    MEDICAL_REFERRAL_EDIT = 'cus.medical_referral.edit', '编辑院内转诊'

    # 膳食管理查看  
    DIET_VIEW = 'cus.diet.view', '查看膳食管理'
    # 膳食管理编辑
    DIET_EDIT = 'cus.diet.edit', '编辑膳食管理'

    # 母婴核心记录查看
    MATERNAL_CORE_RECORD_VIEW = 'cus.maternal_baby_core_records.view', '查看母婴核心记录'
    # 母婴核心记录编辑
    MATERNAL_CORE_RECORD_EDIT = 'cus.maternal_baby_core_records.edit', '编辑母婴核心记录'

    # 活动管理查看
    ACTIVITY_VIEW = 'cus.activity.view', '查看活动管理'
    # 活动管理编辑
    ACTIVITY_EDIT = 'cus.activity.edit', '编辑活动管理'

    # 健康宣教查看
    HEALTH_EDUCATION_VIEW = 'cus.health.education.view', '查看健康宣教'
    # 健康宣教编辑
    HEALTH_EDUCATION_EDIT = 'cus.health.education.edit', '编辑健康宣教'
    
    # 访客管理查看
    VISITOR_VIEW = 'cus.visitor.view', '查看访客管理'
    # 访客管理编辑
    VISITOR_EDIT = 'cus.visitor.edit', '编辑访客管理'
    
    # 消毒管理
    DISINFECTION_VIEW = 'cus.disinfection.view', '查看消毒管理'
    # 消毒管理编辑
    DISINFECTION_EDIT = 'cus.disinfection.edit', '编辑消毒管理'
    
    # 产后康复查看
    POSTPARTUM_VIEW = 'cus.postpartum.view', '查看产后康复'
    # 产后康复编辑
    POSTPARTUM_EDIT = 'cus.postpartum.edit', '编辑产后康复'
    
    # 查房管理
    WARD_ROUND_VIEW = 'cus.ward_round.view', '查看查房管理'
    # 查房管理编辑
    WARD_ROUND_EDIT = 'cus.ward_round.edit', '编辑查房管理'

    # 产妇用户管理
    # 产妇用户管理查看
    MATERNITY_USER_VIEW = 'ogm.maternity.user.view', '查看产妇用户管理'
    # 产妇用户管理编辑
    MATERNITY_USER_EDIT = 'ogm.maternity.user.edit', '编辑产妇用户管理'

    # 员工与排班管理查看
    # 员工管理查看
    STAFF_VIEW = 'ogm.staff_schedule.staff.view', '查看员工管理'
    # 员工管理编辑
    STAFF_EDIT = 'ogm.staff_schedule.staff.edit', '编辑员工管理'
    
    # 值班管理查看
    SCHEDULE_VIEW = 'ogm.staff_schedule.schedule.view', '查看值班管理'
    # 值班管理编辑
    SCHEDULE_EDIT = 'ogm.staff_schedule.schedule.edit', '编辑值班管理'
    
    # 值班与交接班管理
    # 值班管理查看
    DUTY_VIEW = 'ogm.duty_shift.duty.view', '查看值班管理'
    # 值班管理编辑
    DUTY_EDIT = 'ogm.duty_shift.duty.edit', '编辑值班管理'
    
    # 交接班管理查看
    SHIFT_VIEW = 'ogm.duty_shift.shift.view', '查看交接班管理'
    # 交接班管理编辑
    SHIFT_EDIT = 'ogm.duty_shift.shift.edit', '编辑交接班管理'
    
    
    # 收费管理
    # 套餐价格管理查看
    PACKAGE_PRICE_VIEW = 'ogm.charge.package_price.view', '查看套餐价格'
    # 套餐价格管理编辑
    PACKAGE_PRICE_EDIT = 'ogm.charge.package_price.edit', '编辑套餐价格'
    
    # 结算单管理查看
    BILL_VIEW = 'ogm.charge.bill.view', '查看结算单管理'
    # 结算单管理编辑
    BILL_EDIT = 'ogm.charge.bill.edit', '编辑结算单管理'
    
    # 财务统计查看
    FINANCIAL_STATISTICS_VIEW = 'ogm.charge.financial_statistics.view', '查看财务统计'
    
    
    # 院感与环境管理
    # 院感与环境管理查看
    INFECTION_CONTROL_AND_ENVIRONMENT_VIEW = 'ogm.infection_control.view', '查看院感与环境管理'
    # 院感与环境管理编辑
    INFECTION_CONTROL_AND_ENVIRONMENT_EDIT = 'ogm.infection_control.edit', '编辑院感与环境管理'
    
    # 客户反馈满意度调查
    # 客户反馈查看
    CUSTOMER_FEEDBACK_SURVEY_VIEW = 'ogm.feedback.survey.view', '查看客户反馈满意度调查'
    # 客户反馈编辑
    CUSTOMER_FEEDBACK_SURVEY_EDIT = 'ogm.feedback.survey.edit', '编辑客户反馈满意度调查'
    
    # 问卷管理
    # 问卷管理查看
    QUESTIONNAIRE_VIEW = 'ogm.feedback.questionnaire.view', '查看问卷管理'
    # 问卷管理编辑
    QUESTIONNAIRE_EDIT = 'ogm.feedback.questionnaire.edit', '编辑问卷管理'
    
    # 客户问卷查看
    CUSTOMER_QUESTIONNAIRE_VIEW = 'ogm.feedback.customer_questionnaire.view', '查看客户问卷'
    # 客户问卷编辑
    CUSTOMER_QUESTIONNAIRE_EDIT = 'ogm.feedback.customer_questionnaire.edit', '编辑客户问卷'
    
    # 统计报告查看
    STATISTIC_REPORT_VIEW = 'ogm.feedback.statistic_report.view', '查看统计报告'
    
    # 设备管理
    # 设备管理查看
    EQUIPMENT_VIEW = 'ogm.equipment.view', '查看设备管理'
    # 设备管理编辑
    EQUIPMENT_EDIT = 'ogm.equipment.edit', '编辑设备管理'
    
    # 报表与导出中心查看
    REPORT_AND_EXPORT_CENTER_VIEW = 'ogm.report_export_center.view', '查看报表与导出中心'
    # 报表与导出中心编辑
    REPORT_AND_EXPORT_CENTER_EDIT = 'ogm.report_export_center.edit', '编辑报表与导出中心'
    
    # 角色权限管理
    ROLE_VIEW = 'sys.role.view', '查看角色权限管理'
    # 角色权限管理编辑
    ROLE_EDIT = 'sys.role.edit', '编辑角色权限管理'
    
    # 审计日志
    AUDIT_LOG_VIEW = 'sys.audit_log.view', '查看审计日志'
    # 审计日志编辑
    AUDIT_LOG_EDIT = 'sys.audit_log.edit', '编辑审计日志'
    
    # 微信小程序管理
    WECHAT_APP_VIEW = 'sys.wechat_app.view', '查看微信小程序数据'
    # 微信小程序编辑
    WECHAT_APP_EDIT = 'sys.wechat_app.edit', '编辑微信小程序数据'
    
    
