from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.resp import make_response
from core.view import PaginationListBaseView
from permissions.enum import PermissionEnum
from permissions.models import StaffRole
from permissions.serializers import RoleDetailSerializer, RoleListSerializer, StaffRoleSerializer, RoleUpdateSerializer


# 基类
class RoleBaseApiView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROLE_VIEW
    
    def _check_permissions(self, permissions):
        
        if not permissions:
            return make_response(code=-1, msg="权限不能为空")
        
        valid_permissions = {permission.value for permission in PermissionEnum}
        invalid_permissions = set(permissions) - valid_permissions
        
        if invalid_permissions:
            return make_response(code=-1, msg=f"无效的权限: {', '.join(invalid_permissions)}")
        
        return None


class PermissionListView(RoleBaseApiView):

    # 权限列表

    def get(self, request):
        categories = {
            '超级管理员': [],
            '总览': [],
            '客服中心': {
                '房务管理': {
                    '房态总览': [],
                    '入住管理': [],
                    '退房管理': [],
                    '房间管理': [],
                    '换房管理': [],
                    '外出管理': []
                },
                '院内转诊': [],
                '膳食管理': [],
                '母婴核心记录': [],
                '活动管理': [],
                '健康宣教': [],
                '访客管理': [],
                '消毒管理': [],
                '产后康复': [],
                '查房管理': [],
                '文件上传': []
            },
            '机构管理': {
                '产妇用户管理': [],
                '员工与排班管理': {
                    '员工管理': [],
                    '排班管理': []
                },
                '值班与交接班管理': {
                    '值班管理': [],
                    '交接班管理': []
                },
                '收费管理': {
                    '套餐价格管理': [],
                    '结算单管理': [],
                    '财务统计': []
                },
                '院感与环境管理': [],
                '客户反馈满意度调查': {
                    '客户反馈调查': [],
                    '问卷管理': [],
                    '客户问卷': [],
                    '统计报告': []
                },
                '设备管理': [],
                '报表与导出中心': []
            },
            '后台管理': {
                '角色权限管理': [],
                '审计日志': [],
                '微信小程序管理': []
            },
        }
        
        for permission in PermissionEnum:
            permission_data = {'value': permission.value, 'label': permission.label}
            
            if permission.value == PermissionEnum.SUPER_ADMIN.value:
                categories['超级管理员'].append(permission_data)
            elif permission.value.startswith('main.'):
                categories['总览'].append(permission_data)
            elif permission.value.startswith('cus.'):
                if permission.value.startswith('cus.housekeeping.'):
                    if permission.value.startswith('cus.housekeeping.room.status.'):
                        categories['客服中心']['房务管理']['房态总览'].append(permission_data)
                    elif permission.value.startswith('cus.housekeeping.check_in.'):
                        categories['客服中心']['房务管理']['入住管理'].append(permission_data)
                    elif permission.value.startswith('cus.housekeeping.check_out.'):
                        categories['客服中心']['房务管理']['退房管理'].append(permission_data)
                    elif permission.value.startswith('cus.housekeeping.room.') and not permission.value.startswith('cus.housekeeping.room.status.'):
                        categories['客服中心']['房务管理']['房间管理'].append(permission_data)
                    elif permission.value.startswith('cus.housekeeping.room_change.'):
                        categories['客服中心']['房务管理']['换房管理'].append(permission_data)
                    elif permission.value.startswith('cus.housekeeping.out.'):
                        categories['客服中心']['房务管理']['外出管理'].append(permission_data)
                elif permission.value.startswith('cus.medical_referral.'):
                    categories['客服中心']['院内转诊'].append(permission_data)
                elif permission.value.startswith('cus.diet.'):
                    categories['客服中心']['膳食管理'].append(permission_data)
                elif permission.value.startswith('cus.maternal_baby_core_records.'):
                    categories['客服中心']['母婴核心记录'].append(permission_data)
                elif permission.value.startswith('cus.activity.'):
                    categories['客服中心']['活动管理'].append(permission_data)
                elif permission.value.startswith('cus.health.education.'):
                    categories['客服中心']['健康宣教'].append(permission_data)
                elif permission.value.startswith('cus.visitor.'):
                    categories['客服中心']['访客管理'].append(permission_data)
                elif permission.value.startswith('cus.disinfection.'):
                    categories['客服中心']['消毒管理'].append(permission_data)
                elif permission.value.startswith('cus.postpartum.'):
                    categories['客服中心']['产后康复'].append(permission_data)
                elif permission.value.startswith('cus.check_room.'):
                    categories['客服中心']['查房管理'].append(permission_data)
                elif permission.value.startswith('cus.file.'):
                    categories['客服中心']['文件上传'].append(permission_data)
            elif permission.value.startswith('ogm.'):
                if permission.value.startswith('ogm.maternity.user.'):
                    categories['机构管理']['产妇用户管理'].append(permission_data)
                if permission.value.startswith('ogm.staff_schedule.'):
                    if permission.value.startswith('ogm.staff_schedule.staff.'):
                        categories['机构管理']['员工与排班管理']['员工管理'].append(permission_data)
                    elif permission.value.startswith('ogm.staff_schedule.schedule.'):
                        categories['机构管理']['员工与排班管理']['排班管理'].append(permission_data)
                elif permission.value.startswith('ogm.duty_shift.'):
                    if permission.value.startswith('ogm.duty_shift.duty.'):
                        categories['机构管理']['值班与交接班管理']['值班管理'].append(permission_data)
                    elif permission.value.startswith('ogm.duty_shift.shift.'):
                        categories['机构管理']['值班与交接班管理']['交接班管理'].append(permission_data)
                elif permission.value.startswith('ogm.charge.'):
                    if permission.value.startswith('ogm.charge.package_price.'):
                        categories['机构管理']['收费管理']['套餐价格管理'].append(permission_data)
                    elif permission.value.startswith('ogm.charge.bill.'):
                        categories['机构管理']['收费管理']['结算单管理'].append(permission_data)
                    elif permission.value.startswith('ogm.charge.financial_statistics.'):
                        categories['机构管理']['收费管理']['财务统计'].append(permission_data)
                elif permission.value.startswith('ogm.infection_control.'):
                    categories['机构管理']['院感与环境管理'].append(permission_data)
                elif permission.value.startswith('ogm.feedback.'):
                    if permission.value.startswith('ogm.feedback.survey.'):
                        categories['机构管理']['客户反馈满意度调查']['客户反馈调查'].append(permission_data)
                    elif permission.value.startswith('ogm.feedback.questionnaire.'):
                        categories['机构管理']['客户反馈满意度调查']['问卷管理'].append(permission_data)
                    elif permission.value.startswith('ogm.feedback.customer_questionnaire.'):
                        categories['机构管理']['客户反馈满意度调查']['客户问卷'].append(permission_data)
                    elif permission.value.startswith('ogm.feedback.statistic_report.'):
                        categories['机构管理']['客户反馈满意度调查']['统计报告'].append(permission_data)
                elif permission.value.startswith('ogm.equipment.'):
                    categories['机构管理']['设备管理'].append(permission_data)
                elif permission.value.startswith('ogm.report_export_center.'):
                    categories['机构管理']['报表与导出中心'].append(permission_data)
            elif permission.value.startswith('sys.'):
                if permission.value.startswith('sys.role.'):
                    categories['后台管理']['角色权限管理'].append(permission_data)
                elif permission.value.startswith('sys.audit_log.'):
                    categories['后台管理']['审计日志'].append(permission_data)
                elif permission.value.startswith('sys.wechat_app.'):
                    categories['后台管理']['微信小程序管理'].append(permission_data)
        
        def build_category_structure(category_data):
            if isinstance(category_data, dict):
                subcategories = []
                for subcategory, subdata in category_data.items():
                    if isinstance(subdata, dict):
                        # 处理嵌套的子分类（如房务管理）
                        nested_subcategories = []
                        for nested_subcategory, nested_permissions in subdata.items():
                            if nested_permissions:
                                nested_subcategories.append({
                                    'category': nested_subcategory,
                                    'permissions': nested_permissions
                                })
                        if nested_subcategories:
                            subcategories.append({
                                'category': subcategory,
                                'subcategories': nested_subcategories
                            })
                    else:
                        # 处理普通的子分类
                        if subdata:
                            subcategories.append({
                                'category': subcategory,
                                'permissions': subdata
                            })
                return subcategories
            else:
                return category_data
        
        result = []
        for category, permissions in categories.items():
            if isinstance(permissions, dict):
                subcategories = build_category_structure(permissions)
                if subcategories:
                    result.append({
                        'category': category,
                        'subcategories': subcategories
                    })
            else:
                if permissions:
                    result.append({
                        'category': category,
                        'permissions': permissions
                    })
        
        return make_response(result)





class RoleListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = [PermissionEnum.ROLE_VIEW,PermissionEnum.ROLE_EDIT]
    serializer_class = RoleListSerializer
    response_msg = "获取角色列表成功"
    error_response_msg = ""
    search_fields = ['name']
    
    def get_queryset(self):
        return StaffRole.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')


class RoleDetailView(RoleBaseApiView):

    #员工角色详情

    def get(self, request, rid):
        try:
            role = StaffRole.objects.get(rid=rid,maternity_center=request.user.maternity_center)
            serializer = RoleDetailSerializer(role)
            return make_response(code=0, msg="获取角色详情成功", data=serializer.data)
        except StaffRole.DoesNotExist:
            return make_response(code=-1, msg="角色不存在")
    
    
    
class RoleCreateView(RoleBaseApiView):

    #员工角色创建
    staff_required_permission = PermissionEnum.ROLE_EDIT

    def post(self, request):
        
        request.data['maternity_center'] = request.user.maternity_center.id
        
        error = self._check_permissions(request.data.get('permissions',[]))
        if error:
            return error
        
        serializer = StaffRoleSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="角色创建成功", data=RoleDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1, msg=serializer.errors)

class RoleUpdateView(RoleBaseApiView):

    #员工角色更新
    staff_required_permission = PermissionEnum.ROLE_EDIT
    
    def put(self, request, rid):
        try:
            role = StaffRole.objects.get(rid=rid, maternity_center=request.user.maternity_center)
        except StaffRole.DoesNotExist:
            return make_response(code=-1, msg="角色不存在")
        
        error = self._check_permissions(request.data.get('permissions',[]))
        if error:
            return error
        
        serializer = RoleUpdateSerializer(role, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="角色更新成功", data=RoleDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1, msg="数据校验失败")
        

class RoleDeleteView(RoleBaseApiView):

    #员工角色删除
    staff_required_permission = PermissionEnum.ROLE_EDIT
    
    def delete(self, request, rid):
        try:
            role = StaffRole.objects.get(rid=rid, maternity_center=request.user.maternity_center)
        except StaffRole.DoesNotExist:
            return make_response(code=-1, msg="角色不存在")
        
        if role.staffs.count() > 0:
            return make_response(code=-1, msg="该角色已被员工使用，请先解除员工与该角色的关联后再尝试删除")
                
        role.delete()
        return make_response(code=0, msg="角色删除成功", data=None)


# # 权限列表
# class NormalPermissionListView(APIView):
#     """
#     权限验证说明：
#     1. 无需权限即可访问
#     """
#     authentication_classes = []
#     permission_classes = []
    
#     # 获取权限列表
#     def get(self, request):
#         permissions = [permission.value for permission in PermissionEnum]
#         return make_response(permissions)


# # 权限列表
# class PermissionListView(APIView):
#     """
#     权限验证说明：
#     1.CareCenterAuthentication - Token 验证
#     2.MaternityOrStaffWithPermission - 产妇或员工且有特定权限可访问
#     3.staff_required_permission - 特定员工权限  
#     """
#     authentication_classes = [CareCenterAuthentication]
#     permission_classes = [MaternityOrStaffWithPermission]
#     # 权限码
#     staff_required_permission = PermissionEnum.DASHBOARD_VIEW
    
#     # 获取权限列表
#     def get(self, request):
#         permissions = [permission.value for permission in PermissionEnum]
#         return make_response(permissions)


# # 权限列表
# class StaffOnlyPermissionListView(APIView):
#     """
#     权限验证说明：
#     1.CareCenterAuthentication - Token 验证
#     2.StaffWithSpecificPermissionOnly - 仅员工且有特定权限可访问
#     3.staff_required_permission - 特定员工权限  
#     """
#     authentication_classes = [CareCenterAuthentication]
#     permission_classes = [StaffWithSpecificPermissionOnly]
#     # 权限码
#     staff_required_permission = PermissionEnum.DASHBOARD_VIEW
    
#     # 获取权限列表
#     def get(self, request):
#         permissions = [permission.value for permission in PermissionEnum]
#         return make_response(permissions)