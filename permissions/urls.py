from django.urls import path

from .views import PermissionListView, RoleListView, RoleDetailView, RoleCreateView, RoleUpdateView, RoleDeleteView

urlpatterns = [
    # 所有权限列表
    path('permission-list/', PermissionListView.as_view(), name='permission-list'),
    # 员工角色列表
    path('role-list/', RoleListView.as_view(), name='role-list'),
    # 员工角色详情
    path('role-detail/<str:rid>/', RoleDetailView.as_view(), name='role-detail'),
    # 员工角色创建
    path('role-create/', RoleCreateView.as_view(), name='role-create'),
    # 员工角色更新
    path('role-update/<str:rid>/', RoleUpdateView.as_view(), name='role-update'),
    # 员工角色删除
    path('role-delete/<str:rid>/', RoleDeleteView.as_view(), name='role-delete'),
    
]
