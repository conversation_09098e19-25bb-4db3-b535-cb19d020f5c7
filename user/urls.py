from django.urls import path

from .views import LoginView, MaternityCreateView, MaternityListView, MaternityUpdateView, PermissionView, \
        VerifyCodeView, WechatAuthView, WechatVerifyCodeView

urlpatterns = [
    # 登录
    path('login/', LoginView.as_view(), name='login'),
    # 验证短信验证码
    path('verify-code/', VerifyCodeView.as_view(), name='verify_code'),
    # 角色与权限列表
    path('permission/', PermissionView.as_view(), name='permission'),
    
    
    # 微信授权手机号码交换
    path('wechat/auth/', WechatAuthView.as_view(), name='wechat_auth'),
    
    # 微信验证短信验证码
    path('wechat/verify-code/', WechatVerifyCodeView.as_view(), name='wechat_verify_code'),
    
    
    # 创建产妇
    path('maternity/create/', MaternityCreateView.as_view(), name='maternity_create'),
    # 更新产妇
    path('maternity/update/<str:uid>/', MaternityUpdateView.as_view(), name='maternity_update'),
    # 产妇列表
    path('maternity/list/', MaternityListView.as_view(), name='maternity_list'),
    

]