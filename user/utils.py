from core.resp import make_response
from user.models import Maternity

def check_maternity_duplicates(phone, identity_number, maternity_center, exclude_id=None):
        from django.db.models import Q

        conditions = []
        if phone:
            conditions.append(Q(phone=phone))
        if identity_number:
            conditions.append(Q(identity_number=identity_number))

        if not conditions:
            return None

        query = Q()
        for condition in conditions:
            query |= condition
        query &= Q(maternity_center=maternity_center)

        if exclude_id:
            query &= ~Q(id=exclude_id)

        existing = Maternity.objects.filter(query).values('phone', 'identity_number')
        if not existing:
            return None

        for record in existing:
            if phone and record['phone'] == phone:
                return make_response(code=-1, msg=f"电话号码 {phone} 已被使用")
            if identity_number and record['identity_number'] == identity_number:
                return make_response(code=-1, msg=f"身份证号 {identity_number} 已被使用")

        return None