from django import forms
from django.contrib import admin

from user.models import Department, Staff, Maternity


# 自定义Staff表单，专门处理密码字段
class StaffAdminForm(forms.ModelForm):
    password = forms.CharField(
        label='密码', 
        widget=forms.PasswordInput, 
        required=False,
        help_text='留空表示不修改密码'
    )
    
    class Meta:
        model = Staff
        fields = '__all__'

# 工作人员
@admin.register(Staff)
class StaffAdmin(admin.ModelAdmin):
    form = StaffAdminForm
    list_display = ('id', 'name', 'phone', 'role', 'created_at', 'updated_at')
    search_fields = ('name', 'phone')
    list_filter = ('role', 'created_at', 'updated_at')
    exclude = ('password',)  # 在详情页的字段列表中排除原始密码字段
    
    def save_model(self, request, obj, form, change):
        """覆盖保存方法，处理密码加密"""
        # 获取表单中的密码值
        password = form.cleaned_data.get('password')
        # 如果提供了新密码（非空），则使用set_password方法加密
        if password:
            obj.set_password(password)
        super().save_model(request, obj, form, change)

# 孕产妇
@admin.register(Maternity)
class MaternityAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'phone', 'created_at', 'updated_at')
    search_fields = ('name', 'phone')
    list_filter = ('created_at', 'updated_at')




@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'maternity_center', 'created_at', 'updated_at')
    search_fields = ('name',)
    list_filter = ('maternity_center', 'created_at', 'updated_at')
