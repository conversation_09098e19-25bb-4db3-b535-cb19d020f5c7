from django.db import models

# 产妇在院状态枚举
class MaternityInHospitalStatusEnum(models.TextChoices):
    # 已预定
    RESERVED = "RESERVED", "已预定"
    # 已入住
    CHECKED_IN = "CHECKED_IN", "已入住"
    # 已退房
    CHECKED_OUT = "CHECKED_OUT", "已退房"
    # 已转诊
    REFERRAL = "REFERRAL", "已转诊"

# 紧急联系人枚举
class EmergencyContactEnum(models.TextChoices):
    # 未知
    UNKNOWN = "UNKNOWN", "未知"
    # 父亲
    FATHER = "FATHER", "父亲"
    # 母亲
    MOTHER = "MOTHER", "母亲"
    # 配偶
    SPOUSE = "SPOUSE", "配偶"
    # 孩子
    CHILDREN = "CHILDREN", "孩子"
    # 亲戚
    RELATIVE = "RELATIVE", "亲戚"
    # 朋友
    FRIEND = "FRIEND", "朋友"
    # 其他
    OTHER = "OTHER", "其他"
    