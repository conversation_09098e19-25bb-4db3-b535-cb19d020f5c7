from django.db import transaction
from django.utils import timezone
from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from maternity_center.serializers import MaternityCenterSerializer
from permissions.serializers import StaffRoleSerializer
from user.models import Department, Maternity, Staff


# 产妇用户脱敏信息序列化器
class MaternityDesensitizedSerializer(serializers.ModelSerializer):
    
    # 性别显示
    gender_display = serializers.SerializerMethodField()

    class Meta:
        model = Maternity
        fields = ['uid','name','phone','gender','gender_display','birth_date','native_place','home_address']
    
    def get_gender_display(self, obj):
        return obj.get_gender_display()


# 产妇用户序列化器
class MaternitySerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 最后登录时间
    last_login = ShanghaiFriendlyDateTimeField()
    # 性别显示
    gender_display = serializers.SerializerMethodField()
    # 紧急联系人关系显示
    emergency_contact_relation_display = serializers.SerializerMethodField()

    class Meta:
        model = Maternity
        exclude = ['password','id','maternity_center']
    
    def get_gender_display(self, obj):
        return obj.get_gender_display()

    def get_emergency_contact_relation_display(self, obj):
        return obj.get_emergency_contact_relation_display()


# 产妇创建序列化器
class MaternityCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Maternity
        fields = ['name', 'phone','birth_date','ethnicity','native_place','gender','blood_type','home_address','maternity_center','identity_number','emergency_contact','emergency_contact_phone','emergency_contact_relation']

# 产妇更新序列化器
class MaternityUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Maternity
        fields = ['name', 'phone','birth_date','ethnicity','native_place','gender','blood_type','home_address','identity_number','emergency_contact','emergency_contact_phone','emergency_contact_relation']
                

# staff 列表序列化器
class StaffListSerializer(serializers.ModelSerializer):
    # 入职日期
    hire_date = serializers.SerializerMethodField()
    # 性别显示
    gender_display = serializers.SerializerMethodField()
    # 在职状态显示
    is_active_display = serializers.SerializerMethodField()
    # 部门显示
    department_display = serializers.SerializerMethodField()
    # 角色显示
    role_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Staff
        fields = ['sid', 'staff_number','name','phone','hire_date','is_active_display','department_display','position','role_display','gender_display']
        
    def get_hire_date(self, obj):
        if obj.hire_date:
            return obj.hire_date.strftime("%Y-%m-%d")
        return None

    def get_gender_display(self, obj):
        return obj.get_gender_display()

    def get_is_active_display(self, obj):
        return "在职" if obj.is_active else "离职"  

    def get_department_display(self, obj):
        if obj.department:
            return obj.department.name
        return None
    
    
    def get_role_display(self, obj):
        if obj.role:
            return obj.role.name
        return None
    
    
    
# staff 序列化器
class StaffSerializer(serializers.ModelSerializer):

    # 月子中心，排除创建和更新时间
    maternity_center = serializers.SerializerMethodField()
    # 角色
    role = serializers.SerializerMethodField()
    # 部门
    department = serializers.SerializerMethodField()
    # 最后登录时间
    last_login = serializers.SerializerMethodField()
    # 性别显示
    gender_display = serializers.SerializerMethodField()
    # 血型显示
    blood_type_display = serializers.SerializerMethodField()
    # 紧急联系人关系显示
    emergency_contact_relation_display = serializers.SerializerMethodField()
    # 出生日期格式化
    birth_date = serializers.SerializerMethodField()
    # 入职日期格式化
    hire_date = serializers.SerializerMethodField()
    # 合同有效期格式化
    contract_validity_date = serializers.SerializerMethodField()
    # 在职状态
    is_active_display = serializers.SerializerMethodField()

    class Meta:
        model = Staff
        fields = [
            "sid",
            "name",
            "phone",
            "birth_date",
            "identity_number",
            "ethnicity",
            "native_place",
            "gender",
            "gender_display",
            "blood_type",
            "blood_type_display",
            "home_address",
            "maternity_center",
            "emergency_contact",
            "emergency_contact_phone",
            "emergency_contact_relation",
            "emergency_contact_relation_display",
            "role",
            "staff_number",
            "department",
            "position",
            "hire_date",
            "contract_number",
            "contract_validity_date",
            "last_login",
            "is_active",
            "is_active_display",
        ]

    def get_last_login(self, obj):
        if obj.last_login:
            return timezone.localtime(obj.last_login).strftime("%Y-%m-%d %H:%M:%S")
        return None

    def get_birth_date(self, obj):
        if obj.birth_date:
            return obj.birth_date.strftime("%Y-%m-%d")
        return None

    def get_hire_date(self, obj):
        if obj.hire_date:
            return obj.hire_date.strftime("%Y-%m-%d")
        return None

    def get_contract_validity_date(self, obj):
        if obj.contract_validity_date:
            return obj.contract_validity_date.strftime("%Y-%m-%d")
        return None

    def get_gender_display(self, obj):
        return obj.get_gender_display()

    def get_blood_type_display(self, obj):
        return obj.get_blood_type_display()

    def get_emergency_contact_relation_display(self, obj):
        return obj.get_emergency_contact_relation_display()

    def get_maternity_center(self, obj):
        if obj.maternity_center:
            data = MaternityCenterSerializer(obj.maternity_center).data
            return {k: v for k, v in data.items() if k not in ["created_at", "updated_at", "id"]}
        return None

    def get_department(self, obj):
        if obj.department:
            return {
                "rid": obj.department.rid,
                "name": obj.department.name,
            }
        return None

    def get_role(self, obj):
        if obj.role:
            data = StaffRoleSerializer(obj.role).data
            return {k: v for k, v in data.items() if k not in ["created_at", "updated_at", "id", "maternity_center"]}
        return None

    def get_is_active_display(self, obj):
        return "在职" if obj.is_active else "离职"


# 员工创建序列化器
class StaffCreateSerializer(serializers.ModelSerializer):
    # 密码确认字段
    password_confirm = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = Staff
        fields = [
            'name', 'phone', 'birth_date',  'ethnicity', 'native_place','identity_number',
            'gender', 'blood_type', 'password', 'password_confirm', 'home_address',
            'maternity_center', 'emergency_contact', 'emergency_contact_phone',
            'emergency_contact_relation', 'staff_number', 'department', 'position',
            'role', 'hire_date', 'contract_number', 'contract_validity_date'
        ]
        extra_kwargs = {
            'password': {'write_only': True, 'required': False, 'allow_blank': True},
            'maternity_center': {'required': True},
        }


    @transaction.atomic
    def create(self, validated_data):
        validated_data.pop('password_confirm', None)
        password = validated_data.pop('password', '')
        staff = Staff.objects.create(**validated_data)
        if password:
            staff.set_password(password)
            staff.save()
        return staff
    

# 员工更新序列化器
class StaffUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Staff
        fields = [
            'name', 'phone', 'birth_date',  'ethnicity', 'native_place','identity_number',
            'gender', 'blood_type', 'home_address', 'emergency_contact',
            'emergency_contact_phone', 'emergency_contact_relation', 'staff_number',
            'department', 'position', 'role', 'hire_date', 'contract_number',
            'contract_validity_date', 'is_active'
        ]

    @transaction.atomic
    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


# 员工选择列表序列化器
class StaffSelectListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Staff
        fields = ['sid', 'name']
        
        
        
# 部门列表序列化器
class DepartmentListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ['rid', 'name']