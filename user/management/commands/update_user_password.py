from django.core.management import BaseCommand

from user.models import Staff


class Command(BaseCommand):
    help = 'Change user password'

    def handle(self, *args, **options):

        s = Staff.objects.create(
            name='admin',
            phone='13898786765',
            age=30,
            ethnicity='汉族',
            native_place='北京',
            gender=1,
            blood_type='A',
            home_address='北京',
            password='123456',
        )
        s.set_password('123456')
        s.save()
        # us = Staff.objects.all()
        # for u in us:
        #     u.set_password('123456')
        #     u.save()
        # print('done')
