from django.conf import settings
from rest_framework import serializers

from audit_log.enum import OperationTypeEnum
from audit_log.model_config import MODELS_CONFIG
from audit_log.models import AuditLog
from core.parse_time import parse_datetime_to_shanghai_time


# 审计日志列表序列化器
class AuditLogListSerializer(serializers.ModelSerializer):
    # 操作人员
    operator = serializers.SerializerMethodField()
    # 操作类型
    operation_type_display = serializers.SerializerMethodField()
    # 操作时间
    operation_time = serializers.SerializerMethodField()
    # extra_data
    extra_data = serializers.SerializerMethodField()
    
    
    class Meta:
        model = AuditLog
        fields = ['lid', 'operator', 'ip_address', 'operation_module', 'operation_type','operation_type_display', 'operation_detail', 'extra_data','operation_time']
        
    def get_operator(self, obj):
        return obj.operator.name if obj.operator else None
    
    def get_operation_type_display(self, obj):
        return OperationTypeEnum(obj.operation_type).label
    
    def get_operation_time(self, obj):
        return parse_datetime_to_shanghai_time(obj.created_at)
    
    def get_extra_data(self, obj):

        if obj.operation_type == OperationTypeEnum.UPLOAD and obj.extra_data:
            
            model_name = obj.extra_data.get('model')
            rid = obj.extra_data.get('rid')
            field = obj.extra_data.get('field')

            if not all([model_name, rid, field]):
                return {}

            model_class = MODELS_CONFIG.get(model_name)
            
            if not model_class:
                return {}

            try:
                
                source_obj = model_class.objects.get(rid=rid)
                field_value = getattr(source_obj, field, None)
                file_url = field_value.url if field_value else settings.FILE_NOT_FOUND_URL
                
            except model_class.DoesNotExist:
                
                file_url = settings.FILE_NOT_FOUND_URL
                
            except (AttributeError, ValueError, Exception):
                
                file_url = settings.FILE_NOT_FOUND_URL

            return {
                'url': file_url,
                'rid': rid
            }
        return obj.extra_data

