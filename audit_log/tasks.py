from celery import shared_task
from django.db import transaction
import logging

logger = logging.getLogger(__name__)

@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def create_audit_log_task(self, maternity_center_id, operator_id, ip_address,
                         operation_module, operation_type, operation_detail, extra_data=None):
    print(f"开始执行审计日志任务: {operation_module} - {operation_detail}")


    try:
        from audit_log.models import AuditLog


        # 使用事务确保数据一致性
        with transaction.atomic():

            # 创建审计日志
            audit_log = AuditLog.objects.create(
                maternity_center_id=maternity_center_id,
                operator_id=operator_id,
                ip_address=ip_address or 'N/A',
                operation_module=operation_module or 'N/A',
                operation_type=operation_type or 'N/A',
                operation_detail=operation_detail or 'N/A',
                extra_data=extra_data or {}
            )

        print(f"审计日志创建成功: {audit_log.id} - {operation_module} - {operation_detail}")
        return {"status": "success", "message": "审计日志创建成功", "log_id": audit_log.id}

    except Exception as exc:
        print(f"创建审计日志失败: {exc}, 重试次数: {self.request.retries}")

        # 如果是数据库相关错误，进行重试
        if self.request.retries < self.max_retries:
            print(f"准备重试任务，延迟 {60 * (2 ** self.request.retries)} 秒")
            raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))  # 指数退避
        else:
            # 达到最大重试次数，记录错误但不抛出异常
            print(f"审计日志创建最终失败: {operation_module} - {operation_detail}")
            return {"status": "failed", "message": str(exc)}


@shared_task
def cleanup_failed_audit_logs():

    # 这里可以实现一些清理逻辑
    
    pass
