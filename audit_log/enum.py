from django.db import models

# 操作类型枚举
class OperationTypeEnum(models.TextChoices):
    # 创建
    CREATE = "CREATE","创建"
    # 更新
    UPDATE = "UPDATE","更新"
    # 删除
    DELETE = "DELETE","删除"
    # 查询
    QUERY = "QUERY","查询"
    # 上传
    UPLOAD = "UPLOAD","文件上传"
    # 下载
    DOWNLOAD = "DOWNLOAD","文件下载"
    # 登录
    LOGIN = "LOGIN","登录"
    # 登出
    LOGOUT = "LOGOUT","登出"
    # 导出
    EXPORT = "EXPORT","导出"
    # 申请审核
    REQUEST_AUDIT = "REQUEST_AUDIT","申请审核"
    # 状态更改
    STATUS_CHANGE = "STATUS_CHANGE","状态更改"
    # 其他
    OTHER = "OTHER","其他"