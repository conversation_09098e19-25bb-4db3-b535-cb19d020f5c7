from customer_service.health_education.models import HealthEducationContent
from file.models import MaternityContractFile, MaternityDischargeRecordsFile, MedicalReferralFile, MedicalReferralReturnFile, \
    InfectionManagementFile, DisinfectFile, StaffContractFile, StaffQualificationCertificateFile, StaffTrainingRecordAttachment, \
    StaffAssessmentRecordAttachment, StaffHealthCheckRecordAttachment, ActivityCoverFile, EquipmentImage
from maternity_center.models import MaternityCenterCarouselFile
    
    


MODELS_CONFIG = {
    
    'MC': MaternityContractFile,
    'MDR': MaternityDischargeRecordsFile,
    'MCR': MedicalReferralFile,
    'MCB': MedicalReferralReturnFile,
    'IFM': InfectionManagementFile,
    'DIF': DisinfectFile,
    'SCF': StaffContractFile,
    'SQC': StaffQualificationCertificateFile,
    'STR': StaffTrainingRecordAttachment,
    'SAR': StaffAssessmentRecordAttachment,
    'SHR': StaffHealthCheckRecordAttachment,
    'ACF': ActivityCoverFile,
    'EI': EquipmentImage,
    'HEC': HealthEducationContent,
    'MCF': MaternityCenterCarouselFile
}