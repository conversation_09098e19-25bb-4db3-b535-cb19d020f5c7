from django.db import models

from audit_log.enum import OperationTypeEnum
from core.generate_hashid import generate_audit_log_code
from core.model import BaseModel
from maternity_center.models import MaternityCenter
from user.models import Staff


# 审计日志模型
class AuditLog(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 操作人员
    operator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="操作人员",blank=True,null=True)
    # ip地址
    ip_address = models.CharField(verbose_name="ip地址",max_length=20)
    # 操作模块	
    operation_module = models.CharField(verbose_name="操作模块",max_length=30)
    # 操作类型	
    operation_type = models.CharField(verbose_name="操作类型",max_length=20,choices=OperationTypeEnum.choices)
    # 操作详情
    operation_detail = models.Char<PERSON>ield(verbose_name="操作详情",max_length=200)
    # 附加数据，json 格式
    extra_data = models.JSONField(verbose_name="附加数据",blank=True,null=True,default=dict)
    # 日志编号
    lid = models.CharField(verbose_name="日志编号",max_length=20,default=generate_audit_log_code)
    
    class Meta:
        verbose_name = "审计日志"
        verbose_name_plural = "审计日志"
        
    def __str__(self):
        return f"{self.operator.name} - {self.operation_type} - {self.operation_detail}"
    
    
    @classmethod
    def create_audit_log(cls,maternity_center,operator,ip_address,operation_module,operation_type,operation_detail,extra_data=None):
        if extra_data is None:
            extra_data = {}
        cls.objects.create(maternity_center=maternity_center,operator=operator,ip_address=ip_address,operation_module=operation_module,operation_type=operation_type,operation_detail=operation_detail,extra_data=extra_data)
        
        
        