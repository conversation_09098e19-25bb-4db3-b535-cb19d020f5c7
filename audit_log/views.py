from audit_log.enum import OperationTypeEnum
from audit_log.models import AuditLog
from audit_log.serializers import AuditLogListSerializer
from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.view import PaginationListBaseView
from permissions.enum import PermissionEnum




# 审计日志列表
class AuditLogListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.AUDIT_LOG_VIEW
    serializer_class = AuditLogListSerializer
    response_msg = "获取审计日志列表成功"
    search_fields = ['operator__name','ip_address']
    
    def get_queryset(self):
        
        queryset = AuditLog.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
        
        operation_type = self.request.query_params.get('operation_type',None)
        
        if operation_type:
            if operation_type not in OperationTypeEnum.values:
                self.error_response_msg = "操作类型参数错误"
                return None
            queryset = queryset.filter(operation_type=operation_type)
        
        return queryset
        