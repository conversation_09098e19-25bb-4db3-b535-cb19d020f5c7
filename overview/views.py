from collections import defaultdict
from datetime import timedelta

from django.db.models import Count, Q
from django.utils import timezone
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import CheckInStatusEnum
from core.resp import make_response
from customer_service.core_records.enums.maternal import CheckInSourceEnum
from customer_service.core_records.models.baby import Newborn
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.room.enum import RoomStatusEnum
from customer_service.room.models import Room
from permissions.enum import PermissionEnum


class OverviewDataView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.DASHBOARD_VIEW

    def get(self, request):
        try:
            maternity_center = request.user.maternity_center
            today = timezone.now().date()

            # 获取基础数据
            overview_data = {
                # 当前入住率统计
                'current_occupancy_rate': self._get_current_occupancy_rate(maternity_center),
                # 今日预计入住/退房统计
                'today_checkin_checkout': self._get_today_checkin_checkout(maternity_center, today),
                # 在住产妇总数
                'current_mothers_count': self._get_current_mothers_count(maternity_center),
                # 在住新生儿总数
                'current_newborns_count': self._get_current_newborns_count(maternity_center),
                # 待入住客户统计
                'pending_checkin_customers': self._get_pending_checkin_customers(maternity_center, today),
                # 生产期待入住统计
                'delivery_pending_checkin': self._get_delivery_pending_checkin(maternity_center, today),
                # 已签约待入住统计
                'contracted_pending_checkin': self._get_contracted_pending_checkin(maternity_center),
                # 入住率趋势（未来7天）
                'occupancy_trend_7days': self._get_occupancy_trend_7days(maternity_center, today),
                # 客户来源分析
                'customer_source_analysis': self._get_customer_source_analysis(maternity_center),
                # 房间使用状况统计
                'room_usage_status': self._get_room_usage_status(maternity_center, today)
            }

            return make_response(code=0, msg="获取总览数据成功", data=overview_data)

        except Exception as e:
            return make_response(code=-1, msg=f"获取总览数据失败: {str(e)}")

    def _get_current_occupancy_rate(self, maternity_center):
        # 计算当前入住率
        try:
            # 总房间数
            total_rooms = Room.objects.filter(
                maternity_center=maternity_center,
                room_status=RoomStatusEnum.AVAILABLE
            ).count()

            if total_rooms == 0:
                return {
                    'rate': 0.00,
                    'occupied_rooms': 0,
                    'total_rooms': 0
                }

            # 已入住房间数
            occupied_rooms = Room.objects.filter(
                maternity_center=maternity_center,
                room_status=RoomStatusEnum.AVAILABLE,
                maternity_admission__check_in_status=CheckInStatusEnum.CHECKED_IN
            ).distinct().count()

            rate = round((occupied_rooms / total_rooms) * 100, 2)

            return {
                'rate': rate,
                'occupied_rooms': occupied_rooms,
                'total_rooms': total_rooms
            }
        except Exception:
            return {
                'rate': 0.00,
                'occupied_rooms': 0,
                'total_rooms': 0
            }

    def _get_today_checkin_checkout(self, maternity_center, today):
        # 获取今日预计入住/退房数据
        try:
            # 今日预计入住
            expected_checkin = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.RESERVED,
                expected_check_in_date=today
            ).count()

            # 今日实际入住
            actual_checkin = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                actual_check_in_date=today
            ).count()

            # 今日预计退房
            expected_checkout = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.CHECKED_IN,
                expected_check_out_date=today
            ).count()

            # 今日实际退房
            actual_checkout = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                actual_check_out_date=today
            ).count()

            return {
                'expected_checkin': expected_checkin,
                'actual_checkin': actual_checkin,
                'expected_checkout': expected_checkout,
                'actual_checkout': actual_checkout
            }
        except Exception:
            return {
                'expected_checkin': 0,
                'actual_checkin': 0,
                'expected_checkout': 0,
                'actual_checkout': 0
            }

    def _get_current_mothers_count(self, maternity_center):
        # 获取在住产妇总数
        try:
            return MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.CHECKED_IN
            ).count()
        except Exception:
            return 0

    def _get_current_newborns_count(self, maternity_center):
        # 获取在住新生儿总数
        try:
            # 通过产妇入院记录获取在住新生儿数量
            return Newborn.objects.filter(
                maternity_admission__maternity_center=maternity_center,
                maternity_admission__check_in_status=CheckInStatusEnum.CHECKED_IN
            ).count()
        except Exception:
            return 0

    def _get_pending_checkin_customers(self, maternity_center, today):
        # 获取待入住客户数据
        try:
            # 总待入住客户（已预约但未入住）
            total_pending = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.RESERVED
            ).count()

            # 今日应入住但未入住
            today_pending = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.RESERVED,
                expected_check_in_date__lte=today
            ).count()

            # 逾期未入住（预计入住日期已过）
            overdue_pending = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.RESERVED,
                expected_check_in_date__lt=today
            ).count()

            return {
                'total_pending': total_pending,
                'today_pending': today_pending,
                'overdue_pending': overdue_pending
            }
        except Exception:
            return {
                'total_pending': 0,
                'today_pending': 0,
                'overdue_pending': 0
            }

    def _get_delivery_pending_checkin(self, maternity_center, today):
        # 获取生产期待入住数据
        try:
            # 预计分娩日期在未来7天内且未入住的客户
            future_7days = today + timedelta(days=7)

            delivery_pending = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.RESERVED,
                expected_delivery_date__gte=today,
                expected_delivery_date__lte=future_7days
            ).count()

            # 预计分娩日期已过但未入住
            delivery_overdue = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.RESERVED,
                expected_delivery_date__lt=today
            ).count()

            return {
                'delivery_pending_7days': delivery_pending,
                'delivery_overdue': delivery_overdue
            }
        except Exception:
            return {
                'delivery_pending_7days': 0,
                'delivery_overdue': 0
            }

    def _get_contracted_pending_checkin(self, maternity_center):
        # 获取已签约待入住数据
        try:
            # 有合同编号且状态为预约的客户
            contracted_pending = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.RESERVED
            ).exclude(
                Q(contract_number='') | Q(contract_number__isnull=True)
            ).count()

            # 未签约但已预约的客户
            uncontracted_pending = MaternityAdmission.objects.filter(
                maternity_center=maternity_center,
                check_in_status=CheckInStatusEnum.RESERVED
            ).filter(
                Q(contract_number='') | Q(contract_number__isnull=True)
            ).count()

            return {
                'contracted_pending': contracted_pending,
                'uncontracted_pending': uncontracted_pending
            }
        except Exception:
            return {
                'contracted_pending': 0,
                'uncontracted_pending': 0
            }

    def _get_occupancy_trend_7days(self, maternity_center, today):
        # 获取未来7天入住率趋势
        try:
            trend_data = []
            total_rooms = Room.objects.filter(
                maternity_center=maternity_center,
                room_status=RoomStatusEnum.AVAILABLE
            ).count()

            if total_rooms == 0:
                return []

            for i in range(7):
                check_date = today + timedelta(days=i)

                # 计算该日期的占用房间数
                occupied_count = self._get_occupied_rooms_on_date(maternity_center, check_date)

                occupancy_rate = round((occupied_count / total_rooms) * 100, 2)

                trend_data.append({
                    'date': check_date.strftime('%Y-%m-%d'),
                    'date_display': check_date.strftime('%m-%d'),
                    'weekday': ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][check_date.weekday()],
                    'occupied_rooms': occupied_count,
                    'total_rooms': total_rooms,
                    'occupancy_rate': occupancy_rate
                })

            return trend_data
        except Exception:
            return []

    def _get_occupied_rooms_on_date(self, maternity_center, check_date):
        # 计算指定日期的占用房间数
        try:
            # 已入住且在该日期仍在住的房间
            checked_in_rooms = Room.objects.filter(
                maternity_center=maternity_center,
                room_status=RoomStatusEnum.AVAILABLE,
                maternity_admission__check_in_status=CheckInStatusEnum.CHECKED_IN,
                maternity_admission__actual_check_in_date__lte=check_date
            ).exclude(
                maternity_admission__actual_check_out_date__lt=check_date
            ).distinct()

            # 预约且在该日期应该入住的房间
            reserved_rooms = Room.objects.filter(
                maternity_center=maternity_center,
                room_status=RoomStatusEnum.AVAILABLE,
                maternity_admission__check_in_status=CheckInStatusEnum.RESERVED,
                maternity_admission__expected_check_in_date__lte=check_date,
                maternity_admission__expected_check_out_date__gt=check_date
            ).distinct()

            # 合并去重
            all_occupied_rooms = (checked_in_rooms | reserved_rooms).distinct()
            return all_occupied_rooms.count()
        except Exception:
            return 0

    def _get_customer_source_analysis(self, maternity_center):
        # 获取客户来源分析
        try:
            # 统计各来源的客户数量（包括所有状态的客户）
            source_stats = MaternityAdmission.objects.filter(
                maternity_center=maternity_center
            ).values('check_in_source').annotate(
                count=Count('id')
            ).order_by('-count')

            # 转换为更友好的格式
            source_analysis = []
            total_customers = sum(item['count'] for item in source_stats)

            for item in source_stats:
                source_enum = CheckInSourceEnum(item['check_in_source'])
                percentage = round((item['count'] / total_customers) * 100, 2) if total_customers > 0 else 0

                source_analysis.append({
                    'source_code': item['check_in_source'],
                    'source_name': source_enum.label,
                    'count': item['count'],
                    'percentage': percentage
                })

            return {
                'total_customers': total_customers,
                'source_breakdown': source_analysis
            }
        except Exception:
            return {
                'total_customers': 0,
                'source_breakdown': []
            }

    def _get_room_usage_status(self, maternity_center, today):
        # 获取房间使用状况
        try:
            # 按楼层分组统计房间状态
            floor_stats = defaultdict(lambda: {
                'floor': 0,
                'total_rooms': 0,
                'checked_in': 0,
                'reserved': 0,
                'available': 0,
                'unavailable_maintenance': 0,
                'unavailable_cleaning': 0,
                'unavailable_switch_room': 0,
                'unavailable_other': 0
            })

            # 获取所有房间
            rooms = Room.objects.filter(maternity_center=maternity_center).prefetch_related(
                'maternity_admission'
            )

            for room in rooms:
                floor = room.floor
                floor_stats[floor]['floor'] = floor
                floor_stats[floor]['total_rooms'] += 1

                # 判断房间当前状态
                if room.room_status != RoomStatusEnum.AVAILABLE:
                    # 不可用状态
                    if room.room_status == RoomStatusEnum.UNAVAILABLE_MAINTENANCE:
                        floor_stats[floor]['unavailable_maintenance'] += 1
                    elif room.room_status == RoomStatusEnum.UNAVAILABLE_CLEANING:
                        floor_stats[floor]['unavailable_cleaning'] += 1
                    elif room.room_status == RoomStatusEnum.UNAVAILABLE_SWITCH_ROOM:
                        floor_stats[floor]['unavailable_switch_room'] += 1
                    elif room.room_status == RoomStatusEnum.UNAVAILABLE_OTHER:
                        floor_stats[floor]['unavailable_other'] += 1
                else:
                    # 可用房间，检查入住状态
                    current_admission = room.maternity_admission.filter(
                        check_in_status=CheckInStatusEnum.CHECKED_IN
                    ).first()

                    if current_admission:
                        floor_stats[floor]['checked_in'] += 1
                    else:
                        # 检查是否有预约
                        reserved_admission = room.maternity_admission.filter(
                            check_in_status=CheckInStatusEnum.RESERVED,
                            expected_check_in_date__lte=today + timedelta(days=1)  # 明日内的预约
                        ).first()

                        if reserved_admission:
                            floor_stats[floor]['reserved'] += 1
                        else:
                            floor_stats[floor]['available'] += 1

            # 转换为列表格式并按楼层排序
            floor_list = []
            for floor in sorted(floor_stats.keys()):
                stats = floor_stats[floor]
                floor_list.append({
                    'floor': floor,
                    'floor_name': f"{floor}楼",
                    'total_rooms': stats['total_rooms'],
                    'checked_in': stats['checked_in'],
                    'reserved': stats['reserved'],
                    'available': stats['available'],
                    'unavailable_maintenance': stats['unavailable_maintenance'],
                    'unavailable_cleaning': stats['unavailable_cleaning'],
                    'unavailable_switch_room': stats['unavailable_switch_room'],
                    'unavailable_other': stats['unavailable_other']
                })

            # 计算总体统计
            total_stats = {
                'total_rooms': sum(stats['total_rooms'] for stats in floor_stats.values()),
                'checked_in': sum(stats['checked_in'] for stats in floor_stats.values()),
                'reserved': sum(stats['reserved'] for stats in floor_stats.values()),
                'available': sum(stats['available'] for stats in floor_stats.values()),
                'unavailable_maintenance': sum(stats['unavailable_maintenance'] for stats in floor_stats.values()),
                'unavailable_cleaning': sum(stats['unavailable_cleaning'] for stats in floor_stats.values()),
                'unavailable_switch_room': sum(stats['unavailable_switch_room'] for stats in floor_stats.values()),
                'unavailable_other': sum(stats['unavailable_other'] for stats in floor_stats.values())
            }

            return {
                'total_stats': total_stats,
                'floor_breakdown': floor_list
            }
        except Exception:
            return {
                'total_stats': {
                    'total_rooms': 0,
                    'checked_in': 0,
                    'reserved': 0,
                    'available': 0,
                    'unavailable_maintenance': 0,
                    'unavailable_cleaning': 0,
                    'unavailable_switch_room': 0,
                    'unavailable_other': 0
                },
                'floor_breakdown': []
            }
