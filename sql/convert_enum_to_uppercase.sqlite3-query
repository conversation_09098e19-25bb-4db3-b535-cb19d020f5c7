-- ========================================
-- 将枚举字段从小写转换为大写的SQL脚本
-- 适用于SQLite3数据库
-- ========================================

-- 1. 更新questionnaire_questionnaire表的questionnaire_type字段
-- 将小写的问卷类型转换为大写
UPDATE questionnaire_questionnaire 
SET questionnaire_type = CASE 
    WHEN questionnaire_type = 'satisfaction' THEN 'SATISFACTION'
    WHEN questionnaire_type = 'health_status' THEN 'HEALTH_STATUS'  
    WHEN questionnaire_type = 'service_evaluation' THEN 'SERVICE_EVALUATION'
    WHEN questionnaire_type = 'custom' THEN 'CUSTOM'
    ELSE questionnaire_type  -- 保持已经是大写的不变
END
WHERE questionnaire_type IN ('satisfaction', 'health_status', 'service_evaluation', 'custom');

-- 2. 更新questionnaire_question表的question_type字段  
-- 将小写的问题类型转换为大写
UPDATE questionnaire_question
SET question_type = CASE
    WHEN question_type = 'single_choice' THEN 'SINGLE_CHOICE'
    WHEN question_type = 'multiple_choice' THEN 'MULTIPLE_CHOICE'
    WHEN question_type = 'true_false' THEN 'TRUE_FALSE'
    WHEN question_type = 'star_rating' THEN 'STAR_RATING'
    WHEN question_type = 'text' THEN 'TEXT'
    ELSE question_type  -- 保持已经是大写的不变
END
WHERE question_type IN ('single_choice', 'multiple_choice', 'true_false', 'star_rating', 'text');
