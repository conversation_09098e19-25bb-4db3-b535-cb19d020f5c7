from audit_log.enum import OperationTypeEnum
from audit_log.models import AuditLog
import logging

logger = logging.getLogger(__name__)


class AuditLogCreator:
    @staticmethod
    def _create_audit_log(request, operation_module, operation_detail, res_data, operation_type):
        try:
            # 尝试异步创建审计日志
            from audit_log.tasks import create_audit_log_task

            # 提取需要的数据
            ip_address = request.META.get('REMOTE_ADDR', '')            
            
            # 异步创建审计日志
            create_audit_log_task.delay(
                maternity_center_id=request.user.maternity_center.id,
                operator_id=request.user.id,
                ip_address=ip_address,
                operation_module=operation_module,
                operation_type=operation_type,
                operation_detail=str(operation_detail),
                extra_data=res_data
            )

        except ImportError:
            print("Celery未配置，使用同步方式创建审计日志")
            try:
                AuditLog.create_audit_log(
                    maternity_center=request.user.maternity_center,
                    operator=request.user,
                    ip_address=request.META.get('REMOTE_ADDR', ''),
                    operation_module=operation_module,
                    operation_type=operation_type,
                    operation_detail=f"{operation_detail}",
                    extra_data=res_data)
            except:
                pass
        except Exception as e:
            # 如果异步任务提交失败，降级到同步方式
            print(f"提交异步审计日志任务失败: {e}，降级到同步方式")
            try:
                AuditLog.create_audit_log(
                    maternity_center=request.user.maternity_center,
                    operator=request.user,
                    ip_address=request.META.get('REMOTE_ADDR', ''),
                    operation_module=operation_module,
                    operation_type=operation_type,
                    operation_detail=f"{operation_detail}",
                    extra_data=res_data)
            except:
                pass


    # 文件上传
    @staticmethod
    def create_file_upload_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.UPLOAD)

    # 文件下载
    @staticmethod
    def create_file_download_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.DOWNLOAD)

    # 创建
    @staticmethod
    def create_create_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.CREATE)

    # 更新
    @staticmethod
    def create_update_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.UPDATE)

    # 删除
    @staticmethod
    def create_delete_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.DELETE)

    # 查询
    @staticmethod
    def create_query_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.QUERY)
        
    # 申请审核
    @staticmethod
    def create_request_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.REQUEST_AUDIT)
        
    # 状态更改
    @staticmethod
    def create_status_change_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.STATUS_CHANGE)
        
    # 导出
    @staticmethod
    def create_export_audit_log(request, operation_module, operation_detail, res_data=None):    
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.EXPORT)
        
    # 登录
    @staticmethod
    def create_login_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.LOGIN)
        
    # 登出
    @staticmethod
    def create_logout_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.LOGOUT)
        
    # 其他
    @staticmethod
    def create_other_audit_log(request, operation_module, operation_detail, res_data=None):
        AuditLogCreator._create_audit_log(request, operation_module, operation_detail, res_data, OperationTypeEnum.OTHER)
    


# 保持向后兼容性的函数包装器
def create_file_upload_audit_log(request, operation_module, operation_detail, resData=None):
    AuditLogCreator.create_file_upload_audit_log(request, operation_module, operation_detail, resData)

def create_file_download_audit_log(request, operation_module, operation_detail, resData=None):
    AuditLogCreator.create_file_download_audit_log(request, operation_module, operation_detail, resData)

def create_create_audit_log(request, operation_module, operation_detail, resData=None):
    AuditLogCreator.create_create_audit_log(request, operation_module, operation_detail, resData)

def create_update_audit_log(request, operation_module, operation_detail, resData=None):
    AuditLogCreator.create_update_audit_log(request, operation_module, operation_detail, resData)

def create_delete_audit_log(request, operation_module, operation_detail, resData=None):
    AuditLogCreator.create_delete_audit_log(request, operation_module, operation_detail, resData)

def create_query_audit_log(request, operation_module, operation_detail, resData=None):
    AuditLogCreator.create_query_audit_log(request, operation_module, operation_detail, resData)