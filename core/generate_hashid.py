import hashlib
import random
import threading
import time
import uuid
from datetime import datetime


def _generate_long_code(prefix):
    now = datetime.now()
    date_str = now.strftime('%Y%m%d')

    timestamp = str(int(time.time() * 1000000))
    random_num = str(random.randint(100000, 999999))
    unique_id = f"{prefix}_{timestamp}_{random_num}"

    hash_object = hashlib.sha256(unique_id.encode('utf-8'))
    hash_hex = hash_object.hexdigest()
    hash_int = int(hash_hex, 16)
    hash_str = str(hash_int)[-17:].zfill(15)

    return f"{prefix}{date_str}{hash_str}"

def _generate_code(prefix):
    now = datetime.now()
    date_str = now.strftime('%Y%m%d')

    timestamp = str(int(time.time() * 1000000))
    random_num = str(random.randint(100000, 999999))
    unique_id = f"{prefix}_{timestamp}_{random_num}"

    hash_object = hashlib.sha256(unique_id.encode('utf-8'))
    hash_hex = hash_object.hexdigest()
    hash_int = int(hash_hex, 16)
    hash_str = str(hash_int)[-17:].zfill(17)

    return f"{prefix}{date_str}{hash_str}"

# 产妇入院记录编号
def generate_maternity_admission_code():
    return _generate_code("MA")

# 新生儿编号
def generate_newborn_code():
    return _generate_code("BB")


# 最近活动编号
def generate_maternity_recent_activity_code():
    return _generate_code("MRA")

# 产妇每日必填记录编号
def generate_maternity_daily_required_record_code():
    return _generate_code("MD")

# 新生儿入住评估记录编号
def generate_newborn_check_in_assessment_code():
    return _generate_code("NI")

# 产妇入住评估记录编号
def generate_maternity_check_in_assessment_code():
    return _generate_code("MI")

# 产妇康复护理评估记录编号
def generate_maternity_rehabilitation_assessment_record_code():
    return _generate_code("MR")

# 产妇膳食记录表编号
def generate_maternity_diet_record_code():
    return _generate_code("MF")

# 产妇每日生理护理记录编号
def generate_maternity_daily_physical_care_record_code():
    return _generate_code("MP")

# 新生儿每日必填记录
def generate_newborn_daily_required_record_code():
    return _generate_code("NR")

# 新生儿护理记录单（1）
def generate_newborn_care_one_record_code():
    return _generate_code("N1")

# 新生儿护理记录单（2）
def generate_newborn_care_two_record_code():
    return _generate_code("N2")

# 新生儿喂养记录
def generate_newborn_feeding_record_code():
    return _generate_code("NF")

# 新生儿护理操作记录
def generate_newborn_care_operation_record_code():
    return _generate_code("NO")

# 新生儿满月评估
def generate_newborn_month_assessment_code():
    return _generate_code("NM")

# 反馈编号
def generate_feedback_code():
    return _generate_code("FB")

# 结算单号
def generate_bill_number():
    return _generate_code("BI")

# 续住单号
def generate_renew_bill_number():
    return _generate_code("RBI")

# 反馈处理记录编号
def generate_feedback_process_code():
    return _generate_code("FR")

# 访客登记单号
def generate_visitor_record_number():
    return _generate_code("VR")

# 产妇转诊单编号
def generate_maternity_referral_code():
    return _generate_code("MMR")

# 新生儿转诊单编号
def generate_newborn_referral_code():
    return _generate_code("NMR")

# 换房申请单号
def generate_room_change_application_code():
    return _generate_code("RC")

# 外出申请单号
def generate_outing_application_code():
    return _generate_code("OA")

# 产妇膳食记录表编号
def generate_maternity_daily_diet_record_code():
    return _generate_code("MDD")

# 小程序预约参观单号
def generate_wechat_app_visit_appointment_number():
    return _generate_code("VWX")

# 员工编号
def generate_staff_code():
    return _generate_code("ST")

# 问卷编号
def generate_questionnaire_code():
    return _generate_code("MQ")

# 审计日志编号
def generate_audit_log_code():
    return _generate_code("AL")

# 月子中心编号
def generate_maternity_center_cid():
    return str(uuid.uuid4()).replace('-', '')[:17]

# 用户id 生成器
class SnowflakeGenerator:
    EPOCH = 1719792000000

    def __init__(self, node_id: int = 0):
        self.node_id = node_id & 0x3FF
        self.sequence = 0
        self.last_timestamp = -1
        self.lock = threading.Lock()

    def generate(self) -> int:

        with self.lock:
            timestamp = int(time.time() * 1000) 

            # 简化的时钟回拨处理
            if timestamp < self.last_timestamp:
                # 记录异常但不抛出错误
                print(f"时钟回拨异常: 当前时间{timestamp} < 上次时间{self.last_timestamp}")
                # 使用上次时间戳 + 1ms 继续
                timestamp = self.last_timestamp + 1
                print(f"使用上次时间戳 + 1ms 继续: {timestamp}")
            
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & 0xFFF
                if self.sequence == 0:
                    timestamp = self._wait_next_millis(timestamp)
            else:
                self.sequence = 0

            self.last_timestamp = timestamp

            timestamp_part = (timestamp - self.EPOCH) << 22
            node_part = self.node_id << 12
            sequence_part = self.sequence

            snowflake_id = timestamp_part | node_part | sequence_part

            return snowflake_id

    def _wait_next_millis(self, last_timestamp: int) -> int:
        timestamp = int(time.time() * 1000)
        while timestamp <= last_timestamp:
            timestamp = int(time.time() * 1000)
        return timestamp

    @classmethod
    def parse_timestamp(cls, snowflake_id: int) -> int:
        timestamp_ms = (snowflake_id >> 22) + cls.EPOCH
        return int(timestamp_ms / 1000)

    @classmethod
    def parse_node_id(cls, snowflake_id: int) -> int:

        return (snowflake_id >> 12) & 0x3FF

    @classmethod
    def parse_sequence(cls, snowflake_id: int) -> int:
        return snowflake_id & 0xFFF


def parse_uid_info(uid: str) -> dict:
    try:
        uid_int = int(uid)
        timestamp = SnowflakeGenerator.parse_timestamp(uid_int)
        node_id = SnowflakeGenerator.parse_node_id(uid_int)
        sequence = SnowflakeGenerator.parse_sequence(uid_int)

        return {
            'uid': uid,
            'timestamp': timestamp,
            'created_at': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
            'node_id': node_id,
            'sequence': sequence
        }
    except (ValueError, OverflowError) as e:
        return {
            'uid': uid,
            'error': f"无效的UID格式: {e}",
            'timestamp': None,
            'created_at': None,
            'node_id': None,
            'sequence': None
        }


def generate_user_uid() -> str:
    return str(SnowflakeGenerator(node_id=0).generate())

def generate_maternity_uid() -> str:
    return generate_user_uid()


def generate_staff_uid() -> str:
    return generate_user_uid()

# 资源 uuid 生成
def generate_resource_uuid() -> str:
    return str(uuid.uuid4())

# 宣教编号
def generate_health_education_code() -> str:
    return _generate_code("HE")