from django.core.exceptions import ValidationError
from django.core.validators import BaseValidator
from django.core.validators import MinValueValidator
from django.db import models


# 基础模型
class BaseModel(models.Model):
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 更新时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        abstract = True
        
        
    
# 正浮点数字段验证
class PositiveFloatField(models.FloatField):

    def __init__(self, *args, **kwargs):
        validators = list(kwargs.pop('validators', [])) 

        has_min_value_validator = False
        for validator in validators:
            if isinstance(validator, MinValueValidator) and validator.limit_value == 0.0:
                has_min_value_validator = True
                break

        if not has_min_value_validator:
            validators.append(MinValueValidator(0.0))

        kwargs['validators'] = validators
        super().__init__(*args, **kwargs)

    def validate(self, value, model_instance):
        super().validate(value, model_instance)
        if value is not None and value < 0:
            raise ValidationError(
                f'{self.verbose_name}不能为负数',
                code='negative_value'
            )

    def formfield(self, **kwargs):
        defaults = {'min_value': 0.0}
        defaults.update(kwargs)
        return super().formfield(**defaults)
    
    
# 多选字段校验器
class JSONListValidator(BaseValidator):

    def __init__(self, allowed_choices, message=None):
        self.allowed_choices = [choice[0] for choice in allowed_choices]
        self.choices_display = dict(allowed_choices)
        super().__init__(limit_value=self.allowed_choices)
    
    def __call__(self, value):
        print(f"value: {value}")
        if not isinstance(value, list):
            raise ValidationError("数据校验失败")
        
        for item in value:
            if item not in self.allowed_choices:
                valid_options = [v for k, v in self.choices_display.items() if k != 'UNKNOWN']
                raise ValidationError(
                    f"无效值，允许的范围：{', '.join(valid_options)}"
                )
                
                

def get_field_changes(instance, validated_data):
    for field, value in validated_data.items():
        setattr(instance, field, value)

    dirty_fields = instance.get_dirty_fields()
    changed_fields = {}

    for field, old_value in dirty_fields.items():
        new_value = getattr(instance, field)
        verbose_name = instance._meta.get_field(field).verbose_name
        changed_fields[field] = {
            'field_name': field,
            'verbose_name': verbose_name,
            'old_value': old_value,
            'new_value': new_value
        }

    return changed_fields