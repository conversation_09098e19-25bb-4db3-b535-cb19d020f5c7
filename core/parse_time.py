from datetime import datetime

from django.utils import timezone
from rest_framework import serializers


# 序列化时转换时间为上海时间
class ShanghaiFriendlyDateTimeField(serializers.DateTimeField):
    def to_representation(self, value):

        if value:
            # 格式化为上海时间格式 YYYY-MM-DD HH:MM:SS
            return timezone.localtime(value).strftime("%Y-%m-%d %H:%M:%S")

        return None

# 根据传入时间字符串和格式解析时间
def parse_datetime_string(datetime_str, format_str="%Y-%m-%d %H:%M"):
    if not datetime_str:
        return None
    try:
        dt = datetime.strptime(datetime_str, format_str)
        if timezone.is_naive(dt):
            dt = timezone.make_aware(dt)
        return dt
    except Exception:
        return None
    
    
# 根据传入时间字符串和格式解析时间
def parse_datetime_to_shanghai_time(datetime_str,format_str="%Y-%m-%d %H:%M:%S"):
    if not datetime_str:
        return None
    try:
        return timezone.localtime(datetime_str).strftime(format_str)
    except Exception:
        return None