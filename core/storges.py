import datetime
import threading
from storages.backends.s3 import S3Storage
import os
import alibabacloud_oss_v2 as oss

oss_client = None
oss_client_lock = threading.Lock()

def get_oss_client():
    
    global oss_client
    
    if oss_client is None:
        
        with oss_client_lock:
            
            if oss_client is None:
                
                credentials_provider = oss.credentials.StaticCredentialsProvider(
                    access_key_id=os.environ.get('ALIYUN_ACCESS_KEY_ID'),
                    access_key_secret=os.environ.get('ALIYUN_ACCESS_KEY_SECRET')
                )

                cfg = oss.config.load_default()
                cfg.credentials_provider = credentials_provider
                cfg.region = os.environ.get('SIGN_OSS_REGION')
                cfg.endpoint = os.environ.get('SIGN_OSS_ENDPOINT')
                cfg.use_cname = True

                _oss_client = oss.Client(cfg)
                
    return _oss_client



def sign_public_url(region, endpoint, bucket, key, expire_minutes=15):

    client = get_oss_client()

    pre_result = client.presign(
        oss.GetObjectRequest(
            bucket=bucket,
            key=key,
        ),
        expires=datetime.timedelta(minutes=expire_minutes)
    )

    return pre_result.url

class CustomOSSStorage(S3Storage):

    def url(self, name, parameters=None, expire=None, http_method=None):
        
        object_key = self._normalize_name(name)
        region = os.environ.get('SIGN_OSS_REGION')  
        endpoint = os.environ.get('SIGN_OSS_ENDPOINT') 
        bucket = os.environ.get('SIGN_OSS_BUCKET_NAME')

        try:
            signed_url = sign_public_url(region, endpoint, bucket, object_key)
            print(f"signed_url: {signed_url}")
            return signed_url
        except Exception as e:
            print(f"签名生成失败: {e}")
            # 如果签名失败，回退到默认方法
            return super().url(name, parameters, expire, http_method)