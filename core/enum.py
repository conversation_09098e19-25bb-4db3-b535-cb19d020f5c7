from django.db import models
# 性别
class GenderEnum(models.IntegerChoices):
    UNKNOWN = 0, '未知'
    MALE = 1, '男'
    FEMALE = 2, '女'

# 血型
class BloodTypeEnum(models.TextChoices):
    UNKNOWN = "UNKNOWN", "未知"
    A = "A", "A 型"
    B = "B", "B 型"
    AB = "AB", "AB 型"
    O = "O", "O 型"

# 分娩方式
class DeliveryMethodEnum(models.TextChoices):
    # 未分娩
    UNBORN = "UNBORN", "未分娩"
    # 顺产
    VAGINAL = "VAGINAL", "阴道分娩"
    # 剖宫产
    CESAREAN = "CESAREAN", "剖宫产"
    # 产钳助产
    FORCEPS = "FORCEPS", "产钳助产"
    
    
# 在院状态
class CheckInStatusEnum(models.TextChoices):
    # 已预定
    RESERVED = "RESERVED", "已预定"
    # 已入住
    CHECKED_IN = "CHECKED_IN", "已入住"
    # 已退房
    CHECKED_OUT = "CHECKED_OUT", "已退房"
    # 已取消
    CANCELLED = "CANCELLED", "已取消"
    # 已作废
    INVALID = "INVALID", "已作废"
    

# 审批状态
class ApprovalStatusEnum(models.TextChoices):
    PENDING = 'PENDING','待处理'
    APPROVED = 'APPROVED','已批准'
    REJECTED = 'REJECTED','已拒绝'