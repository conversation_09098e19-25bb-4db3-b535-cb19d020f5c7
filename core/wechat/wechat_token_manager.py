import time
import threading
import requests
import json
import logging
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger(__name__)

class WechatAccessTokenManager:
    
    def __init__(self):
        self.lock = threading.Lock()
        self.cache_key = 'wechat:access_token'
        self.lock_key = 'wechat:token_lock'
        self.lock_timeout = 30  
        self.token_advance_expire = 60 
        
    def get_access_token(self):
        
        try:
            token_data = cache.get(self.cache_key)
            
            if token_data and self._is_token_valid(token_data):
                print("从缓存获取微信access_token成功")
                return token_data['access_token']
                
            print("微信access_token过期或不存在，开始刷新")
            return self._refresh_access_token()
            
        except Exception as e:
            print(f"获取微信access_token异常: {e}")
            return None
    
    def _refresh_access_token(self):
        
        lock_acquired = self._acquire_lock()
        
        if not lock_acquired:
            print("获取微信token锁失败，等待其他进程完成")
            time.sleep(1)
            token_data = cache.get(self.cache_key)
            if token_data and self._is_token_valid(token_data):
                return token_data['access_token']
            return None
            
        try:
            token_data = cache.get(self.cache_key)
            if token_data and self._is_token_valid(token_data):
                print("双重检查发现token仍然有效")
                return token_data['access_token']
                
            print("开始请求微信API获取新token")
            new_token_data = self._request_new_token()
            
            if new_token_data:
                # 缓存新token到Redis
                cache_timeout = new_token_data['expires_in'] - self.token_advance_expire
                cache.set(self.cache_key, new_token_data, timeout=cache_timeout)
                
                print(f"微信access_token刷新成功，缓存{cache_timeout}秒")
                return new_token_data['access_token']
            else:
                print("请求微信API获取token失败")
                return None
                
        except Exception as e:
            print(f"刷新微信access_token异常: {e}")
            return None
        finally:
            # 释放锁
            self._release_lock()
    
    def _acquire_lock(self):
        try:

            lock_value = f"{time.time()}_{threading.current_thread().ident}"
            
            for _ in range(50):  
                if cache.add(self.lock_key, lock_value, timeout=self.lock_timeout):
                    print("获取微信token分布式锁成功")
                    return True
                time.sleep(0.1)
                
            print("获取微信token分布式锁超时")
            return False
            
        except Exception as e:
            print(f"获取微信token锁异常: {e}")
            return False
    
    def _release_lock(self):
        try:
            cache.delete(self.lock_key)
            print("释放微信token分布式锁成功")
        except Exception as e:
            print(f"释放微信token锁异常: {e}")
    
    def _request_new_token(self):
        if not settings.WECHAT_APPID or not settings.WECHAT_APPSECRET:
            logger.error("微信APPID或APPSECRET未配置")
            return None
            
        url = "https://api.weixin.qq.com/cgi-bin/token"
        params = {
            'grant_type': 'client_credential',
            'appid': settings.WECHAT_APPID,
            'secret': settings.WECHAT_APPSECRET
        }
        
        try:
            print(f"请求微信API: {url}")
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            print(f"微信API响应: {data}")
            
            if 'access_token' in data and 'expires_in' in data:
                return {
                    'access_token': data['access_token'],
                    'expires_in': data['expires_in'],
                    'created_at': time.time()
                }
            else:
                error_msg = data.get('errmsg', '未知错误')
                error_code = data.get('errcode', -1)
                print(f"微信API返回错误: code={error_code}, msg={error_msg}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求微信API网络异常: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"解析微信API响应JSON异常: {e}")
            return None
        except Exception as e:
            print(f"请求微信API未知异常: {e}")
            return None
    
    def _is_token_valid(self, token_data):
        if not token_data or not isinstance(token_data, dict):
            return False
            
        required_keys = ['access_token', 'expires_in', 'created_at']
        if not all(key in token_data for key in required_keys):
            return False
            
        current_time = time.time()
        expire_time = token_data['created_at'] + token_data['expires_in'] - self.token_advance_expire
        
        is_valid = current_time < expire_time
        if not is_valid:
            logger.info(f"微信token已过期: 当前时间={current_time}, 过期时间={expire_time}")
            
        return is_valid
    
    def force_refresh_token(self):
 
        try:
            cache.delete(self.cache_key)
            print("强制清除微信token缓存")
            return self._refresh_access_token()
        except Exception as e:
            print(f"强制刷新微信token异常: {e}")
            return None
    
    def get_token_info(self):

        try:
            token_data = cache.get(self.cache_key)
            if token_data:
                return {
                    'has_token': True,
                    'is_valid': self._is_token_valid(token_data),
                    'created_at': token_data.get('created_at'),
                    'expires_in': token_data.get('expires_in'),
                    'remaining_seconds': token_data.get('created_at', 0) + token_data.get('expires_in', 0) - time.time()
                }
            return {'has_token': False}
        except Exception as e:
            print(f"获取token信息异常: {e}")
            return None


# 全局单例实例
wechat_token_manager = WechatAccessTokenManager()