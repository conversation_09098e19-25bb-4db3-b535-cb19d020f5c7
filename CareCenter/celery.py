import os
from celery import Celery


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CareCenter.settings')

app = Celery('CareCenter')

app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务
app.autodiscover_tasks()

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
    
    

@app.task(name='check_certificate_expiry')
def check_certificate_expiry():
    print('开始检查证书到期提醒')
    from organizational_management.staff_schedule.models import StaffQualificationCertificate
    StaffQualificationCertificate.check_certificate_expiry()