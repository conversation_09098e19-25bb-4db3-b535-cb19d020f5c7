from django.db import models
from django.utils import timezone

from core.model import BaseModel
from maternity_center.models import MaternityCenter


# 短信验证码模型
class SendSMSCodeLog(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True, related_name="send_sms_code_logs")
    # 手机号
    phone = models.CharField(max_length=11, verbose_name="手机号")
    # 验证码
    code = models.CharField(max_length=6, verbose_name="验证码")
    # 发送成功与否
    is_success = models.BooleanField(default=False, verbose_name="发送成功与否")
    # 发送结果
    send_result = models.JSONField(blank=True, null=True, verbose_name="发送结果")
    # 发送时间
    send_time = models.DateTimeField(auto_now_add=True, verbose_name="发送时间")
    # 是否使用过
    is_used = models.BooleanField(default=False, verbose_name="是否使用过")
    # 使用时间
    used_time = models.DateTimeField(blank=True, null=True, verbose_name="使用时间")
    # 验证结果
    verified_result = models.BooleanField(blank=True,default=False,verbose_name="验证结果")
    
    
    class Meta:
        verbose_name = "短信验证码"
        verbose_name_plural = "短信验证码"
        
    def __str__(self):
        return f"{self.phone} - {self.code}"
    
    
    @classmethod
    def send_sms_code_log(cls,phone, code,is_success,send_result,maternity_center):
        cls.objects.create(phone=phone, code=code, is_success=is_success, send_result=send_result,maternity_center=maternity_center)
    
    def mark_as_failed(self):
        self.is_used = True
        self.used_time = timezone.now()
        self.verified_result = False
        self.save()
        
    def mark_as_verified(self):
        self.is_used = True
        self.used_time = timezone.now()
        self.verified_result = True
        self.save()
        
        