import threading

from .config import SMSConfig
from .sms_client import SMSClient


_sms_client_instance = None
_client_lock = threading.Lock()

def get_sms_client():
    global _sms_client_instance
    if _sms_client_instance is None:
        with _client_lock:
            if _sms_client_instance is None:
                config = SMSConfig()
                _sms_client_instance = SMSClient(config)
    return _sms_client_instance