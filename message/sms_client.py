from Tea.exceptions import UnretryableException
from alibabacloud_dysmsapi20170525 import models
from alibabacloud_dysmsapi20170525.client import Client
from alibabacloud_tea_openapi import models as open_api_models

from .config import SMSConfig
from .exceptions import SMSNetworkException, SMSBusinessException


class SMSClient:
    def __init__(self, config: SMSConfig):
        """初始化短信客户端"""
        self.config = config
        self.config.validate()  # 验证配置
        self.client = self._create_client()

    def _create_client(self):
        """创建阿里云短信客户端"""
        client_config = open_api_models.Config(
            access_key_id=self.config.access_key_id,
            access_key_secret=self.config.access_key_secret,
            endpoint=self.config.endpoint
        )
        return Client(client_config)

    def send_sms(self, phone_numbers: str, template_params: dict) -> dict:
        """
        发送短信
        :param phone_numbers: 接收短信的手机号码，多个号码用逗号分隔
        :param template_params: 模板参数，如 {"code": "123456"}
        :return: 包含发送结果的字典
        """
        request = models.SendSmsRequest(
            phone_numbers=phone_numbers,
            sign_name=self.config.sign_name,
            template_code=self.config.template_code,
            template_param=str(template_params)
        )

        try:
            response = self.client.send_sms(request)
            return {
                'request_id': response.body.request_id,
                'code': response.body.code,
                'message': response.body.message
            }
        except UnretryableException as e:
            # 网络异常
            raise SMSNetworkException(f"网络错误: {str(e)}") from e
        except Exception as e:
            # 业务异常
            error_info = getattr(e, 'data', {})
            code = error_info.get('Code', 'UNKNOWN')
            msg = error_info.get('Message', str(e))
            raise SMSBusinessException(code, msg) from e


    def send_sms_with_template(self, template_code: str, phone_numbers: str, template_params: dict) -> dict:
        
        request = models.SendSmsRequest(
            phone_numbers=phone_numbers,
            sign_name=self.config.sign_name,
            template_code=template_code,
            template_param=str(template_params)
        )

        try:
            response = self.client.send_sms(request)
            return {
                'request_id': response.body.request_id,
                'code': response.body.code,
                'message': response.body.message
            }
        except UnretryableException as e:
            # 网络异常
            raise SMSNetworkException(f"网络错误: {str(e)}") from e
        except Exception as e:
            # 业务异常
            error_info = getattr(e, 'data', {})
            code = error_info.get('Code', 'UNKNOWN')
            msg = error_info.get('Message', str(e))
            raise SMSBusinessException(code, msg) from e

    def send_verification_code(self, phone: str, code: str) -> dict:
        """发送验证码短信的便捷方法"""
        return self.send_sms(phone, {"code": code})