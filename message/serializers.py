from rest_framework import serializers

class SMSCodeSerializer(serializers.Serializer):
    phone = serializers.CharField(max_length=11, help_text="手机号码")
    code = serializers.CharField(max_length=6, required=False, help_text="验证码")

    def validate_phone(self, value):
        # 手机号格式验证
        if not value.isdigit() or len(value) != 11:
            raise serializers.ValidationError("手机号码格式不正确")
        return value