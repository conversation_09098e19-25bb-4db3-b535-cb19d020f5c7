from django.conf import settings

class SMSConfig:
    def __init__(self):
        # 从Django设置中读取配置
        self.access_key_id = settings.ALIBABA_CLOUD_ACCESS_KEY_ID
        self.access_key_secret = settings.ALIBABA_CLOUD_ACCESS_KEY_SECRET
        self.endpoint = getattr(settings, 'SMS_ENDPOINT', 'dysmsapi.aliyuncs.com')
        self.sign_name = settings.SMS_SIGN_NAME
        self.template_code = settings.SMS_TEMPLATE_CODE

    def validate(self):
        required_fields = ['access_key_id', 'access_key_secret', 'sign_name', 'template_code']
        for field in required_fields:
            if not getattr(self, field):
                raise ValueError(f"配置缺失: {field}")