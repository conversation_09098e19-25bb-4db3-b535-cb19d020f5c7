from django.contrib import admin
from django.utils.html import format_html
from message.models import SendSMSCodeLog


@admin.register(SendSMSCodeLog)
class SendSMSCodeLogAdmin(admin.ModelAdmin):
    list_display = ['phone', 'code', 'maternity_center', 'success_status', 'usage_status', 'verification_status', 'send_time', 'used_time']
    search_fields = ['phone', 'code', 'maternity_center__name']
    list_filter = ['is_success', 'is_used', 'verified_result', 'maternity_center', 'send_time', 'created_at']
    date_hierarchy = 'send_time'
    ordering = ['-send_time']
    readonly_fields = ['created_at', 'updated_at', 'send_time', 'used_time']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('maternity_center')
    
    def success_status(self, obj):
        if obj.is_success:
            return format_html('<span style="color: #28a745; font-weight: bold;">✅ 发送成功</span>')
        else:
            return format_html('<span style="color: #dc3545; font-weight: bold;">❌ 发送失败</span>')
    success_status.short_description = '发送状态'
    
    def usage_status(self, obj):
        if obj.is_used:
            return format_html('<span style="color: #17a2b8; font-weight: bold;">🔓 已使用</span>')
        else:
            return format_html('<span style="color: #6c757d; font-weight: bold;">🔒 未使用</span>')
    usage_status.short_description = '使用状态'
    
    def verification_status(self, obj):
        if obj.verified_result:
            return format_html('<span style="color: #28a745; font-weight: bold;">✅ 验证通过</span>')
        elif obj.is_used:
            return format_html('<span style="color: #dc3545; font-weight: bold;">❌ 验证失败</span>')
        else:
            return format_html('<span style="color: #ffc107; font-weight: bold;">⏳ 待验证</span>')
    verification_status.short_description = '验证状态'
    
    def phone(self, obj):
        return format_html('<span style="font-family: monospace; background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">{}</span>', obj.phone)
    phone.short_description = '手机号'
    
    def code(self, obj):
        return format_html('<span style="font-family: monospace; background: #e9ecef; padding: 2px 6px; border-radius: 3px; font-weight: bold; color: #495057;">{}</span>', obj.code)
    code.short_description = '验证码'