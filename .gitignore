**/__pycache__/

# 环境变量文件
.env
env.sh

# 虚拟环境
venv
.venv

# 数据库
db.sqlite3

# .DS_Store
.DS_Store
**/.DS_Store

# .idea
.idea

# migrations 文件
*/migrations/*.py
!*/migrations/__init__.py
**/migrations/*.py
!**/migrations/__init__.py

# media 文件
media/*

clear_migrations.sh


# profiling 文件
profiling/*

.promptx/


api_monitoring/
clear_monitoring_data.py
setup_api_monitoring.py
test_cleanup.py
test_visitor_detail.py
db copy.sqlite3

logs/
logs/*

db_副本.sqlite3